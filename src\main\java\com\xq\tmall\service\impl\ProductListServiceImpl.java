package com.xq.tmall.service.impl;

import com.xq.tmall.dto.ProductListDTO;
import com.xq.tmall.entity.*;
import com.xq.tmall.service.*;
import com.xq.tmall.util.OrderUtil;
import com.xq.tmall.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 商品列表服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductListServiceImpl implements ProductListService {
    
    private final ProductService productService;
    private final ProductImageService productImageService;
    private final CategoryService categoryService;
    private final ReviewService reviewService;
    private final ProductOrderItemService productOrderItemService;
    
    @Override
    public ProductListDTO getProductsByCategory(Integer categoryId, PageUtil pageUtil, String orderBy, Boolean isDesc) {
        try {
            // 构建查询条件
            Product product = new Product();
            Category category = new Category();
            category.setCategory_id(categoryId);
            product.setProduct_category(category);
            
            // 构建排序条件
            OrderUtil orderUtil = buildOrderUtil(orderBy, isDesc);
            
            // 查询商品列表
            List<Product> productList = productService.getList(product, new Byte[]{0, 2}, orderUtil, pageUtil);
            
            // 查询商品总数
            Integer productCount = productService.getTotal(product, new Byte[]{0, 2});

            // 设置分页信息的总数
            pageUtil.setTotal(productCount);

            // 设置商品附加信息
            if (!CollectionUtils.isEmpty(productList)) {
                setProductImages(productList);
                setProductStats(productList);
            }

            // 获取分类列表
            List<Category> categoryList = categoryService.getList(null, new PageUtil(0, 5));

            return ProductListDTO.success(productList, categoryList, pageUtil, productCount,
                                        null, 1, orderBy, isDesc);
            
        } catch (Exception e) {
            log.error("根据分类获取商品列表失败，分类ID: {}", categoryId, e);
            return ProductListDTO.error("获取商品列表失败");
        }
    }
    
    @Override
    public ProductListDTO searchProductsByName(String productName, PageUtil pageUtil, String orderBy, Boolean isDesc) {
        try {
            // 构建查询条件
            Product product = new Product();
            product.setProduct_name(productName);
            
            // 构建排序条件
            OrderUtil orderUtil = buildOrderUtil(orderBy, isDesc);
            
            // 查询商品列表
            List<Product> productList = productService.getList(product, new Byte[]{0, 2}, orderUtil, pageUtil);
            
            // 查询商品总数
            Integer productCount = productService.getTotal(product, new Byte[]{0, 2});

            // 设置分页信息的总数
            pageUtil.setTotal(productCount);

            // 设置商品附加信息
            if (!CollectionUtils.isEmpty(productList)) {
                setProductImages(productList);
                setProductStats(productList);
            }

            // 获取分类列表
            List<Category> categoryList = categoryService.getList(null, new PageUtil(0, 5));

            return ProductListDTO.success(productList, categoryList, pageUtil, productCount,
                                        productName, 2, orderBy, isDesc);
            
        } catch (Exception e) {
            log.error("根据名称搜索商品列表失败，商品名称: {}", productName, e);
            return ProductListDTO.error("搜索商品失败");
        }
    }
    
    @Override
    public ProductListDTO getProductList(Integer categoryId, String productName, PageUtil pageUtil, 
                                       String orderBy, Boolean isDesc) {
        // 参数验证
        if (!isValidSearchParams(categoryId, productName)) {
            return ProductListDTO.error("搜索参数无效");
        }
        
        // 根据参数类型选择相应的查询方法
        if (categoryId != null) {
            return getProductsByCategory(categoryId, pageUtil, orderBy, isDesc);
        } else {
            return searchProductsByName(productName, pageUtil, orderBy, isDesc);
        }
    }
    
    @Override
    public void setProductImages(List<Product> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        
        try {
            for (Product product : productList) {
                // 获取商品的单张预览图片
                List<ProductImage> singleProductImageList = productImageService.getList(
                    product.getProduct_id(), (byte) 0, new PageUtil(0, 5));
                product.setSingleProductImageList(singleProductImageList);
            }
        } catch (Exception e) {
            log.error("设置商品图片信息失败", e);
        }
    }
    
    @Override
    public void setProductStats(List<Product> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        
        try {
            for (Product product : productList) {
                // 设置销量
                Integer saleCount = productOrderItemService.getSaleCountByProductId(product.getProduct_id());
                product.setProduct_sale_count(saleCount);
                
                // 设置评论数
                Integer reviewCount = reviewService.getTotalByProductId(product.getProduct_id());
                product.setProduct_review_count(reviewCount);
            }
        } catch (Exception e) {
            log.error("设置商品统计信息失败", e);
        }
    }
    
    @Override
    public boolean isValidSearchParams(Integer categoryId, String productName) {
        // 至少要有一个搜索条件
        if (categoryId == null && (productName == null || productName.trim().isEmpty())) {
            return false;
        }
        
        // 商品名称不能为空字符串
        if (productName != null && productName.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 构建排序条件
     */
    private OrderUtil buildOrderUtil(String orderBy, Boolean isDesc) {
        // 默认排序
        if (!StringUtils.hasText(orderBy)) {
            return new OrderUtil("product_name", false);
        }
        
        // 验证排序字段
        String validOrderBy = validateOrderBy(orderBy);
        
        // 默认降序为true
        boolean desc = isDesc != null ? isDesc : true;
        
        return new OrderUtil(validOrderBy, desc);
    }
    
    /**
     * 验证排序字段
     */
    private String validateOrderBy(String orderBy) {
        switch (orderBy) {
            case "product_name":
            case "product_create_date":
            case "product_sale_count":
            case "product_sale_price":
                return orderBy;
            default:
                log.warn("无效的排序字段: {}, 使用默认排序", orderBy);
                return "product_name";
        }
    }
}
