html,body,#div_background{
    width: 100%;
    height: 100%;
}
#div_background{
    position: relative;
    padding: 40px;
    min-width: 1000px;
    min-height: 650px;
    background-repeat: no-repeat;
    background-size: cover;
}
#div_background>#div_nav{
    width: 100%;
    height: 30px;
}
#div_nav>#txt_date,#div_nav>#txt_peel{
    padding-left: 10px;
    color: rgba(255,255,255,0.8);
    line-height: 30px;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
#txt_peel:hover{
    cursor: pointer;
    text-decoration: underline;
}
#div_nav>#div_peelPanel{
    width: 100%;
    padding: 20px;
    margin-bottom: 0;
    display: none;
    background-color: rgba(0,0,0,0.4);
    border-radius: 0 0 5px 5px;
    position: absolute;
    z-index: 100;
}
#div_peelPanel>li{
    display: inline-block;
    padding: 10px;
    list-style: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
#div_peelPanel>li>img{
    width: 128px;
    height: 80px;
    cursor: pointer;
    border-radius: 0 0 5px 5px;
    transition: box-shadow ease-in-out .15s;
}
#div_peelPanel>li>img:hover{
    box-shadow: 0 0 8px rgba(102,175,255,.8);
}

#div_background>#div_main{
    position: absolute;
    top: 0;bottom: 0;left: 0;right: 0;
    margin: auto;
    width: 360px;
    height: 550px;
}
#div_main>#div_head{
    width: 100%;
    height: 100px;
}
#div_head>p{
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    margin: 0;
    line-height: 100px;
    height: 100%;
    text-align: center;
    font-size: 30px;
    color: white;
    font-family: "Trebuchet MS", cursive;
}
#div_head>p>span{
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: relative;
    bottom: 2px;
    font-weight: bold;
    font-size: 26px;
    font-family: "幼圆", "Microsoft YaHei UI", serif;
}
#div_content {
    width: 300px; /*减小表单宽度*/
    margin: 0 auto; /*水平居中*/
    padding: 40px; /*增加内边距*/
}
#div_main>#div_content{
    width: 100%;
    padding: 50px;
    background-color: rgba(239 234 234 / 50%);
    border-radius: 85px;
}
#div_content #img_profile_picture{
    width: 84px;
    height: 84px;
    border-radius: 84px;
    display: block;
    margin: 0 auto 40px;
}
.form_control {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border-radius: 5px;
}

#btn_login {
    background-color: #2896f5;
    color: white;
    font-size: 16px;
    border: none;
    cursor: pointer;
}

#btn_login:hover {
    background-color: #72b5ef;
}
#div_content .form_control{
    height: 38px;
    margin: 20px 0;
    background-color: white;
    border: 1px solid #9F9FA1;
}
#div_content .form_control:focus{
    border: 1px solid #cccccc;
}
#div_content #txt_error_msg{
    display: inline-block;
    min-width: 10px;
    min-height: 20px;
    float: right;
    position: relative;
    color: #c33;
    opacity: 0;
    left: 20px;
    bottom: 10px;
    margin-top: 20px;
}
#div_content #btn_login{
    width: 100%;
    height: 38px;
    display: block;
    outline: none;
}

.form_control::-webkit-input-placeholder{
    color:#444;
}
.form_control::-moz-placeholder{   /* Mozilla Firefox 19+ */
    color:#444;
}
.form_control:-moz-placeholder{    /* Mozilla Firefox 4 to 18 */
    color:#444;
}
.form_control:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color:#444;
}
#txt_error_msg {
    font-size: 14px;
    color: #c33;
    display: block;
    margin-top: 10px;
}