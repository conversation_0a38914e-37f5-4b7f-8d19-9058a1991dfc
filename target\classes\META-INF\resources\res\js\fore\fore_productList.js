/**
 * 商品列表页面JavaScript - 使用AJAX加载数据
 */
$(document).ready(function() {
    // 初始化页面
    ProductListPage.init();
});

const ProductListPage = {
    // 当前搜索参数
    currentParams: {
        category_id: null,
        product_name: null,
        page: 0,
        size: 10,
        orderBy: null,
        isDesc: false
    },

    // 当前数据
    currentData: null,

    // 初始化
    init: function() {
        this.parseUrlParams();
        this.bindEvents();
        this.loadProductList();
    },

    // 解析URL参数
    parseUrlParams: function() {
        // 优先使用服务器传递的参数
        if (window.searchParams) {
            this.currentParams.category_id = window.searchParams.category_id !== '' ? parseInt(window.searchParams.category_id) : null;
            this.currentParams.product_name = window.searchParams.product_name !== '' ? window.searchParams.product_name : null;
            this.currentParams.page = window.searchParams.page || 0;
            this.currentParams.size = window.searchParams.size || 20;
        }

        // 解析URL参数作为备用
        const urlParams = new URLSearchParams(window.location.search);
        if (!this.currentParams.category_id && urlParams.get('category_id')) {
            this.currentParams.category_id = parseInt(urlParams.get('category_id'));
        }
        if (!this.currentParams.product_name && urlParams.get('product_name')) {
            this.currentParams.product_name = urlParams.get('product_name');
        }
        this.currentParams.orderBy = urlParams.get('orderBy') || null;
        this.currentParams.isDesc = urlParams.get('isDesc') === 'true';

        // 设置搜索框的值
        if (this.currentParams.product_name) {
            $('.searchInput').val(this.currentParams.product_name);
        }
    },

    // 绑定事件
    bindEvents: function() {
        const self = this;

        // 搜索表单提交
        $('#searchForm').on('submit', function(e) {
            e.preventDefault();
            const productName = $('.searchInput').val().trim();
            if (productName === '') {
                alert('请输入关键字！');
                return;
            }
            self.search(null, productName);
        });

        // 重试搜索表单提交
        $('#retrySearchForm').on('submit', function(e) {
            e.preventDefault();
            const productName = $(this).find('.errorInput').val().trim();
            if (productName === '') {
                alert('请输入关键字！');
                return;
            }
            self.search(null, productName);
        });

        // 排序菜单点击
        $('#sortMenu').on('click', 'li', function() {
            const orderBy = $(this).attr('data-name');
            let isDesc = true;

            if (orderBy === 'product_sale_price') {
                if ($(this).children(".orderByDesc").hasClass("orderBySelect")) {
                    isDesc = false;
                }
            }

            self.sort(orderBy, isDesc);
        });
    },

    // 加载商品列表
    loadProductList: function() {
        const self = this;

        // 显示加载中
        $('#loadingIndicator').show();
        $('#productListContainer').hide();
        $('#noProductsMessage').hide();

        // 构建请求参数
        const params = {};
        if (this.currentParams.category_id) params.category_id = this.currentParams.category_id;
        if (this.currentParams.product_name) params.product_name = this.currentParams.product_name;
        params.page = this.currentParams.page;
        params.size = this.currentParams.size;
        if (this.currentParams.orderBy) params.orderBy = this.currentParams.orderBy;
        params.isDesc = this.currentParams.isDesc;

        // 发送AJAX请求
        $.ajax({
            url: '/tmall/api/product/list',
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(response) {
                $('#loadingIndicator').hide();

                if (response.code === 200 && response.data) {
                    self.currentData = response.data;
                    self.renderProductList(response.data);
                    self.updatePageTitle(response.data);
                } else {
                    self.showNoProductsMessage(response.message || '获取商品列表失败');
                }
            },
            error: function(xhr, status, error) {
                $('#loadingIndicator').hide();
                console.error('加载商品列表失败:', error);
                self.showNoProductsMessage('网络错误，请稍后重试');
            }
        });
    },

    // 渲染商品列表
    renderProductList: function(data) {
        if (!data.productList || data.productList.length === 0) {
            this.showNoProductsMessage();
            return;
        }

        // 渲染分类列表
        this.renderCategoryList(data.categoryList);

        // 渲染商品
        this.renderProducts(data.productList);

        // 渲染分页
        this.renderPagination(data);

        // 更新排序菜单状态
        this.updateSortMenu();

        // 显示商品列表容器
        $('#productListContainer').show();
    },

    // 渲染分类列表
    renderCategoryList: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;

        let html = '';
        const self = this;
        categoryList.forEach(function(category) {
            html += `<li><a href="javascript:void(0)" onclick="ProductListPage.search(${category.category_id}, null)">${category.category_name}</a></li>`;
        });
        $('#categoryList').html(html);
    },

    // 渲染商品
    renderProducts: function(productList) {
        const self = this;
        let html = '';
        productList.forEach(function(product) {
            const imageUrl = product.singleProductImageList && product.singleProductImageList.length > 0
                ? `/tmall/res/images/item/productSinglePicture/${product.singleProductImageList[0].productImage_src}`
                : '/tmall/res/images/item/productSinglePicture/default.jpg';

            const saleCount = product.product_sale_count || 0;
            const reviewCount = product.product_review_count || 0;
            const categoryName = product.product_category ? product.product_category.category_name : '未知分类';

            html += `
                <div class="context_productStyle">
                    <div class="context_product">
                        <a href="/tmall/product/${product.product_id}" target="_blank">
                            <img class="context_product_imgMain" src="${imageUrl}" alt="${product.product_name}"/>
                        </a>
                        <ul class="context_product_imgList">`;

            if (product.singleProductImageList) {
                product.singleProductImageList.forEach(function(img) {
                    html += `<li><img src="/tmall/res/images/item/productSinglePicture/${img.productImage_src}" alt="商品图片"/></li>`;
                });
            }

            html += `
                        </ul>
                        <p class="context_product_price"><span>¥</span>${product.product_sale_price}</p>
                        <p class="context_product_name">
                            <a href="/tmall/product/${product.product_id}" target="_blank">${product.product_name}</a>
                        </p>
                        <p class="context_product_shop"><span>海涛${categoryName}旗舰店</span></p>
                        <p class="context_product_status">
                            <span class="status_left">总成交<em>${saleCount}笔</em></span>
                            <span class="status_middle">评价<em>${reviewCount}</em></span>
                            <span class="status_right">
                                <img src="/tmall/res/images/fore/WebsiteImage/T11lggFoXcXXc1v.nr-93-93.png"/>
                            </span>
                        </p>
                    </div>
                </div>
            `;
        });

        $('#productList').html(html);

        // 绑定商品图片点击事件
        this.bindProductImageEvents();
    },

    // 渲染分页
    renderPagination: function(data) {
        if (!data.pagination) {
            $('#paginationContainer').html('');
            return;
        }

        // 即使只有一页也显示分页信息
        if (data.totalPage <= 1) {
            let html = `
                <div class="pagination-container">
                    <div class="pagination">
                        <div class="pagination-info">
                            <span>共 ${data.productCount} 件商品，第 1 / 1 页</span>
                        </div>
                    </div>
                </div>
                <style>
                .pagination-container {
                    width: 100%;
                    margin: 20px 0;
                }
                .pagination {
                    width: 1230px;
                    margin: 0 auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 15px;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                }
                .pagination-info {
                    color: #666;
                    font-size: 14px;
                }
                </style>
            `;
            $('#paginationContainer').html(html);
            return;
        }

        const pagination = data.pagination;
        let html = `
            <div class="pagination-container">
                <div class="pagination">
                    <div class="pagination-info">
                        <span>共 ${data.productCount} 件商品，第 ${pagination.currentPage + 1} / ${pagination.totalPages} 页</span>
                    </div>
                    <div class="pagination-controls">
        `;

        // 首页
        if (pagination.currentPage > 0) {
            html += `<a href="javascript:void(0)" class="pagination-btn" onclick="ProductListPage.goToPage(0)">首页</a>`;
        }

        // 上一页
        if (pagination.hasPrevious) {
            html += `<a href="javascript:void(0)" class="pagination-btn" onclick="ProductListPage.goToPage(${pagination.currentPage - 1})">上一页</a>`;
        }

        // 页码列表
        html += '<div class="pagination-numbers">';
        if (pagination.pageNumbers) {
            pagination.pageNumbers.forEach(function(pageNum) {
                if (pageNum === pagination.currentPage) {
                    html += `<span class="pagination-current">${pageNum + 1}</span>`;
                } else {
                    html += `<a href="javascript:void(0)" class="pagination-number" onclick="ProductListPage.goToPage(${pageNum})">${pageNum + 1}</a>`;
                }
            });
        }
        html += '</div>';

        // 下一页
        if (pagination.hasNext) {
            html += `<a href="javascript:void(0)" class="pagination-btn" onclick="ProductListPage.goToPage(${pagination.currentPage + 1})">下一页</a>`;
        }

        // 末页
        if (pagination.currentPage < pagination.totalPages - 1) {
            html += `<a href="javascript:void(0)" class="pagination-btn" onclick="ProductListPage.goToPage(${pagination.totalPages - 1})">末页</a>`;
        }

        html += `
                    </div>
                    <div class="pagination-jump">
                        <span>跳转到</span>
                        <input type="number" id="jumpPageInput" min="1" max="${pagination.totalPages}" value="${pagination.currentPage + 1}">
                        <span>页</span>
                        <button onclick="ProductListPage.jumpToPage()" class="pagination-jump-btn">确定</button>
                    </div>
                </div>
            </div>

            <style>
            .pagination-container {
                width: 100%;
                margin: 20px 0;
            }

            .pagination {
                width: 1230px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 15px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .pagination-info {
                color: #666;
                font-size: 14px;
            }

            .pagination-controls {
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .pagination-btn, .pagination-number {
                display: inline-block;
                padding: 8px 12px;
                margin: 0 2px;
                text-decoration: none;
                color: #007bff;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
                transition: all 0.2s;
                cursor: pointer;
            }

            .pagination-btn:hover, .pagination-number:hover {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }

            .pagination-current {
                display: inline-block;
                padding: 8px 12px;
                margin: 0 2px;
                background: #007bff;
                color: white;
                border: 1px solid #007bff;
                border-radius: 4px;
                font-weight: bold;
            }

            .pagination-numbers {
                display: flex;
                align-items: center;
                gap: 2px;
            }

            .pagination-jump {
                display: flex;
                align-items: center;
                gap: 5px;
                color: #666;
                font-size: 14px;
            }

            .pagination-jump input {
                width: 60px;
                padding: 6px 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
            }

            .pagination-jump-btn {
                padding: 6px 12px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                transition: background 0.2s;
            }

            .pagination-jump-btn:hover {
                background: #0056b3;
            }

            @media (max-width: 768px) {
                .pagination {
                    flex-direction: column;
                    gap: 10px;
                }

                .pagination-controls {
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .pagination-jump {
                    font-size: 12px;
                }

                .pagination-jump input {
                    width: 50px;
                }
            }
            </style>
        `;

        $('#paginationContainer').html(html);

        // 绑定回车键跳转事件
        $('#jumpPageInput').on('keypress', function(e) {
            if (e.key === 'Enter') {
                ProductListPage.jumpToPage();
            }
        });
    },

    // 更新排序菜单状态
    updateSortMenu: function() {
        $('#sortMenu li').removeClass('orderBySelect');
        $('#sortMenu li .orderByAsc, #sortMenu li .orderByDesc').removeClass('orderBySelect');

        if (this.currentParams.orderBy) {
            const menuItem = $(`#sortMenu li[data-name="${this.currentParams.orderBy}"]`);
            menuItem.addClass('orderBySelect');

            if (this.currentParams.orderBy === 'product_sale_price') {
                if (this.currentParams.isDesc) {
                    menuItem.find('.orderByDesc').addClass('orderBySelect');
                } else {
                    menuItem.find('.orderByAsc').addClass('orderBySelect');
                }
            }
        } else {
            // 默认选中综合排序
            $('#sortMenu li[data-name="product_name"]').addClass('orderBySelect');
        }
    },

    // 绑定商品图片点击事件
    bindProductImageEvents: function() {
        $(".context_product_imgList li").off('click').on('click', function() {
            const url = $(this).children("img").attr("src");
            if (url) {
                $(this).parent("ul").prev("a").children(".context_product_imgMain").attr("src", url);
            }
        });
    },

    // 显示无商品消息
    showNoProductsMessage: function(message) {
        if (message) {
            $('#noProductsTitle').text(message);
        } else {
            const searchValue = this.currentParams.product_name || '';
            $('#noProductsTitle').text(`喵~没找到与"${searchValue}"相关的商品哦，要不您换个关键词我帮您再找找看`);
        }

        // 设置重试搜索框的值
        $('#retrySearchForm .errorInput').val(this.currentParams.product_name || '');

        $('#noProductsMessage').show();
    },

    // 更新页面标题
    updatePageTitle: function(data) {
        let title = '商品列表-趣味商城-理想生活上趣味商城';

        if (this.currentParams.product_name) {
            title = `${this.currentParams.product_name}-趣味商城-理想生活上趣味商城`;
        } else if (data.productList && data.productList.length > 0 && data.productList[0].product_category) {
            title = `${data.productList[0].product_category.category_name}-趣味商城-理想生活上趣味商城`;
        }

        document.title = title;
        $('#pageTitle').text(title);
    },

    // 搜索
    search: function(categoryId, productName) {
        this.currentParams.category_id = categoryId;
        this.currentParams.product_name = productName;
        this.currentParams.page = 0; // 重置到第一页
        this.updateUrl();
        this.loadProductList();
    },

    // 排序
    sort: function(orderBy, isDesc) {
        this.currentParams.orderBy = orderBy;
        this.currentParams.isDesc = isDesc;
        this.currentParams.page = 0; // 重置到第一页
        this.updateUrl();
        this.loadProductList();
    },

    // 跳转到指定页
    goToPage: function(page) {
        this.currentParams.page = page;
        this.updateUrl();
        this.loadProductList();
    },

    // 跳转页面输入框
    jumpToPage: function() {
        const input = document.getElementById('jumpPageInput');
        const page = parseInt(input.value) - 1; // 转换为0基索引
        const maxPage = this.currentData.pagination.totalPages - 1;

        if (isNaN(page) || page < 0 || page > maxPage) {
            alert(`请输入有效的页码（1-${this.currentData.pagination.totalPages}）`);
            return;
        }

        this.goToPage(page);
    },

    // 更新URL
    updateUrl: function() {
        const params = new URLSearchParams();

        if (this.currentParams.category_id) {
            params.set('category_id', this.currentParams.category_id);
        }
        if (this.currentParams.product_name) {
            params.set('product_name', this.currentParams.product_name);
        }
        if (this.currentParams.page > 0) {
            params.set('page', this.currentParams.page);
        }
        if (this.currentParams.size !== 10) {
            params.set('size', this.currentParams.size);
        }
        if (this.currentParams.orderBy) {
            params.set('orderBy', this.currentParams.orderBy);
        }
        if (this.currentParams.isDesc) {
            params.set('isDesc', this.currentParams.isDesc);
        }

        const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        window.history.pushState({}, '', newUrl);
    }
};