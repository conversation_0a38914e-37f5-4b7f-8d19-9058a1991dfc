<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.PropertyMapper">
    <resultMap id="propertyMap" type="com.xq.tmall.entity.Property">
        <id property="property_id" column="property_id"/>
        <result property="property_name" column="property_name"/>
        <association property="property_category" javaType="com.xq.tmall.entity.Category">
            <id property="category_id" column="property_category_id"/>
            <id property="category_name" column="category_name"/>
        </association>
    </resultMap>

    <insert id="insertOne" parameterType="com.xq.tmall.entity.Property">
        INSERT property (property_name,property_category_id)
            VALUES(#{property.property_name},
            #{property.property_category.category_id})
    </insert>
    <insert id="insertList" parameterType="list">
        INSERT property (property_name,property_category_id)
        VALUES
        <foreach collection="property_list" index="index" item="property" separator=",">
            (#{property.property_name},
            #{property.property_category_id})
        </foreach>
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.Property">
        UPDATE property
        <set>
            <if test="property.property_name != null">property_name = #{property.property_name},</if>
            <if test="property.property_category.category_id != null">property_category_id = #{property.property_category.category_id},</if>
        </set>
        <where>
            property_id = #{property.property_id}
        </where>
    </update>
    <delete id="deleteList" parameterType="java.util.ArrayList">
        DELETE FROM property
        <where>
            property_id IN
            <foreach collection="property_id_list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete>
    <delete id="delete" parameterType="java.lang.Integer">
        DELETE FROM property where property_id = #{propertyId}
    </delete>
    <select id="selectPropertyList" resultMap="propertyMap">
        SELECT pr.property_id,pr.property_name,pr.property_category_id,ca.category_name FROM property pr
        left join category ca on pr.property_category_id=ca.category_id
        <if test="property != null">
            <where>
                <if test="property.property_name != null">and pr.property_name LIKE concat('%',#{property.property_name},'%')</if>
                <if test="property.property_category != null">
                    <if test="property.property_category.category_id != null and property.property_category.category_id!=0">and pr.property_category_id = #{property.property_category.category_id}</if>
                </if>
            </where>
        </if>
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectOne" resultMap="propertyMap" parameterType="java.lang.Integer">
        SELECT pr.property_id,pr.property_name,pr.property_category_id,ca.category_name FROM property pr
        left join category ca on pr.property_category_id=ca.category_id
        <where>
            pr.property_id = #{property_id}
        </where>
    </select>
    <select id="selectTotal" resultType="java.lang.Integer" parameterType="com.xq.tmall.entity.Property">
        SELECT COUNT(*) FROM property
        <if test="property != null">
            <where>
                <if test="property.property_name != null">and property_name LIKE concat('%',#{property.property_name},'%')</if>
                <if test="property.property_category != null">
                    <if test="property.property_category.category_id != null and property.property_category.category_id!=0">and property_category_id = #{property.property_category.category_id}</if>
                </if>
            </where>
        </if>
    </select>
</mapper>