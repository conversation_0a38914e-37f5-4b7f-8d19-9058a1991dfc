<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="335f8ffe-5004-4a83-9b6a-eb24d6df5c3a" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/BaseController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/BaseController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AccountController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AccountController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AdminHomeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AdminHomeController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AdminLoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/AdminLoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/CategoryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/CategoryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/OrderController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/OrderController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/ProductController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/ProductController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/PropertyController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/PropertyController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/ReviewController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/ReviewController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/UserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/admin/UserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeHomeController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeHomeController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeLoginController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeLoginController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeOrderController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeOrderController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeProductDetailsController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeProductDetailsController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeProductListController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeProductListController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeRegisterController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeRegisterController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeReviewController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeReviewController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeUserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/controller/fore/ForeUserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/util/Constants.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/util/Constants.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/xq/tmall/util/PageUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/xq/tmall/util/PageUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/META-INF/spring-devtools.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/META-INF/MANIFEST.MF" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/homePage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/homePage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/include/navigator.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/include/navigator.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/loginPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/loginPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/orderListPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/orderListPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productBuyCarPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productBuyCarPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productBuyPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productBuyPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productDetailsPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productDetailsPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productListPage.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/productListPage.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/register.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/register.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/userDetails.jsp" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/WEB-INF/page/fore/userDetails.jsp" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_login.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_login.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_nav.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_nav.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_productDetails.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_productDetails.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_productList.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/css/fore/fore_productList.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_login.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_productBuyCar.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_productBuyCar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_productList.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_productList.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_register.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/webapp/res/js/fore/fore_register.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\SDK\maven\current" />
        <option name="localRepository" value="D:\SDK\maven\localRepository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="useMavenConfig" value="false" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2y2V6zFn6Pfq1EsFxLNhAdZrCfh" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.funNetwork [clean].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.TmallApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Downloads/tmall-demo&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="TmallApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="funNetwork" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xq.tmall.TmallApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="335f8ffe-5004-4a83-9b6a-eb24d6df5c3a" name="更改" comment="" />
      <created>1749032484320</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749032484320</updated>
      <workItem from="1749032485716" duration="408000" />
      <workItem from="1749087522970" duration="4313000" />
      <workItem from="1749093484905" duration="1139000" />
      <workItem from="1749094640835" duration="2000" />
      <workItem from="1749097374173" duration="5344000" />
      <workItem from="1749102730496" duration="10063000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>