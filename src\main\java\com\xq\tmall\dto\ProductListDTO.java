package com.xq.tmall.dto;

import com.xq.tmall.entity.Category;
import com.xq.tmall.entity.Product;
import com.xq.tmall.util.PageUtil;
import lombok.Data;

import java.util.List;

/**
 * 商品列表页面数据传输对象
 */
@Data
public class ProductListDTO {
    
    /**
     * 商品列表
     */
    private List<Product> productList;
    
    /**
     * 分类列表
     */
    private List<Category> categoryList;
    
    /**
     * 分页信息
     */
    private PageUtil pageUtil;
    
    /**
     * 商品总数
     */
    private Integer productCount;
    
    /**
     * 总页数
     */
    private Integer totalPage;
    
    /**
     * 搜索关键词
     */
    private String searchValue;
    
    /**
     * 搜索类型（1:分类搜索, 2:名称搜索）
     */
    private Integer searchType;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 是否降序
     */
    private Boolean isDesc;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页显示数量
     */
    private Integer pageSize;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 分页导航信息（用于前端）
     */
    private PaginationInfo pagination;
    
    /**
     * 分页导航信息
     */
    @Data
    public static class PaginationInfo {
        /**
         * 当前页码
         */
        private Integer currentPage;
        
        /**
         * 总页数
         */
        private Integer totalPages;
        
        /**
         * 每页显示数量
         */
        private Integer pageSize;
        
        /**
         * 总记录数
         */
        private Integer totalCount;
        
        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;
        
        /**
         * 是否有下一页
         */
        private Boolean hasNext;
        
        /**
         * 页码列表（用于显示页码导航）
         */
        private List<Integer> pageNumbers;
        
        /**
         * 开始页码
         */
        private Integer startPage;
        
        /**
         * 结束页码
         */
        private Integer endPage;
    }
    
    /**
     * 创建成功的结果
     */
    public static ProductListDTO success(List<Product> productList, List<Category> categoryList, 
                                       PageUtil pageUtil, Integer productCount, String searchValue, 
                                       Integer searchType, String orderBy, Boolean isDesc) {
        ProductListDTO dto = new ProductListDTO();
        dto.setProductList(productList);
        dto.setCategoryList(categoryList);
        dto.setPageUtil(pageUtil);
        dto.setProductCount(productCount);
        dto.setTotalPage(pageUtil.getTotalPage());
        dto.setSearchValue(searchValue);
        dto.setSearchType(searchType);
        dto.setOrderBy(orderBy);
        dto.setIsDesc(isDesc);
        dto.setCurrentPage(pageUtil.getPageStart() / pageUtil.getCount());
        dto.setPageSize(pageUtil.getCount());
        dto.setHasPrevious(dto.getCurrentPage() > 0);
        dto.setHasNext(dto.getCurrentPage() < dto.getTotalPage() - 1);
        return dto;
    }
    
    /**
     * 创建失败的结果
     */
    public static ProductListDTO error(String errorMessage) {
        ProductListDTO dto = new ProductListDTO();
        dto.setErrorMessage(errorMessage);
        return dto;
    }
    
    /**
     * 获取分页导航信息
     */
    public PaginationInfo getPaginationInfo() {
        PaginationInfo info = new PaginationInfo();
        info.setCurrentPage(this.currentPage);
        info.setTotalPages(this.totalPage);
        info.setPageSize(this.pageSize);
        info.setTotalCount(this.productCount);
        info.setHasPrevious(this.hasPrevious);
        info.setHasNext(this.hasNext);
        
        // 计算显示的页码范围（显示当前页前后各2页）
        int displayRange = 2;
        int startPage = Math.max(0, this.currentPage - displayRange);
        int endPage = Math.min(this.totalPage - 1, this.currentPage + displayRange);
        
        info.setStartPage(startPage);
        info.setEndPage(endPage);
        
        // 生成页码列表
        List<Integer> pageNumbers = new java.util.ArrayList<>();
        for (int i = startPage; i <= endPage; i++) {
            pageNumbers.add(i);
        }
        info.setPageNumbers(pageNumbers);
        
        return info;
    }
}
