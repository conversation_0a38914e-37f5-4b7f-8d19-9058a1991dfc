/**
 * 注册页面对象
 */
const RegisterPage = {
    // 初始化
    init: function() {
        this.loadAddressData();
        this.bindAddressEvents();
    },

    // 加载地址数据
    loadAddressData: function() {
        const self = this;

        $.ajax({
            url: "/tmall/api/register/addresses",
            type: "GET",
            dataType: "json",
            success: function(response) {
                if (response.code === 200) {
                    self.renderAddressSelectors(response.data);
                } else {
                    console.error("获取地址数据失败:", response.message);
                    alert("获取地址数据失败，请刷新页面重试");
                }
            },
            error: function(xhr, status, error) {
                console.error("获取地址数据失败:", error);
                alert("网络错误，请刷新页面重试");
            }
        });
    },

    // 渲染地址选择器
    renderAddressSelectors: function(data) {
        // 渲染省份选择器
        this.renderProvinceSelector(data.addressList, data.defaultAddressId);
        // 渲染城市选择器
        this.renderCitySelector(data.cityList, data.defaultCityAddressId);
        // 渲染区县选择器
        this.renderDistrictSelector(data.districtList, data.defaultDistrictAddressId);
    },

    // 渲染省份选择器
    renderProvinceSelector: function(addressList, defaultId) {
        const $selector = $("#select_user_address_province");
        $selector.empty();
        $selector.append("<option value=''>请选择省份</option>");

        if (addressList && addressList.length > 0) {
            addressList.forEach(function(address) {
                const selected = address.address_areaId === defaultId ? 'selected' : '';
                $selector.append(`<option value="${address.address_areaId}" ${selected}>${address.address_name}</option>`);
            });
        }

        $selector.selectpicker('refresh');
    },

    // 渲染城市选择器
    renderCitySelector: function(cityList, defaultId) {
        const $selector = $("#select_user_address_city");
        $selector.empty();
        $selector.append("<option value=''>请选择城市</option>");

        if (cityList && cityList.length > 0) {
            cityList.forEach(function(address) {
                const selected = address.address_areaId === defaultId ? 'selected' : '';
                $selector.append(`<option value="${address.address_areaId}" ${selected}>${address.address_name}</option>`);
            });
        }

        $selector.selectpicker('refresh');
    },

    // 渲染区县选择器
    renderDistrictSelector: function(districtList, defaultId) {
        const $selector = $("#select_user_address_district");
        $selector.empty();
        $selector.append("<option value=''>请选择区县</option>");

        if (districtList && districtList.length > 0) {
            districtList.forEach(function(address) {
                const selected = address.address_areaId === defaultId ? 'selected' : '';
                $selector.append(`<option value="${address.address_areaId}" ${selected}>${address.address_name}</option>`);
            });
        }

        $selector.selectpicker('refresh');
    },

    // 绑定地址选择器事件
    bindAddressEvents: function() {
        const self = this;

        // 省份选择器改变事件
        $("#select_user_address_province").change(function () {
            const addressId = $(this).val();
            if (addressId) {
                self.loadCityData(addressId);
            } else {
                self.clearCityAndDistrict();
            }
        });

        // 城市选择器改变事件
        $("#select_user_address_city").change(function () {
            const addressId = $(this).val();
            if (addressId) {
                self.loadDistrictData(addressId);
            } else {
                self.clearDistrict();
            }
        });
    },

    // 加载城市数据
    loadCityData: function(provinceId) {
        const self = this;

        $.ajax({
            url: "/tmall/api/register/addresses/" + provinceId,
            type: "GET",
            dataType: "json",
            success: function (response) {
                if (response.code === 200) {
                    self.renderCitySelector(response.data);
                    self.clearDistrict();
                } else {
                    alert("获取城市信息失败！");
                }
            },
            error: function () {
                alert("获取城市信息失败！");
            }
        });
    },

    // 加载区县数据
    loadDistrictData: function(cityId) {
        const self = this;

        $.ajax({
            url: "/tmall/api/register/addresses/" + cityId,
            type: "GET",
            dataType: "json",
            success: function (response) {
                if (response.code === 200) {
                    self.renderDistrictSelector(response.data);
                } else {
                    alert("获取区县信息失败！");
                }
            },
            error: function () {
                alert("获取区县信息失败！");
            }
        });
    },

    // 清空城市和区县选择器
    clearCityAndDistrict: function() {
        $("#select_user_address_city").empty().append("<option value=''>请选择城市</option>").selectpicker('refresh');
        this.clearDistrict();
    },

    // 清空区县选择器
    clearDistrict: function() {
        $("#select_user_address_district").empty().append("<option value=''>请选择区县</option>").selectpicker('refresh');
    }
};

$(function () {
    // 地址选择器事件已移到RegisterPage对象中处理

    //用户名input获取光标
    $("#user_name").focus(function () {
        $(this).css("border", "1px solid #3879D9")
            .next().text("请输入用户名").css("display", "inline-block").css("color", "#00A0E9");
    });
    //密码input获取光标
    $("#user_password").focus(function () {
        $(this).css("border", "1px solid #3879D9")
            .next().text("请输入密码").css("display", "inline-block").css("color", "#00A0E9");
    });
    //再次输入密码input获取光标
    $("#user_password_one").focus(function () {
        $(this).css("border", "1px solid #3879D9")
            .next().text("请再次输入密码").css("display", "inline-block").css("color", "#00A0E9");
    });
    //昵称input获取光标
    $("#user_nickname").focus(function () {
        $(this).css("border", "1px solid #3879D9")
            .next().text("请输入昵称").css("display", "inline-block").css("color", "#00A0E9");
    });
    //出生日期input获取光标
    $("#user_birthday").focus(function () {
        $(this).css("border", "1px solid #3879D9")
            .next().text("请输入出生日期").css("display", "inline-block").css("color", "#00A0E9");
    });

    //input离开光标
    $(".form-text").blur(function () {
        $(this).css("border-color", "#cccccc")
            .next().css("display", "none");
    });

    //非空验证
    $("#register_sub").click(function () {
        //用户名
        const user_name = $.trim($("input[name=user_name]").val());
        //密码
        const user_password = $.trim($("input[name=user_password]").val());
        //确认密码
        const user_password_one = $.trim($("input[name=user_password_one]").val());
        //昵称
        const user_nickname = $.trim($("input[name=user_nickname]").val());
        //出生日期
        const user_birthday = $.trim($("input[name=user_birthday]").val());

        //验证密码的格式 包含数字和英文字母
        const reg = new RegExp(/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/);
        if (user_name == null || user_name === "") {
            $("#user_name").css("border", "1px solid red")
                .next().text("请输入用户名").css("display", "inline-block").css("color", "red");
            return false;
        } else if (user_password == null || user_password === "") {
            $("#user_password").css("border", "1px solid red")
                .next().text("请输入密码").css("display", "inline-block").css("color", "red");
            return false;
        } else if (user_password_one == null || user_password_one === "") {
            $("#user_password_one").css("border", "1px solid red")
                .next().text("请重复输入密码").css("display", "inline-block").css("color", "red");
            return false;
        }else if(!reg.test(user_password)){
            $("#user_password").css("border", "1px solid red")
                .next().text("密码格式必须包含数字和字母").css("display", "inline-block").css("color", "red");
            return false;
        } else if (user_password !== user_password_one) {
            $("#user_password_one").css("border", "1px solid red")
                .next().text("两次输入密码不相同").css("display", "inline-block").css("color", "red");
            return false;
        } else if (user_nickname == null || user_nickname === "") {
            $("#user_nickname").css("border", "1px solid red")
                .next().text("请输入昵称").css("display", "inline-block").css("color", "red");
            return false;
        } else if (user_birthday == null || user_birthday === "") {
            $("#user_birthday").css("border", "1px solid red")
                .next().text("请选择出生日期").css("display", "inline-block").css("color", "red");
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/tmall/register/doRegister",
            data: {
                "user_name": user_name,
                "user_password": user_password,
                "user_nickname": user_nickname,
                "user_birthday": user_birthday,
                "user_gender": $("input[name=user_gender]:checked").val(),
                "user_address": $("#select_user_address_district").val()
            },
            dataType: "json",
            success: function (data) {
                if (data.success) {
                    $(".msg").stop(true, true).animate({
                        opacity: 1
                    }, 550, function () {
                        $(".msg").animate({
                            opacity: 0
                        }, 1500, function () {
                            location.href = "/tmall/login";
                        });
                    });
                } else {
                    $("#user_name").css("border", "1px solid red")
                        .next().text(data.msg).css("display", "inline-block").css("color", "red");
                }
            },
            error: function (data) {
                location.reload(true);
            },
            beforeSend: function () {
            }
        });
    });
});

