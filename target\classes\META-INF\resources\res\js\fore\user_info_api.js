/**
 * 用户信息页面API数据处理模块
 */
var UserInfoAPI = {
    
    // API基础路径
    baseUrl: '',
    
    // 当前用户信息
    currentUser: null,
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.bindEvents();

        // 延迟加载用户信息，避免与导航栏API调用冲突
        var self = this;
        setTimeout(function() {
            self.loadUserInfo();
        }, 500);
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 绑定事件
    bindEvents: function() {
        var self = this;
        
        // 绑定表单提交事件
        $("#register_form").off('submit').on('submit', function(e) {
            e.preventDefault();
            self.handleUpdateUserInfo();
            return false;
        });
        
        // 绑定头像上传事件
        $("#user_profile_picture_src").off('change').on('change', function() {
            self.handleAvatarUpload(this);
        });
        
        // 绑定地址选择事件
        $('#select_user_address_province').off('change').on('change', function() {
            self.loadCityList($(this).val());
        });
        
        $('#select_user_address_city').off('change').on('change', function() {
            self.loadDistrictList($(this).val());
        });
        
        // 绑定输入框焦点事件
        $(".form-text").off('focus').on('focus', function() {
            $(this).css("border", "1px solid #3879D9")
                .next().css("display", "none");
        });
        
        $(".form-text").off('blur').on('blur', function() {
            $(this).css("border-color", "#cccccc");
        });
    },
    
    // 加载用户信息
    loadUserInfo: function() {
        var self = this;
        var token = self.getToken();

        // 调试信息
        console.log('Token from storage:', token);

        if (!token) {
            self.showError('未找到登录凭证，请重新登录');
            window.location.href = self.baseUrl + '/login';
            return;
        }

        $.ajax({
            url: self.baseUrl + '/api/user/info',
            type: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            dataType: 'json',
            success: function(response) {
                console.log('API Response:', response);
                if (response.code === 200) {
                    self.currentUser = response.data.user;
                    self.populateUserForm(response.data);
                } else {
                    self.showError('获取用户信息失败：' + response.message);
                    if (response.code === 401) {
                        // 未登录，跳转到登录页
                        window.location.href = self.baseUrl + '/login';
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('获取用户信息请求失败：', error);
                console.error('Response status:', xhr.status);
                console.error('Response text:', xhr.responseText);
                self.showError('获取用户信息失败，请刷新页面重试');
            }
        });
    },
    
    // 填充用户表单
    populateUserForm: function(data) {
        var user = data.user;
        
        // 填充基本信息
        $("#user_nickname").val(user.user_nickname || '');
        $("#user_realname").val(user.user_realname || '');
        $("#user_birthday").val(user.user_birthday || '');
        
        // 设置性别
        $("input[name='user_gender'][value='" + (user.user_gender || 0) + "']").prop('checked', true);
        
        // 设置头像
        if (user.user_profile_picture_src) {
            $("#header_image").attr("src", this.baseUrl + "/res/images/item/userProfilePicture/" + user.user_profile_picture_src);
            $("#user_profile_picture_src_value").val(user.user_profile_picture_src);
        }
        
        // 填充地址信息
        if (data.addressList) {
            this.populateAddressSelects(data);
        }
        
        // 更新页面显示的用户名
        $("#username_display").text(user.user_name || '');

        // 更新侧边栏头像
        if (user.user_profile_picture_src) {
            $("#sidebar_avatar").attr("src", this.baseUrl + "/res/images/item/userProfilePicture/" + user.user_profile_picture_src);
        }
    },
    
    // 填充地址选择框
    populateAddressSelects: function(data) {
        // 填充省份
        var provinceSelect = $("#select_user_address_province");
        provinceSelect.empty();
        if (data.addressList) {
            data.addressList.forEach(function(address) {
                var selected = data.addressId === address.address_areaId ? 'selected' : '';
                provinceSelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 填充城市
        var citySelect = $("#select_user_address_city");
        citySelect.empty();
        if (data.cityList) {
            data.cityList.forEach(function(address) {
                var selected = data.cityAddressId === address.address_areaId ? 'selected' : '';
                citySelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 填充区县
        var districtSelect = $("#select_user_address_district");
        districtSelect.empty();
        if (data.districtList) {
            data.districtList.forEach(function(address) {
                var selected = data.districtAddressId === address.address_areaId ? 'selected' : '';
                districtSelect.append('<option value="' + address.address_areaId + '" ' + selected + '>' + address.address_name + '</option>');
            });
        }
        
        // 刷新选择器
        $('.selectpicker').selectpicker('refresh');
    },
    
    // 加载城市列表
    loadCityList: function(provinceId) {
        var self = this;
        
        $.ajax({
            url: self.baseUrl + '/address/' + provinceId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var citySelect = $("#select_user_address_city");
                    var districtSelect = $("#select_user_address_district");
                    
                    citySelect.empty();
                    districtSelect.empty();
                    
                    response.addressList.forEach(function(address) {
                        citySelect.append('<option value="' + address.address_areaId + '">' + address.address_name + '</option>');
                    });
                    
                    $('.selectpicker').selectpicker('refresh');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取城市列表失败：', error);
            }
        });
    },
    
    // 加载区县列表
    loadDistrictList: function(cityId) {
        var self = this;
        
        $.ajax({
            url: self.baseUrl + '/address/' + cityId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    var districtSelect = $("#select_user_address_district");
                    districtSelect.empty();
                    
                    response.addressList.forEach(function(address) {
                        districtSelect.append('<option value="' + address.address_areaId + '">' + address.address_name + '</option>');
                    });
                    
                    $('.selectpicker').selectpicker('refresh');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取区县列表失败：', error);
            }
        });
    },
    
    // 处理用户信息更新
    handleUpdateUserInfo: function() {
        var self = this;
        
        // 验证表单
        if (!this.validateForm()) {
            return;
        }
        
        var formData = {
            user_nickname: $("#user_nickname").val().trim(),
            user_realname: $("#user_realname").val().trim(),
            user_gender: $("input[name='user_gender']:checked").val(),
            user_birthday: $("#user_birthday").val(),
            user_address: $("#select_user_address_district").val(),
            user_profile_picture_src: $("#user_profile_picture_src_value").val(),
            user_password: $("#user_password").val().trim()
        };
        
        $.ajax({
            url: self.baseUrl + '/api/user/update',
            type: 'POST',
            headers: {
                'Authorization': 'Bearer ' + self.getToken()
            },
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    self.showSuccess('用户信息更新成功');
                    // 清空密码字段
                    $("#user_password").val('');
                    $("#user_password_one").val('');
                } else {
                    self.showError('更新失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('更新用户信息请求失败：', error);
                self.showError('更新失败，请重试');
            }
        });
    },
    
    // 处理头像上传
    handleAvatarUpload: function(fileInput) {
        var self = this;
        var file = fileInput.files[0];
        
        // 验证文件
        if (!file) {
            return;
        }
        
        var imageType = /^image\//;
        if (!imageType.test(file.type)) {
            self.showError("请选择图片文件！");
            return;
        }
        
        if (file.size > 512000) {
            self.showError("图片大小不能超过500KB！");
            return;
        }
        
        var formData = new FormData();
        formData.append("file", file);
        
        $.ajax({
            url: self.baseUrl + '/api/user/uploadAvatar',
            type: 'POST',
            headers: {
                'Authorization': 'Bearer ' + self.getToken()
            },
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                if (response.code === 200) {
                    $("#header_image").attr("src", self.baseUrl + response.data.filePath);
                    $("#sidebar_avatar").attr("src", self.baseUrl + response.data.filePath);
                    $("#user_profile_picture_src_value").val(response.data.fileName);
                    self.showSuccess("头像上传成功");
                } else {
                    self.showError("头像上传失败：" + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('头像上传请求失败：', error);
                self.showError("头像上传失败，请重试");
            }
        });
        
        // 清空文件输入框
        $(fileInput).val('');
    },
    
    // 表单验证
    validateForm: function() {
        var user_realname = $("#user_realname").val().trim();
        var user_password = $("#user_password").val().trim();
        var user_password_one = $("#user_password_one").val().trim();
        var user_nickname = $("#user_nickname").val().trim();
        var user_birthday = $("#user_birthday").val();
        
        // 验证密码格式 包含数字和英文字母
        var reg = new RegExp(/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/);
        
        if (!user_realname) {
            this.showFieldError("#user_realname", "请输入真实姓名");
            return false;
        }
        if (!user_password) {
            this.showFieldError("#user_password", "请输入密码");
            return false;
        }
        if (!user_password_one) {
            this.showFieldError("#user_password_one", "请重复输入密码");
            return false;
        }
        if (!reg.test(user_password)) {
            this.showFieldError("#user_password", "密码格式必须包含数字和字母");
            return false;
        }
        if (user_password !== user_password_one) {
            this.showFieldError("#user_password_one", "两次输入密码不相同");
            return false;
        }
        if (!user_nickname) {
            this.showFieldError("#user_nickname", "请输入昵称");
            return false;
        }
        if (!user_birthday) {
            this.showFieldError("#user_birthday", "请选择出生日期");
            return false;
        }
        
        return true;
    },
    
    // 显示字段错误
    showFieldError: function(fieldSelector, message) {
        $(fieldSelector).css("border", "1px solid red")
            .next().text(message).css("display", "inline-block").css("color", "red");
    },
    
    // 显示成功消息
    showSuccess: function(message) {
        // 可以使用更好的UI组件，这里简单使用alert
        alert(message);
    },
    
    // 显示错误消息
    showError: function(message) {
        // 可以使用更好的UI组件，这里简单使用alert
        alert(message);
    },
    
    // 获取JWT Token
    getToken: function() {
        // 从localStorage或sessionStorage中获取token
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    UserInfoAPI.init();
});
