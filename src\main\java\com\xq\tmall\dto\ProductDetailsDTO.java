package com.xq.tmall.dto;

import com.xq.tmall.entity.Category;
import com.xq.tmall.entity.Product;
import com.xq.tmall.entity.Property;
import lombok.Data;

import java.util.List;

/**
 * 商品详情页面数据传输对象
 */
@Data
public class ProductDetailsDTO {
    
    /**
     * 商品基本信息
     */
    private Product product;
    
    /**
     * 商品属性列表
     */
    private List<Property> propertyList;
    
    /**
     * 猜你喜欢商品列表
     */
    private List<Product> recommendedProducts;
    
    /**
     * 分类列表
     */
    private List<Category> categoryList;
    
    /**
     * 随机数（用于猜你喜欢功能）
     */
    private Integer guessNumber;
    
    /**
     * 是否有库存
     */
    private Boolean hasStock;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 猜你喜欢商品结果
     */
    @Data
    public static class GuessProductResult {
        /**
         * 商品列表
         */
        private List<Product> products;
        
        /**
         * 新的随机数
         */
        private Integer newGuessNumber;
        
        /**
         * 是否成功
         */
        private Boolean success;
        
        public GuessProductResult(List<Product> products, Integer newGuessNumber, Boolean success) {
            this.products = products;
            this.newGuessNumber = newGuessNumber;
            this.success = success;
        }
    }
    
    /**
     * 创建成功的结果
     */
    public static ProductDetailsDTO success(Product product, List<Property> propertyList, 
                                          List<Product> recommendedProducts, List<Category> categoryList, 
                                          Integer guessNumber) {
        ProductDetailsDTO dto = new ProductDetailsDTO();
        dto.setProduct(product);
        dto.setPropertyList(propertyList);
        dto.setRecommendedProducts(recommendedProducts);
        dto.setCategoryList(categoryList);
        dto.setGuessNumber(guessNumber);
        dto.setHasStock(true);
        return dto;
    }
    
    /**
     * 创建失败的结果
     */
    public static ProductDetailsDTO error(String errorMessage) {
        ProductDetailsDTO dto = new ProductDetailsDTO();
        dto.setErrorMessage(errorMessage);
        return dto;
    }
}
