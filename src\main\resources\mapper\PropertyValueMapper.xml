<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.PropertyValueMapper">
    <resultMap id="propertyValueMap" type="com.xq.tmall.entity.PropertyValue">
        <id property="propertyValue_id" column="propertyValue_id"/>
        <result property="propertyValue_value" column="propertyValue_value"/>
        <association property="propertyValue_property" javaType="com.xq.tmall.entity.Property">
            <id property="property_id" column="propertyValue_property_id"/>
        </association>
        <association property="propertyValue_product" javaType="com.xq.tmall.entity.Product">
            <id property="product_id" column="propertyValue_product_id"/>
        </association>
    </resultMap>

    <insert id="insertOne" parameterType="com.xq.tmall.entity.PropertyValue">
        INSERT propertyValue(propertyValue_value,propertyValue_property_id,propertyValue_product_id)
            VALUES (#{propertyValue.propertyValue_value},
        #{propertyValue.propertyValue_property.property_id},
        #{propertyValue.propertyValue_product.product_id})
    </insert>
    <insert id="insertList" parameterType="list">
        INSERT propertyValue(propertyValue_value,propertyValue_property_id,propertyValue_product_id)
        VALUES
        <foreach collection="propertyValue_list" index="index" item="propertyValue" separator=",">
            (#{propertyValue.propertyValue_value},
            #{propertyValue.propertyValue_property.property_id},
            #{propertyValue.propertyValue_product.product_id})
        </foreach>
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.PropertyValue">
        UPDATE propertyValue
        <set>
            <if test="propertyValue.propertyValue_value != null">propertyValue_value = #{propertyValue.propertyValue_value}</if>
        </set>
        <where>
            propertyValue_id = #{propertyValue.propertyValue_id}
        </where>
    </update>
    <delete id="deleteOne" parameterType="java.lang.Integer">
        DELETE FROM propertyValue
        <where>
            propertyvalue_product_id =#{id}
        </where>
    </delete>
    <delete id="deleteList" parameterType="java.util.ArrayList">
        DELETE FROM propertyValue
        <where>
            propertyValue_id IN
            <foreach collection="propertyValue_id_list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete>
    <select id="selectPropertyValueList" resultMap="propertyValueMap">
        SELECT propertyValue_id,propertyValue_value,propertyValue_property_id,propertyValue_product_id FROM propertyValue
        <if test="propertyValue != null">
            <where>
                <if test="propertyValue.propertyValue_value">and propertyValue_value = #{propertyValue.propertyValue_value}</if>
                <if test="propertyValue.propertyValue_property != null">
                    <if test="propertyValue.propertyValue_property.property_id">and propertyValue_property_id = #{propertyValue.propertyValue_property.property_id}</if>
                </if>
                <if test="propertyValue.propertyValue_product != null">
                    <if test="propertyValue.propertyValue_product.product_id">and propertyValue_product_id = #{propertyValue.propertyValue_product.product_id}</if>
                </if>
            </where>
        </if>
    </select>
    <select id="selectOne" resultMap="propertyValueMap" parameterType="java.lang.Integer">
        SELECT propertyValue_id,propertyValue_value,propertyValue_property_id,propertyValue_product_id FROM propertyValue
        <where>
            propertyValue_id = #{propertyValue_id}
        </where>
    </select>
    <select id="selectTotal" resultType="java.lang.Integer" parameterType="com.xq.tmall.entity.PropertyValue">
        SELECT COUNT(*) FROM propertyValue
        <if test="propertyValue != null">
            <where>
                <if test="propertyValue.propertyValue_value">and propertyValue_value = #{propertyValue.propertyValue_value}</if>
                <if test="propertyValue.propertyValue_property != null">
                    <if test="propertyValue.propertyValue_property.property_id">and propertyValue_property_id = #{propertyValue.propertyValue_property.property_id}</if>
                </if>
                <if test="propertyValue.propertyValue_product != null">
                    <if test="propertyValue.propertyValue_product.product_id">and propertyValue_product_id = #{propertyValue.propertyValue_product.product_id}</if>
                </if>
            </where>
        </if>
    </select>
</mapper>