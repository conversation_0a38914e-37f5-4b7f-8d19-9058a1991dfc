package com.xq.tmall.controller.fore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.dto.ProductDetailsDTO;
import com.xq.tmall.entity.*;
import com.xq.tmall.service.*;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 前台天猫-产品详情页
 */
@Slf4j
@Api(tags = "前台天猫-产品详情页")
@Controller
@RequiredArgsConstructor
public class ForeProductDetailsController extends BaseController {
    private final ProductDetailsService productDetailsService;
    private final UserService userService;
    private final ReviewService reviewService;

    // 转到前台天猫-产品详情页
    @ApiOperation(value = "转到前台天猫-产品详情页", notes = "转到前台天猫-产品详情页")
    @GetMapping(value = "product/{pid}")
    public String goToPage(HttpServletRequest request, Map<String, Object> map,
                           @PathVariable("pid") String pid /*产品ID*/) {
        try {
            // 检查用户是否登录
            User user = checkUser(request);
            if (user != null) {
                map.put("user", user);
            }

            // 解析产品ID
            Integer productId;
            try {
                productId = Integer.parseInt(pid);
            } catch (NumberFormatException e) {
                log.warn("无效的产品ID: {}", pid);
                return "redirect:/404";
            }

            // 获取商品详情信息
            ProductDetailsDTO productDetails = productDetailsService.getProductDetails(productId);

            // 检查是否获取成功
            if (productDetails.getErrorMessage() != null) {
                log.warn("获取商品详情失败: {}", productDetails.getErrorMessage());
                return "redirect:/404";
            }

            // 获取产品评论信息
            Product product = productDetails.getProduct();
            product.setReviewList(reviewService.getListByProductId(productId, null));
            if (product.getReviewList() != null) {
                for (Review review : product.getReviewList()) {
                    review.setReview_user(userService.get(review.getReview_user().getUser_id()));
                }
            }

            // 设置页面数据
            map.put("product", product);
            map.put("propertyList", productDetails.getPropertyList());
            map.put("loveProductList", productDetails.getRecommendedProducts());
            map.put("categoryList", productDetails.getCategoryList());
            map.put("guessNumber", productDetails.getGuessNumber());
            map.put("pageUtil", new PageUtil(0, 10).setTotal(product.getProduct_review_count()));

            return "fore/productDetailsPage";

        } catch (Exception e) {
            log.error("商品详情页面加载失败，产品ID: {}", pid, e);
            return "redirect:/404";
        }
    }

    // 按产品ID加载产品评论列表-ajax
    @ApiOperation(value = "按产品ID加载产品评论列表", notes = "按产品ID加载产品评论列表")
    @ResponseBody
    @GetMapping(value = "review/{pid}", produces = "application/json;charset=utf-8")
    public String loadProductReviewList(@PathVariable("pid") String pid/*产品ID*/,
                                        @RequestParam Integer index/* 页数 */,
                                        @RequestParam Integer count/* 行数 */) {
        try {
            Integer productId = Integer.parseInt(pid);

            // 获取产品评论列表
            List<Review> reviewList = reviewService.getListByProductId(productId, new PageUtil(index, count));

            // 设置评论用户信息
            if (reviewList != null) {
                for (Review review : reviewList) {
                    User user = userService.get(review.getReview_user().getUser_id());
                    review.setReview_user(user);
                }
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", true);
            jsonObject.put("reviewList", JSON.parseArray(JSON.toJSONString(reviewList)));
            return String.valueOf(jsonObject);

        } catch (Exception e) {
            log.error("加载产品评论列表失败，产品ID: {}", pid, e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", false);
            jsonObject.put("message", "加载评论失败");
            return String.valueOf(jsonObject);
        }
    }

    // 按产品ID加载产品属性列表-ajax
    @ApiOperation(value = "按产品ID加载产品属性列表", notes = "按产品ID加载产品属性列表")
    @ResponseBody
    @GetMapping(value = "property/{pid}", produces = "application/json;charset=utf-8")
    public String loadProductPropertyList(@PathVariable("pid") String pid/*产品ID*/) {
        try {
            Integer productId = Integer.parseInt(pid);

            // 获取产品基本信息
            Product product = productDetailsService.getProductBasicInfo(productId);
            if (product == null) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("success", false);
                jsonObject.put("message", "产品不存在");
                return String.valueOf(jsonObject);
            }

            // 获取产品属性列表
            List<Property> propertyList = productDetailsService.getProductProperties(product);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", true);
            jsonObject.put("propertyList", JSON.parseArray(JSON.toJSONString(propertyList)));
            return String.valueOf(jsonObject);

        } catch (Exception e) {
            log.error("加载产品属性列表失败，产品ID: {}", pid, e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", false);
            jsonObject.put("message", "加载属性失败");
            return String.valueOf(jsonObject);
        }
    }

    // 加载猜你喜欢列表-ajax
    @ApiOperation(value = "加载猜你喜欢列表", notes = "加载猜你喜欢列表")
    @ResponseBody
    @GetMapping(value = "guess/{cid}", produces = "application/json;charset=utf-8")
    public String guessYouLike(@PathVariable("cid") Integer cid, @RequestParam Integer guessNumber) {
        try {
            // 获取随机推荐商品
            ProductDetailsDTO.GuessProductResult result = productDetailsService
                .getRandomRecommendedProducts(cid, guessNumber, 3);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", result.getSuccess());

            if (result.getSuccess()) {
                jsonObject.put("loveProductList", JSON.parseArray(JSON.toJSONString(result.getProducts())));
                jsonObject.put("guessNumber", result.getNewGuessNumber());
            } else {
                jsonObject.put("message", "获取推荐商品失败");
            }

            return String.valueOf(jsonObject);

        } catch (Exception e) {
            log.error("获取猜你喜欢列表失败，分类ID: {}", cid, e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("success", false);
            jsonObject.put("message", "服务器异常");
            return String.valueOf(jsonObject);
        }
    }
}
