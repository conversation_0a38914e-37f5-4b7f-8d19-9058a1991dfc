<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="include/header.jsp" %>
<head>
    <script src="${pageContext.request.contextPath}/res/js/fore/fore_productBuyCar.js"></script>
    <link href="${pageContext.request.contextPath}/res/css/fore/fore_productBuyCarPage.css" rel="stylesheet"/>
    <title>趣味商城 - 购物车</title>

</head>
<body>
<nav>
    <%@ include file="include/navigator.jsp" %>
    <div class="header">
        <div id="mallLogo">
        </div>
        <div class="shopSearchHeader">
            <form action="${pageContext.request.contextPath}/product" method="get">
                <div class="shopSearchInput">
                    <input type="text" class="searchInput" name="product_name" placeholder="搜索 商品/品牌/店铺" maxlength="50">
                    <input type="submit" value="搜 索" class="searchBtn">
                </div>
            </form>
            <ul id="categoryList">
                <!-- 分类列表将通过JavaScript动态加载 -->
            </ul>
        </div>
    </div>
</nav>
<div class="content">
    <!-- 加载中提示 -->
    <div id="loadingIndicator" style="text-align: center; padding: 50px;">
        <p>正在加载购物车数据...</p>
    </div>

    <!-- 空购物车提示 -->
    <div id="emptyCart" style="display: none;">
        <div id="crumbs">
            <span class="cart-tip">购物车帮您一次性完成批量购买与付款，下单更便捷，付款更简单！<a
                    href="http://service.taobao.com/support/help-11746.htm?spm=a1z0d.1.0.0.ogEwpx" target="_blank">如何使用购物车</a></span>
        </div>
        <div id="empty">
            <h2>您的购物车还是空的，赶紧行动吧！您可以：</h2>
            <ul>
                <li>看看<a href="${pageContext.request.contextPath}/order">已买到的宝贝</a></li>
            </ul>
        </div>
    </div>

    <!-- 购物车内容 -->
    <div id="cartContent" style="display: none;">
        <!-- 购物车内容将通过JavaScript动态加载 -->
    </div>
</div>

<%@include file="include/footer_two.jsp" %>
<%@include file="include/footer.jsp" %>
</body>
