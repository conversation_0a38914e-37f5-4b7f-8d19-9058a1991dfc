package com.xq.tmall.service.impl;

import com.xq.tmall.dto.ProductDetailsDTO;
import com.xq.tmall.entity.*;
import com.xq.tmall.service.*;
import com.xq.tmall.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 商品详情服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductDetailsServiceImpl implements ProductDetailsService {
    
    private final ProductService productService;
    private final ProductImageService productImageService;
    private final CategoryService categoryService;
    private final PropertyValueService propertyValueService;
    private final PropertyService propertyService;
    private final ReviewService reviewService;
    private final ProductOrderItemService productOrderItemService;
    
    @Override
    public ProductDetailsDTO getProductDetails(Integer productId) {
        try {
            // 获取商品基本信息
            Product product = getProductBasicInfo(productId);
            if (product == null || !isProductAvailable(product)) {
                return ProductDetailsDTO.error("商品不存在或已下架");
            }
            
            // 获取商品属性
            List<Property> propertyList = getProductProperties(product);
            
            // 获取推荐商品
            List<Product> recommendedProducts = getRecommendedProducts(
                product.getProduct_category().getCategory_id(), productId, 3);
            
            // 获取分类列表
            List<Category> categoryList = categoryService.getList(null, new PageUtil(0, 3));
            
            // 生成随机数
            Integer guessNumber = generateGuessNumber(product.getProduct_category().getCategory_id());
            
            return ProductDetailsDTO.success(product, propertyList, recommendedProducts, 
                                           categoryList, guessNumber);
            
        } catch (Exception e) {
            log.error("获取商品详情失败，商品ID: {}", productId, e);
            return ProductDetailsDTO.error("获取商品详情失败");
        }
    }
    
    @Override
    public Product getProductBasicInfo(Integer productId) {
        try {
            Product product = productService.get(productId);
            if (product == null) {
                return null;
            }
            
            // 设置分类信息
            product.setProduct_category(categoryService.get(product.getProduct_category().getCategory_id()));
            
            // 设置图片信息
            setProductImages(product);
            
            // 设置销量和评论数
            product.setProduct_sale_count(productOrderItemService.getSaleCountByProductId(productId));
            product.setProduct_review_count(reviewService.getTotalByProductId(productId));
            
            return product;
            
        } catch (Exception e) {
            log.error("获取商品基本信息失败，商品ID: {}", productId, e);
            return null;
        }
    }
    
    @Override
    public List<Property> getProductProperties(Product product) {
        try {
            // 获取产品属性值信息
            PropertyValue propertyValue1 = new PropertyValue();
            propertyValue1.setPropertyValue_product(product);
            List<PropertyValue> propertyValueList = propertyValueService.getList(propertyValue1, null);
            
            // 获取分类对应的属性列表
            Property property1 = new Property();
            property1.setProperty_category(product.getProduct_category());
            List<Property> propertyList = propertyService.getList(property1, null);
            
            // 合并属性和属性值
            mergePropertyAndValues(propertyList, propertyValueList);
            
            return propertyList;
            
        } catch (Exception e) {
            log.error("获取商品属性失败，商品ID: {}", product.getProduct_id(), e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Product> getRecommendedProducts(Integer categoryId, Integer excludeProductId, Integer count) {
        try {
            Category category = new Category();
            category.setCategory_id(categoryId);
            Product product = new Product();
            product.setProduct_category(category);
            
            Integer total = productService.getTotal(product, new Byte[]{0, 2});
            if (total == null || total <= 0) {
                return new ArrayList<>();
            }
            
            // 生成随机起始位置
            int startIndex = generateRandomStartIndex(total, count);
            
            List<Product> productList = productService.getList(product, new Byte[]{0, 2}, 
                null, new PageUtil().setCount(count).setPageStart(startIndex));
            
            if (!CollectionUtils.isEmpty(productList)) {
                // 设置商品图片
                for (Product prod : productList) {
                    prod.setSingleProductImageList(productImageService.getList(
                        prod.getProduct_id(), (byte) 0, new PageUtil(0, 1)));
                }
            }
            
            return productList;
            
        } catch (Exception e) {
            log.error("获取推荐商品失败，分类ID: {}", categoryId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public ProductDetailsDTO.GuessProductResult getRandomRecommendedProducts(Integer categoryId, 
                                                                            Integer currentGuessNumber, Integer count) {
        try {
            Category category = new Category();
            category.setCategory_id(categoryId);
            Product product = new Product();
            product.setProduct_category(category);
            
            Integer total = productService.getTotal(product, new Byte[]{0, 2});
            if (total == null || total <= 0) {
                return new ProductDetailsDTO.GuessProductResult(new ArrayList<>(), currentGuessNumber, false);
            }
            
            // 生成新的随机数，确保与当前不同
            int newGuessNumber = generateDifferentRandomNumber(total, currentGuessNumber, count);
            
            List<Product> productList = productService.getList(product, new Byte[]{0, 2}, 
                null, new PageUtil().setCount(count).setPageStart(newGuessNumber));
            
            if (!CollectionUtils.isEmpty(productList)) {
                // 设置商品图片
                for (Product prod : productList) {
                    prod.setSingleProductImageList(productImageService.getList(
                        prod.getProduct_id(), (byte) 0, new PageUtil(0, 1)));
                }
            }
            
            return new ProductDetailsDTO.GuessProductResult(productList, newGuessNumber, true);
            
        } catch (Exception e) {
            log.error("获取随机推荐商品失败，分类ID: {}", categoryId, e);
            return new ProductDetailsDTO.GuessProductResult(new ArrayList<>(), currentGuessNumber, false);
        }
    }
    
    @Override
    public boolean isProductAvailable(Product product) {
        return product != null && product.getProduct_isEnabled() != 1;
    }
    
    /**
     * 设置商品图片信息
     */
    private void setProductImages(Product product) {
        List<ProductImage> productImageList = productImageService.getList(product.getProduct_id(), null, null);
        List<ProductImage> singleProductImageList = new ArrayList<>(5);
        List<ProductImage> detailsProductImageList = new ArrayList<>(8);
        
        for (ProductImage productImage : productImageList) {
            if (productImage.getProductImage_type() == 0) {
                singleProductImageList.add(productImage);
            } else {
                detailsProductImageList.add(productImage);
            }
        }
        
        product.setSingleProductImageList(singleProductImageList);
        product.setDetailProductImageList(detailsProductImageList);
    }
    
    /**
     * 合并属性和属性值
     */
    private void mergePropertyAndValues(List<Property> propertyList, List<PropertyValue> propertyValueList) {
        for (Property property : propertyList) {
            List<PropertyValue> values = new ArrayList<>();
            for (PropertyValue propertyValue : propertyValueList) {
                if (property.getProperty_id().equals(propertyValue.getPropertyValue_property().getProperty_id())) {
                    values.add(propertyValue);
                }
            }
            property.setPropertyValueList(values);
        }
    }
    
    /**
     * 生成随机起始索引
     */
    private int generateRandomStartIndex(Integer total, Integer count) {
        Random random = new Random();
        int maxStart = Math.max(0, total - count);
        return random.nextInt(maxStart + 1);
    }
    
    /**
     * 生成随机数
     */
    private Integer generateGuessNumber(Integer categoryId) {
        Category category = new Category();
        category.setCategory_id(categoryId);
        Product product = new Product();
        product.setProduct_category(category);
        
        Integer total = productService.getTotal(product, new Byte[]{0, 2});
        if (total == null || total <= 0) {
            return 0;
        }
        
        return generateRandomStartIndex(total, 3);
    }
    
    /**
     * 生成与当前不同的随机数
     */
    private int generateDifferentRandomNumber(Integer total, Integer currentNumber, Integer count) {
        Random random = new Random();
        int maxStart = Math.max(0, total - count);
        int newNumber;
        
        do {
            newNumber = random.nextInt(maxStart + 1);
        } while (newNumber == currentNumber && maxStart > 0);
        
        return newNumber;
    }
}
