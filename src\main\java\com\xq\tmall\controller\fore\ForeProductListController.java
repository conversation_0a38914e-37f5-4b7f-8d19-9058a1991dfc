package com.xq.tmall.controller.fore;

import com.xq.tmall.controller.BaseController;
import com.xq.tmall.dto.ProductListDTO;
import com.xq.tmall.entity.Category;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.CategoryService;
import com.xq.tmall.service.ProductListService;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 前台天猫-产品搜索列表
 */
@Slf4j
@Api(tags = "前台天猫-产品搜索列表")
@Controller
@RequiredArgsConstructor
public class ForeProductListController extends BaseController {
    private final ProductListService productListService;
    private final CategoryService categoryService;


    // 转到前台天猫-产品搜索列表页
    @ApiOperation(value = "转到前台天猫-产品搜索列表页", notes = "转到前台天猫-产品搜索列表页")
    @GetMapping(value = "product")
    public String goToPage(HttpServletRequest request, Map<String, Object> map,
                           @RequestParam(value = "category_id", required = false) Integer categoryId/* 分类ID */,
                           @RequestParam(value = "product_name", required = false) String productName/* 产品名称 */,
                           @RequestParam(value = "page", required = false, defaultValue = "0") Integer page/* 页码 */,
                           @RequestParam(value = "size", required = false, defaultValue = "10") Integer size/* 每页数量 */) {
        try {
            // 检查用户是否登录
            User user = checkUser(request);
            if (user != null) {
                map.put("user", user);
            }

            // 参数验证
            if (!productListService.isValidSearchParams(categoryId, productName)) {
                return "redirect:/";
            }

            // 如果是分类搜索，检查分类是否存在且未删除
            if (categoryId != null) {
                Category category = categoryService.get(categoryId);
                if (category == null) {
                    log.warn("分类不存在或已删除，分类ID: {}", categoryId);
                    return "redirect:/";
                }
            }

            // 设置搜索参数到页面，让JavaScript使用
            Map<String, Object> searchParams = new HashMap<>();
            searchParams.put("category_id", categoryId != null ? categoryId : "");
            searchParams.put("product_name", productName != null ? productName : "");
            searchParams.put("page", page);
            searchParams.put("size", size);
            map.put("searchParams", searchParams);

            return "fore/productListPage";

        } catch (Exception e) {
            log.error("商品列表页面加载失败", e);
            return "redirect:/";
        }
    }

    // 获取商品列表数据-API接口
    @ApiOperation(value = "获取商品列表数据", notes = "获取商品列表数据")
    @ResponseBody
    @GetMapping(value = "api/product/list", produces = "application/json;charset=utf-8")
    public Result<ProductListDTO> getProductListData(
                           @RequestParam(value = "category_id", required = false) Integer categoryId/* 分类ID */,
                           @RequestParam(value = "product_name", required = false) String productName/* 产品名称 */,
                           @RequestParam(value = "page", required = false, defaultValue = "0") Integer page/* 页码 */,
                           @RequestParam(value = "size", required = false, defaultValue = "10") Integer size/* 每页数量 */,
                           @RequestParam(required = false) String orderBy/* 排序字段 */,
                           @RequestParam(required = false, defaultValue = "false") Boolean isDesc/* 是否倒序 */) {
        try {
            // 参数验证
            if (!productListService.isValidSearchParams(categoryId, productName)) {
                return Result.error("搜索参数无效");
            }

            // 如果是分类搜索，检查分类是否存在且未删除
            if (categoryId != null) {
                Category category = categoryService.get(categoryId);
                if (category == null) {
                    log.warn("分类不存在或已删除，分类ID: {}", categoryId);
                    return Result.error("分类不存在或已删除");
                }
            }

            // 构建分页参数
            PageUtil pageUtil = new PageUtil(page, size);

            // 获取商品列表
            ProductListDTO productListDTO = productListService.getProductList(
                categoryId, productName, pageUtil, orderBy, isDesc);

            // 检查是否获取成功
            if (productListDTO.getErrorMessage() != null) {
                log.warn("获取商品列表失败: {}", productListDTO.getErrorMessage());
                return Result.error(productListDTO.getErrorMessage());
            }

            // 设置分页信息
            productListDTO.setPagination(productListDTO.getPaginationInfo());

            return Result.success("获取商品列表成功", productListDTO);

        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            return Result.error("获取商品列表失败：" + e.getMessage());
        }
    }

    // 产品高级查询（带分页和排序）
    @ApiOperation(value = "产品高级查询", notes = "产品高级查询")
    @GetMapping(value = "product/{page}/{size}")
    public String searchProduct(HttpServletRequest request, Map<String, Object> map,
                                @PathVariable("page") Integer page/* 页码 */,
                                @PathVariable("size") Integer size/* 每页数量*/,
                                @RequestParam(value = "category_id", required = false) Integer categoryId/* 分类ID */,
                                @RequestParam(value = "product_name", required = false) String productName/* 产品名称 */,
                                @RequestParam(required = false) String orderBy/* 排序字段 */,
                                @RequestParam(required = false, defaultValue = "true") Boolean isDesc/* 是否倒序 */) {
        try {
            // 检查用户是否登录
            User user = checkUser(request);
            if (user != null) {
                map.put("user", user);
            }

            // 参数验证
            if (!productListService.isValidSearchParams(categoryId, productName)) {
                return "redirect:/";
            }

            // 如果是分类搜索，检查分类是否存在且未删除
            if (categoryId != null) {
                Category category = categoryService.get(categoryId);
                if (category == null) {
                    log.warn("分类不存在或已删除，分类ID: {}", categoryId);
                    return "redirect:/";
                }
            }

            // 构建分页参数
            PageUtil pageUtil = new PageUtil(page, size);

            // 获取商品列表
            ProductListDTO productListDTO = productListService.getProductList(
                categoryId, productName, pageUtil, orderBy, isDesc);

            // 检查是否获取成功
            if (productListDTO.getErrorMessage() != null) {
                log.warn("获取商品列表失败: {}", productListDTO.getErrorMessage());
                map.put("searchValue", productName);
                return "fore/productListPage";
            }

            // 设置页面数据
            map.put("productList", productListDTO.getProductList());
            map.put("categoryList", productListDTO.getCategoryList());
            map.put("pageUtil", productListDTO.getPageUtil());
            map.put("totalPage", productListDTO.getTotalPage());
            map.put("productCount", productListDTO.getProductCount());
            map.put("searchValue", productListDTO.getSearchValue());
            map.put("searchType", productListDTO.getSearchType());
            map.put("orderBy", productListDTO.getOrderBy());
            map.put("isDesc", productListDTO.getIsDesc());
            map.put("currentPage", productListDTO.getCurrentPage());
            map.put("pagination", productListDTO.getPaginationInfo());

            return "fore/productListPage";

        } catch (Exception e) {
            log.error("商品高级查询失败", e);
            return "redirect:/";
        }
    }
}