<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="include/header.jsp" %>
<link href="${pageContext.request.contextPath}/res/css/fore/fore_productList.css" rel="stylesheet">
<body>
<title id="pageTitle">商品列表-趣味商城-理想生活上趣味商城</title>

<!-- 传递搜索参数给JavaScript -->
<script>
    window.searchParams = {
        category_id: '${searchParams.category_id}',
        product_name: '${searchParams.product_name}',
        page: ${searchParams.page},
        size: ${searchParams.size}
    };
</script>
<nav>
    <%@ include file="include/navigator.jsp" %>
    <div class="header">
        <div id="mallLogo">

        </div>
        <div class="shopSearchHeader">
            <form id="searchForm">
                <div class="shopSearchInput">
                    <input type="text" class="searchInput" name="product_name" placeholder="搜索 商品/品牌/店铺" maxlength="50">
                    <input type="submit" value="搜 索" class="searchBtn">
                </div>
            </form>
            <ul id="categoryList">
                <!-- 分类列表将通过JavaScript动态加载 -->
            </ul>
        </div>
    </div>
</nav>
<div class="context">
    <!-- 加载中提示 -->
    <div id="loadingIndicator" style="text-align: center; padding: 50px;">
        <p>正在加载商品数据...</p>
    </div>

    <!-- 商品列表容器 -->
    <div id="productListContainer" style="display: none;">
        <!-- 排序菜单 -->
        <div class="context_menu">
            <ul id="sortMenu">
                <li data-name="product_name" class="orderBySelect">
                    <span>综合</span>
                    <span class="orderByAsc"></span>
                </li>
                <li data-name="product_create_date">
                    <span>新品</span>
                    <span class="orderByAsc"></span>
                </li>
                <li data-name="product_sale_count">
                    <span>销量</span>
                    <span class="orderByAsc"></span>
                </li>
                <li data-name="product_sale_price">
                    <span style="position: relative;left: 3px">价格</span>
                    <span class="orderByDesc" style="bottom: 5px; left: 6px;"></span>
                    <span class="orderByAsc" style="top:4px;right: 5px;"></span>
                </li>
            </ul>
        </div>

        <!-- 商品列表 -->
        <div class="context_main" id="productList">
            <!-- 商品将通过JavaScript动态加载 -->
        </div>

    
        <!-- 分页控制器 -->
        <div id="paginationContainer">
            <!-- 分页将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 无商品提示 -->
    <div id="noProductsMessage" style="display: none;">

            <div class="error">
                <h2>喵~没找到与“${requestScope.searchValue}”相关的 商品 哦，要不您换个关键词我帮您再找找看</h2>
                <h3>建议您：</h3>
                <ol>
                    <li>看看输入的文字是否有误</li>
                    <li>调整关键词，如“全铜花洒套件”改成“花洒”或“花洒 套件”</li>
                    <li>
                        <form action="${pageContext.request.contextPath}/product" method="get">
                            <input title="查询产品" type="text" class="errorInput" name="product_name"
                                   value="${requestScope.searchValue}">
                            <input type="submit" value="去淘宝搜索" class="errorBtn">
                        </form>
                    </li>
                </ol>
            </div>

</div>
<%@ include file="include/footer_two.jsp" %>
<%@ include file="include/footer.jsp" %>

<script src="${pageContext.request.contextPath}/res/js/fore/fore_productList.js"></script>
</body>