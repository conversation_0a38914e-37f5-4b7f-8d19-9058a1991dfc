<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="include/header.jsp" %>
<head>
    <link href="${pageContext.request.contextPath}/res/css/fore/fore_orderList.css" rel="stylesheet"/>
    <title>已买到的宝贝</title>
    <script>
        // 全局变量
        var contextPath = "${pageContext.request.contextPath}";
        var currentIndex = 0;
        var currentCount = 10;
        var currentStatus = null;
        var orderListData = null;

        $(function () {
            // 首先检查用户是否登录
            var token = localStorage.getItem('token') || sessionStorage.getItem('token');
            if (!token) {
                // 用户未登录，跳转到登录页面
                window.location.href = contextPath + "/login";
                return;
            }

            // 从URL中获取参数
            var urlParams = new URLSearchParams(window.location.search);
            var pathParts = window.location.pathname.split('/');

            // 获取路径中的index和count参数
            if (pathParts.length >= 4) {
                currentIndex = parseInt(pathParts[pathParts.length - 2]) || 0;
                currentCount = parseInt(pathParts[pathParts.length - 1]) || 10;
            }

            // 获取status参数
            if (urlParams.has('status')) {
                currentStatus = parseInt(urlParams.get('status'));
            }

            // 加载订单数据
            loadOrderData();

            // 绑定取消订单确认按钮事件
            $('#btn-ok').click(function () {
                $.ajax({
                    url: contextPath + "/order/close/" + $("#order_id_hidden").val(),
                    type: "PUT",
                    data: null,
                    dataType: "json",
                    success: function (data) {
                        if (data.success !== true) {
                            alert("订单处理异常，请稍候再试！");
                        }
                        // 重新加载当前页面数据
                        loadOrderData();
                        $('#modalDiv').modal('hide');
                    },
                    beforeSend: function () {

                    },
                    error: function () {
                        alert("订单取消出现问题，请稍后再试！");
                        loadOrderData();
                        $('#modalDiv').modal('hide');
                    }
                });
            });
        });

        // 加载订单数据
        function loadOrderData() {
            var token = localStorage.getItem('token') || sessionStorage.getItem('token');
            var url = contextPath + "/api/order/" + currentIndex + "/" + currentCount;
            if (currentStatus !== null) {
                url += "?status=" + currentStatus;
            }

            $.ajax({
                url: url,
                type: "GET",
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                dataType: "json",
                success: function (response) {
                    if (response.code === 200 && response.data) {
                        orderListData = response.data;
                        renderOrderList(response.data);
                        renderCategoryList(response.data.categoryList);
                        renderPagination(response.data.pageUtil);
                        updateTabSelection();
                    } else {
                        if (response.code === 500 && response.message === "用户未登录") {
                            // 用户未登录，清除token并跳转到登录页面
                            localStorage.removeItem('token');
                            sessionStorage.removeItem('token');
                            window.location.href = contextPath + "/login";
                        } else {
                            alert("获取订单列表失败：" + (response.message || "未知错误"));
                        }
                    }
                },
                error: function (xhr, status, error) {
                    console.error('加载订单列表失败:', error);
                    if (xhr.status === 401) {
                        // token过期或无效，清除token并跳转到登录页面
                        localStorage.removeItem('token');
                        sessionStorage.removeItem('token');
                        window.location.href = contextPath + "/login";
                    } else {
                        alert('网络错误，请稍后重试');
                    }
                }
            });
        }

        function closeOrder(orderCode) {
            if (isNaN(orderCode) || orderCode === null) {
                return;
            }
            $("#order_id_hidden").val(orderCode);
            $('#modalDiv').modal();
        }

        function getPage(index) {
            currentIndex = index;
            var statusParam = currentStatus !== null ? "?status=" + currentStatus : "";
            window.location.href = contextPath + "/order/" + index + "/" + currentCount + statusParam;
        }
    </script>
</head>
<body>
<nav>
    <%@ include file="include/navigator.jsp" %>
    <div class="header">
        <div id="mallLogo">
        </div>
        <div class="shopSearchHeader">
            <form action="${pageContext.request.contextPath}/product" method="get">
                <div class="shopSearchInput">
                    <input type="text" class="searchInput" name="product_name" placeholder="搜索 商品/品牌/店铺"
                           maxlength="50">
                    <input type="submit" value="搜 索" class="searchBtn">
                </div>
            </form>
            <ul id="categoryList">
                <!-- 分类列表将通过JavaScript动态加载 -->
            </ul>
        </div>
    </div>
</nav>
<div class="content">
    <ul class="tabs_ul">
        <li id="tab_all"><a href="javascript:void(0)" onclick="changeStatus(null)">所有订单</a></li>
        <li id="tab_0"><a href="javascript:void(0)" onclick="changeStatus(0)" name="status=0">待付款</a></li>
        <li id="tab_1"><a href="javascript:void(0)" onclick="changeStatus(1)" name="status=1">待发货</a></li>
        <li id="tab_2"><a href="javascript:void(0)" onclick="changeStatus(2)" name="status=2">待收货</a></li>
        <li id="tab_3"><a href="javascript:void(0)" onclick="changeStatus(3)" name="status=3">已完成</a></li>
    </ul>
    <div id="paginationTop">
        <!-- 分页信息将通过JavaScript动态加载 -->
    </div>
    <table class="table_orderList">
        <thead>
        <tr>
            <th>宝贝</th>
            <th width="80px">单价</th>
            <th width="80px">数量</th>
            <th width="140px">实付款</th>
            <th width="140px">交易状态</th>
            <th width="140px">交易操作</th>
        </tr>
        </thead>
        <tbody id="orderListBody">
            <!-- 订单列表将通过JavaScript动态加载 -->
        </tbody>
    </table>
    <div id="paginationBottom">
        <!-- 分页信息将通过JavaScript动态加载 -->
    </div>
</div>

<script>
    // 渲染分类列表
    function renderCategoryList(categoryList) {
        var categoryListHtml = '';
        if (categoryList && categoryList.length > 0) {
            for (var i = 0; i < categoryList.length; i++) {
                var category = categoryList[i];
                categoryListHtml += '<li><a href="' + contextPath + '/product?category_id=' + category.category_id + '">' + category.category_name + '</a></li>';
            }
        }
        $('#categoryList').html(categoryListHtml);
    }

    // 渲染订单列表
    function renderOrderList(data) {
        var orderListHtml = '';
        var productOrderList = data.productOrderList;

        if (productOrderList && productOrderList.length > 0) {
            for (var i = 0; i < productOrderList.length; i++) {
                var productOrder = productOrderList[i];
                var productOrderItemList = productOrder.productOrderItemList;

                // 订单信息行
                orderListHtml += '<tr class="tr_order_info">';
                orderListHtml += '<td colspan="6">';
                orderListHtml += '<span class="span_pay_date">' + (productOrder.productOrder_pay_date || '') + '</span>';
                orderListHtml += '<span class="span_order_code_title">订单号:</span>';
                orderListHtml += '<span class="span_order_code">' + productOrder.productOrder_code + '</span>';
                orderListHtml += '</td>';
                orderListHtml += '</tr>';

                // 订单项行
                if (productOrderItemList && productOrderItemList.length > 0) {
                    for (var j = 0; j < productOrderItemList.length; j++) {
                        var productOrderItem = productOrderItemList[j];
                        var product = productOrderItem.productOrderItem_product;
                        var unitPrice = (productOrderItem.productOrderItem_price / productOrderItem.productOrderItem_number).toFixed(2);

                        orderListHtml += '<tr class="tr_orderItem_info">';

                        // 商品信息
                        orderListHtml += '<td>';
                        if (product.singleProductImageList && product.singleProductImageList.length > 0) {
                            orderListHtml += '<img class="orderItem_product_image" src="' + contextPath + '/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src + '" style="width: 80px;height: 80px;"/>';
                        }
                        orderListHtml += '<span class="orderItem_product_name">';
                        orderListHtml += '<a href="' + contextPath + '/product/' + product.product_id + '">' + product.product_name + '</a>';
                        orderListHtml += '</span>';
                        orderListHtml += '</td>';

                        // 单价
                        orderListHtml += '<td><span class="orderItem_product_price">￥' + unitPrice + '</span></td>';

                        // 数量
                        orderListHtml += '<td><span class="orderItem_product_number">' + productOrderItem.productOrderItem_number + '</span></td>';

                        // 实付款
                        orderListHtml += '<td class="td_order_content"><span class="orderItem_product_realPrice">￥' + productOrderItem.productOrderItem_price + '</span></td>';

                        // 第一个订单项显示订单状态和操作
                        if (j === 0) {
                            var rowspan = productOrderItemList.length;

                            // 交易状态
                            orderListHtml += '<td class="td_order_content" rowspan="' + rowspan + '">';
                            switch (productOrder.productOrder_status) {
                                case 0:
                                    orderListHtml += '<span class="span_order_status" title="等待买家付款">等待买家付款</span>';
                                    break;
                                case 1:
                                    orderListHtml += '<span class="span_order_status" title="买家已付款，等待卖家发货">等待卖家发货</span>';
                                    break;
                                case 2:
                                    orderListHtml += '<span class="span_order_status" title="卖家已发货，等待买家确认">等待买家确认</span>';
                                    break;
                                case 3:
                                    orderListHtml += '<span class="span_order_status" title="交易成功">交易成功</span>';
                                    break;
                                default:
                                    orderListHtml += '<span class="td_error" title="交易关闭">交易关闭</span>';
                                    break;
                            }
                            orderListHtml += '</td>';

                            // 交易操作
                            orderListHtml += '<td class="td_order_content" rowspan="' + rowspan + '">';
                            switch (productOrder.productOrder_status) {
                                case 0:
                                    orderListHtml += '<a class="order_btn pay_btn" href="' + contextPath + '/order/pay/' + productOrder.productOrder_code + '">立即付款</a>';
                                    orderListHtml += '<p class="order_close"><a class="order_close" href="javascript:void(0)" onclick="closeOrder(\'' + productOrder.productOrder_code + '\')">取消订单</a></p>';
                                    break;
                                case 1:
                                    orderListHtml += '<a class="order_btn delivery_btn" href="' + contextPath + '/order/delivery/' + productOrder.productOrder_code + '">提醒发货</a>';
                                    break;
                                case 2:
                                    orderListHtml += '<a class="order_btn confirm_btn" href="' + contextPath + '/order/confirm/' + productOrder.productOrder_code + '">确认收货</a>';
                                    break;
                                default:
                                    break;
                            }
                            orderListHtml += '</td>';
                        }

                        // 评价按钮（仅对已完成且未评价的订单项显示）
                        if (productOrder.productOrder_status === 3 && productOrderItem.isReview !== null && !productOrderItem.isReview) {
                            orderListHtml += '<td class="td_order_content">';
                            orderListHtml += '<a class="order_btn review_btn" href="' + contextPath + '/review/' + productOrderItem.productOrderItem_id + '">评价</a>';
                            orderListHtml += '</td>';
                        }

                        orderListHtml += '</tr>';
                    }
                }
            }
        } else {
            // 没有订单时显示提示信息
            orderListHtml += '<tr>';
            orderListHtml += '<td colspan="6" class="no_search_result">';
            orderListHtml += '<img src="' + contextPath + '/res/images/fore/WebsiteImage/T1MQ1cXhtiXXXXXXXX-78-120.png"/>';
            orderListHtml += '<span class="error_msg">没有符合条件的宝贝，请尝试其他搜索条件。</span>';
            orderListHtml += '</td>';
            orderListHtml += '</tr>';
        }

        $('#orderListBody').html(orderListHtml);
    }

    // 渲染分页信息
    function renderPagination(pageUtil) {
        // 这里可以根据需要实现分页组件
        // 暂时简化处理
        var paginationHtml = '';
        if (pageUtil && pageUtil.totalPage > 1) {
            paginationHtml += '<div class="pagination">';
            for (var i = 0; i < pageUtil.totalPage; i++) {
                var isActive = i === pageUtil.index ? 'active' : '';
                paginationHtml += '<a href="javascript:void(0)" onclick="getPage(' + i + ')" class="' + isActive + '">' + (i + 1) + '</a>';
            }
            paginationHtml += '</div>';
        }
        $('#paginationTop').html(paginationHtml);
        $('#paginationBottom').html(paginationHtml);
    }

    // 更新标签页选中状态
    function updateTabSelection() {
        $('.tabs_ul li').removeClass('tab_select');
        if (currentStatus === null) {
            $('#tab_all').addClass('tab_select');
        } else {
            $('#tab_' + currentStatus).addClass('tab_select');
        }
    }

    // 切换订单状态
    function changeStatus(status) {
        currentStatus = status;
        currentIndex = 0; // 重置到第一页
        var statusParam = status !== null ? "?status=" + status : "";
        window.location.href = contextPath + "/order/" + currentIndex + "/" + currentCount + statusParam;
    }
</script>
<%-- 模态框 --%>
<div class="modal fade" id="modalDiv" tabindex="-1" role="dialog" aria-labelledby="modalDiv" aria-hidden="true"
     data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">提示</h4>
            </div>
            <div class="modal-body">您确定要取消该订单吗？取消订单后，不能恢复。</div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" id="btn-ok">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal" id="btn-close">关闭</button>
                <input type="hidden" id="order_id_hidden">
            </div>
        </div>
        <%-- /.modal-content --%>
    </div>
    <%-- /.modal --%>
</div>
<%@include file="include/footer_two.jsp" %>
<%@include file="include/footer.jsp" %>
</body>
