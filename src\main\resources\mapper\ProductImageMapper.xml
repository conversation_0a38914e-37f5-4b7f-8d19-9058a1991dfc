<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.ProductImageMapper">
    <resultMap id="imageMap" type="com.xq.tmall.entity.ProductImage">
        <id property="productImage_id" column="productImage_id" />
        <result property="productImage_type" column="productImage_type"/>
        <result property="productImage_src" column="productImage_src"/>
        <association property="productImage_product" javaType="com.xq.tmall.entity.Product">
            <id property="product_id" column="productImage_product_id"/>
        </association>
    </resultMap>

    <insert id="insertOne" parameterType="com.xq.tmall.entity.ProductImage">
        INSERT productImage (productImage_type,productImage_src,productImage_product_id)
            VALUES (#{productImage.productImage_type},
            #{productImage.productImage_src},
            #{productImage.productImage_product.product_id})
    </insert>
    <insert id="insertList" parameterType="list">
        INSERT productImage (productImage_type,productImage_src,productImage_product_id)
        VALUES
        <foreach collection="productImage_list" item="productImage" index="index" separator=",">
            (#{productImage.productImage_type},
            #{productImage.productImage_src},
            #{productImage.productImage_product.product_id})
        </foreach>
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.ProductImage">
        UPDATE productImage
        <set>
            <if test="productImage.productImage_type != null">productImage_type = #{productImage.productImage_type},
            </if>
            <if test="productImage.productImage_src != null">productImage_src = #{productImage.productImage_src}</if>
        </set>
        <where>
            productImage_id = #{productImage.productImage_id}
        </where>
    </update>
    <delete id="deleteList" parameterType="java.util.ArrayList">
        DELETE FROM productImage
        <where>
            productImage_id IN
            <foreach collection="productImage_id_list" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>
    <select id="selectProductImageList" resultMap="imageMap">
        SELECT productImage_id,productImage_type,productImage_src,productImage_product_id FROM productImage
        <where>
            <if test="product_id != null">
                and productImage_product_id = #{product_id}
            </if>
            <if test="productImage_type != null">
                and productImage_type = #{productImage_type}
            </if>
        </where>
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectOne" resultMap="imageMap" parameterType="java.lang.Integer">
        SELECT productImage_id,productImage_type,productImage_src,productImage_product_id FROM productImage
        <where>
            productImage_id = #{productImage_id}
        </where>
    </select>
    <select id="selectTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM productImage
        <where>
            <if test="product_id != null">
                and productImage_product_id = #{product_id}
            </if>
            <if test="productImage_type != null">
                and productImage_type = #{productImage_type}
            </if>
        </where>
    </select>
</mapper>