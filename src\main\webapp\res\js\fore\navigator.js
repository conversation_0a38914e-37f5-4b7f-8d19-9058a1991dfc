/**
 * 导航栏用户状态管理
 */
var NavigatorManager = {
    
    // API基础路径
    baseUrl: '',
    
    // 初始化
    init: function() {
        this.baseUrl = this.getContextPath();
        this.checkUserLoginStatus();
    },
    
    // 获取上下文路径
    getContextPath: function() {
        var pathName = document.location.pathname;
        var index = pathName.substr(1).indexOf("/");
        var result = pathName.substr(0, index + 1);
        return result === "/" ? "" : result;
    },
    
    // 检查用户登录状态
    checkUserLoginStatus: function() {
        var token = this.getToken();
        console.log('Navigator: 检查用户登录状态, token:', token ? '存在' : '不存在');

        if (token) {
            // 如果在用户详情页面，不重复调用API
            if (window.location.pathname.indexOf('/userDetails') !== -1) {
                console.log('Navigator: 在用户详情页面，跳过API调用');
                // 仍然显示用户信息，但使用简单的显示
                this.showSimpleUserInfo();
                return;
            }
            this.loadUserInfo();
        } else {
            this.showLoginLinks();
        }
    },
    
    // 加载用户信息
    loadUserInfo: function() {
        var self = this;

        console.log('Navigator: 开始加载用户信息');

        $.ajax({
            url: self.baseUrl + '/api/user/info',
            type: 'GET',
            headers: {
                'Authorization': 'Bearer ' + self.getToken()
            },
            dataType: 'json',
            success: function(response) {
                console.log('Navigator: API响应成功', response);
                if (response.code === 200) {
                    self.showUserInfo(response.data.user);
                } else {
                    console.log('Navigator: API返回错误', response.message);
                    // Token可能已过期，清除并显示登录链接
                    self.clearToken();
                    self.showLoginLinks();
                }
            },
            error: function(xhr, status, error) {
                console.error('Navigator: API请求失败', xhr.status, error);
                // 请求失败，可能是网络问题或token过期
                if (xhr.status === 401) {
                    self.clearToken();
                }
                self.showLoginLinks();
            }
        });
    },
    
    // 显示用户信息
    showUserInfo: function(user) {
        var loginContainer = $("#container_login");
        var userDisplayName = user.user_nickname || user.user_name || '用户';

        var userHtml = '<em>你好，' + userDisplayName + '</em>' +
                      '<a href="' + this.baseUrl + '/userDetails">个人中心</a>' +
                      '<a href="javascript:void(0)" onclick="NavigatorManager.logout()">退出登录</a>';

        loginContainer.html(userHtml);
    },

    // 显示简单的用户信息（不调用API）
    showSimpleUserInfo: function() {
        var loginContainer = $("#container_login");

        var userHtml = '<em>你好，用户</em>' +
                      '<a href="' + this.baseUrl + '/userDetails">个人中心</a>' +
                      '<a href="javascript:void(0)" onclick="NavigatorManager.logout()">退出登录</a>';

        loginContainer.html(userHtml);
    },

    // 更新用户信息（供其他模块调用）
    updateUserInfo: function(user) {
        if (user) {
            this.showUserInfo(user);
        } else {
            this.showLoginLinks();
        }
    },
    
    // 显示登录链接
    showLoginLinks: function() {
        var loginContainer = $("#container_login");
        
        var loginHtml = '<em>喵，欢迎来天猫</em>' +
                       '<a href="' + this.baseUrl + '/login">请登录</a>' +
                       '<a href="' + this.baseUrl + '/register">免费注册</a>';
        
        loginContainer.html(loginHtml);
    },
    
    // 用户退出登录
    logout: function() {
        var self = this;
        
        // 调用退出登录API
        $.ajax({
            url: self.baseUrl + '/api/logout',
            type: 'POST',
            headers: {
                'Authorization': 'Bearer ' + self.getToken()
            },
            dataType: 'json',
            success: function(response) {
                // 无论API调用是否成功，都清除本地token
                self.clearToken();
                self.showLoginLinks();
                
                // 如果在需要登录的页面，跳转到首页
                if (self.isLoginRequiredPage()) {
                    window.location.href = self.baseUrl + '/';
                }
            },
            error: function(xhr, status, error) {
                // 即使API调用失败，也清除本地token
                self.clearToken();
                self.showLoginLinks();
                
                if (self.isLoginRequiredPage()) {
                    window.location.href = self.baseUrl + '/';
                }
            }
        });
    },
    
    // 检查是否是需要登录的页面
    isLoginRequiredPage: function() {
        var currentPath = window.location.pathname;
        var loginRequiredPaths = ['/userDetails', '/order', '/cart'];
        
        return loginRequiredPaths.some(function(path) {
            return currentPath.indexOf(path) !== -1;
        });
    },
    
    // 获取JWT Token
    getToken: function() {
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    },
    
    // 清除JWT Token
    clearToken: function() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
    }
};

// 将NavigatorManager设为全局变量，供其他模块使用
window.NavigatorManager = NavigatorManager;

// 页面加载完成后初始化
$(document).ready(function() {
    NavigatorManager.init();
});
