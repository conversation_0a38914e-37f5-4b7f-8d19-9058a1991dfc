.header {
    width: 1230px;
    margin: 0 auto;
    height: 96px;
}

.header > #mall<PERSON>ogo {
    float: left;
    padding-top: 28px;
    width: 280px;
    height: 64px;
    line-height: 64px;
    position: relative;
}

#mallLogo > a {
    position: relative;
    display: block;
    width: 190px;
    height: 30px;
    overflow: hidden;
}

#mallLogo img {
    width: 190px;
    height: 28px;
    cursor: pointer;
    vertical-align: top;
}

.header > .shopSearchHeader {
    float: right;
    overflow: hidden;
    width: 597px;
    padding-top: 25px;
}

.shopSearchHeader > form {
    border: solid #ff0036;
    border-width: 3px 0 3px 3px;
}

.shopSearchHeader > form > .shopSearchInput {
    position: relative;
    height: 30px;
    font-family: Arial, serif;
}

input::-webkit-input-placeholder { /* WebKit browsers*/
    font-weight: normal;
}

input:-moz-placeholder { /* Mozilla Firefox 4 to 18*/
    font-weight: normal;
}

input::-moz-placeholder { /* Mozilla Firefox 19+*/
    font-weight: normal;
}

input:-ms-input-placeholder { /* Internet Explorer 10+*/
    font-weight: normal;
}

.shopSearchInput > .searchInput {
    font-size: 12px;
    color: #000;
    width: 496px;
    height: 20px;
    line-height: 20px;
    padding: 5px 3px 5px 5px;
    border: none;
    font-weight: 900;
    outline: none;
    float: left;
}

.shopSearchInput > .searchBtn {
    width: 90px;
    height: 30px;
    font-size: 16px;
    cursor: pointer;
    color: #ffffff;
    background-color: #FF0036;
    overflow: hidden;
    border: 0;
    font-family: Arial, serif;
    float: left;
}

.content {
    width: 1230px;
    margin: auto;
    min-height: 400px;
    padding-bottom: 60px;
    color: #666;
}

.content > .take-delivery {
    margin-top: 10px;
}

.take-delivery > .summary-status {
    background: url(../../images/fore/WebsiteImage/T13iv.XiFdXXa94Hfd-32-32.png) no-repeat scroll 30px 30px transparent;
    padding: 27px 20px 27px 76px;
    border: 1px solid #e5e5e5;
    margin-bottom: 10px;
    color: #333;
    font-family: Arial, serif;
}

.summary-status > h2 {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
}

.summary-status > .successInfo {
    margin-left: -46px;
    padding: 0 0 27px 46px;
}

.successInfo > .info-rate-coin {
    padding: 6px 0 0 16px;
}

.info-rate-coin > li {
    list-style: disc;
    padding-left: 10px;
    font-size: 12px;
}

.info-rate-coin > li > .J_makePoint {
    display: block;
    margin: 8px 0;
    width: 326px;
    min-height: 100px;
    max-height: 120px;
    overflow: hidden;
    padding: 8px;
    border: 2px solid #efece8;
    background: #fcfaf9;
    text-align: left;
    text-decoration: none;
    color: #666;
}

.J_makePoint > img {
    float: left;
    margin-right: 10px;
}

.J_makePoint > p {
    margin: 7px 0 10px;
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.J_makePoint > span {
    padding: 0 22px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-weight: 700;
    background-color: #950b00;
    display: inline-block;
    cursor: pointer;
    color: white;
    border-radius: 3px;
}

.summary-status > p {
    font-size: 12px;
}