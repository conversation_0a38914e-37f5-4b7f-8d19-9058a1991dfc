<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.ReviewMapper">
    <resultMap id="reviewMap" type="com.xq.tmall.entity.Review">
        <id column="review_id" property="review_id"/>
        <result column="review_content" property="review_content"/>
        <result column="review_createDate" property="review_createDate"/>
        <association property="review_user" javaType="com.xq.tmall.entity.User">
            <id property="user_id" column="review_user_id"/>
            <result property="user_name" column="user_name"/>
        </association>
        <association property="review_product" javaType="com.xq.tmall.entity.Product">
            <id property="product_id" column="review_product_id"/>
            <result property="product_name" column="product_name"/>
        </association>
        <association property="review_orderItem" javaType="com.xq.tmall.entity.ProductOrderItem">
            <id property="productOrderItem_id" column="review_orderItem_id"/>
        </association>
    </resultMap>

    <insert id="insertOne" parameterType="com.xq.tmall.entity.Review">
        INSERT review (review_content,review_createDate,review_user_id,review_product_id,review_orderItem_id)
            VALUES (#{review.review_content},
            #{review.review_createDate},
        #{review.review_user.user_id},
        #{review.review_product.product_id},
        #{review.review_orderItem.productOrderItem_id})
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.Review">
        UPDATE review
        <set>
            <if test="review.review_content != null">review_content = #{review.review_content},</if>
            <if test="review.review_createDate != null">review_createDate = #{review.review_createDate}</if>
        </set>
        <where>
            review_id = #{review.review_id}
        </where>
    </update>
    <delete id="deleteList" parameterType="java.util.ArrayList">
        DELETE FROM review
        <where>
            review_id IN
            <foreach collection="review_id_list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>

    <delete id="deleteData" parameterType="java.lang.Integer">
        DELETE FROM review
        <where>
            review_id =#{id}
        </where>
    </delete>
    <select id="selectReviewList" resultMap="reviewMap">
        SELECT r.review_id,r.review_content, date_format(r.review_createDate,'%Y-%m-%d %H:%i:%s') as review_createDate,u.user_name,p.product_name,r.review_user_id,r.review_product_id FROM review r
        left join user u on r.review_user_id=u.user_id left join product p on r.review_product_id=p.product_id
        <if test="review != null">
            <where>
                <if test="review.review_content != null">
                    AND r.review_content like concat('%', #{review.review_content}, '%')
                </if>
                <if test="review.review_createDate != null and review.review_createDate != ''">
                    AND date_format(r.review_createDate,'%Y-%m-%d') = #{review.review_createDate}
                </if>
                <if test="review.review_user!= null and review.review_user.user_name!=null ">
                    AND u.user_name like concat('%', #{review.review_user.user_name}, '%')
                </if>
                <if test="review.review_product!= null and review.review_product.product_name!=null ">
                    AND p.product_name like concat('%', #{review.review_product.product_name}, '%')
                </if>
            </where>
        </if>
        <if test="orderUtil != null">
            ORDER BY ${orderUtil.orderBy}
            <if test="orderUtil.isDesc">desc</if>
        </if>
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectByUserId" resultMap="reviewMap">
        SELECT review_id,review_content,date_format(review_createDate,'%Y-%m-%d %H:%i:%s') as review_createDate,review_user_id,review_product_id FROM review
        <where>
            review_user_id = #{user_id}
        </where>
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectByProductId" resultMap="reviewMap">
        SELECT review_id,review_content,date_format(review_createDate,'%Y-%m-%d %H:%i:%s') as review_createDate,review_user_id,review_product_id FROM review
        <where>
            review_product_id = #{product_id}
        </where>
        ORDER BY review_id desc
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectOne" resultMap="reviewMap" parameterType="java.lang.Integer">
        SELECT r.review_id,r.review_content,date_format(r.review_createDate,'%Y-%m-%d %H:%i:%s') as review_createDate,r.review_user_id,r.review_product_id,u.user_name,p.product_name FROM review r
        left join user u on r.review_user_id=u.user_id left join product p on r.review_product_id=p.product_id
        <where>
            review_id = #{review_id}
        </where>
    </select>
    <select id="selectTotal" resultType="java.lang.Integer" parameterType="com.xq.tmall.entity.Review">
        SELECT COUNT(*) FROM review r
        left join user u on r.review_user_id=u.user_id left join product p on r.review_product_id=p.product_id
        <if test="review != null">
            <where>
                <if test="review.review_content != null">
                    AND r.review_content like concat('%', #{review.review_content}, '%')
                </if>
                <if test="review.review_createDate != null and review.review_createDate != ''">
                    AND date_format(r.review_createDate,'%Y-%m-%d') = #{review.review_createDate}
                </if>
                <if test="review.review_user!= null and review.review_user.user_name!=null ">
                    AND u.user_name like concat('%', #{review.review_user.user_name}, '%')
                </if>
                <if test="review.review_product!= null and review.review_product.product_name!=null ">
                    AND p.product_name like concat('%', #{review.review_product.product_name}, '%')
                </if>
            </where>
        </if>
    </select>
    <select id="selectTotalByUserId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(*) FROM review
        <where>
            review_user_id = #{user_id}
        </where>
    </select>
    <select id="selectTotalByProductId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(*) FROM review
        <where>
            review_product_id = #{product_id}
        </where>
    </select>
    <select id="selectTotalByOrderItemId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        SELECT COUNT(*) FROM review
        <where>
            review_orderItem_id = #{productOrderItem_id}
        </where>
    </select>

</mapper>