package com.xq.tmall.controller.fore;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.toolkit.StringUtils;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.entity.Address;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.AddressService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 前台天猫-用户
 */
@Api(tags = "前台天猫-用户")
@Controller
@RequiredArgsConstructor
public class ForeUserController extends BaseController {
    private final AddressService addressService;
    private final UserService userService;



    // 前台天猫-用户更换头像
    @ApiOperation(value = "前台天猫-用户更换头像", notes = "前台天猫-用户更换头像")
    @ResponseBody
    @PostMapping(value = "user/uploadUserHeadImage", produces = "application/json;charset=utf-8")
    public String uploadUserHeadImage(@RequestParam MultipartFile file, HttpServletRequest request
    ) {
        String originalFileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(originalFileName)) {
            throw new RuntimeException("上传失败！");
        }
        // 获取图片原始文件名：{}, originalFileName
        String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));
        String fileName = UUID.randomUUID() + extension;
        String filePath = request.getServletContext().getRealPath("/") + "res/images/item/userProfilePicture/" + fileName;
        // 文件上传路径：{}, filePath
        JSONObject jsonObject = new JSONObject();
        try {
            // 文件上传中...
            file.transferTo(new File(filePath));
            // 文件上传成功！
            jsonObject.put("success", true);
            jsonObject.put("fileName", fileName);
        } catch (IOException e) {
            logger.warn("文件上传失败！");
            e.printStackTrace();
            jsonObject.put("success", false);
        }
        return String.valueOf(jsonObject);
    }

    // 前台天猫-用户详情更新
    @ApiOperation(value = "前台天猫-用户详情更新", notes = "前台天猫-用户详情更新")
    @PostMapping(value = "user/update", produces = "application/json;charset=utf-8")
    public String userUpdate(HttpServletRequest request, Map<String, Object> map,
                             @RequestParam(value = "user_nickname") String user_nickname  /*用户昵称 */,
                             @RequestParam(value = "user_realname") String user_realname  /*真实姓名*/,
                             @RequestParam(value = "user_gender") String user_gender  /*用户性别*/,
                             @RequestParam(value = "user_birthday") String user_birthday /*用户生日*/,
                             @RequestParam(value = "user_address") String user_address  /*用户所在地 */,
                             @RequestParam(value = "user_profile_picture_src", required = false) String user_profile_picture_src /* 用户头像*/,
                             @RequestParam(value = "user_password") String user_password/* 用户密码 */
    ) {
        // 检查用户是否登录
        User user = checkUser(request);
        if (user != null) {
            // 获取用户信息
            map.put("user", user);
        } else {
            return "redirect:/login";
        }
        // 创建用户对象
        if (user_profile_picture_src != null && "".equals(user_profile_picture_src)) {
            user_profile_picture_src = null;
        }
        User userUpdate = new User();
        userUpdate.setUser_id(user.getUser_id());
        userUpdate.setUser_nickname(user_nickname);
        userUpdate.setUser_realname(user_realname);
        userUpdate.setUser_gender(Byte.valueOf(user_gender));
        userUpdate.setUser_birthday(user_birthday);
        Address address = new Address();
        address.setAddress_areaId(user_address);
        userUpdate.setUser_address(address);
        userUpdate.setUser_profile_picture_src(user_profile_picture_src);
        userUpdate.setUser_password(user_password);
        // 执行修改
        if (userService.update(userUpdate)) {
            // 修改成功!跳转到用户详情页面
            return "redirect:/userDetails";
        }
        throw new RuntimeException();
    }

    // 获取用户信息-API接口
    @ApiOperation(value = "获取用户信息", notes = "获取当前登录用户的详细信息")
    @GetMapping(value = "api/user/info", produces = "application/json;charset=utf-8")
    @ResponseBody
    public Result<Map<String, Object>> getUserInfo(HttpServletRequest request) {
        try {
            // 调试信息
            String authHeader = request.getHeader("Authorization");
            logger.info("获取用户信息请求，Authorization头：{}", authHeader);

            // 检查用户是否登录
            User user = checkUser(request);
            if (user == null) {
                logger.warn("用户认证失败，返回401");
                return createUserAuthFailureResponse();
            }

            logger.info("用户认证成功，用户ID：{}", user.getUser_id());

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("user", user);

            // 获取用户所在地区级地址
            try {
                if (user.getUser_address() != null && user.getUser_address().getAddress_areaId() != null) {
                    String districtAddressId = user.getUser_address().getAddress_areaId();
                    Address districtAddress = addressService.get(districtAddressId);
                    if (districtAddress != null && districtAddress.getAddress_regionId() != null) {
                        // 获取市级地址信息
                        Address cityAddress = addressService.get(districtAddress.getAddress_regionId().getAddress_areaId());
                        if (cityAddress != null && cityAddress.getAddress_regionId() != null) {
                            // 获取其他地址信息
                            List<Address> addressList = addressService.getRoot();
                            List<Address> cityList = addressService.getList(null, cityAddress.getAddress_regionId().getAddress_areaId());
                            List<Address> districtList = addressService.getList(null, cityAddress.getAddress_areaId());

                            responseData.put("addressList", addressList);
                            responseData.put("cityList", cityList);
                            responseData.put("districtList", districtList);
                            responseData.put("addressId", cityAddress.getAddress_regionId().getAddress_areaId());
                            responseData.put("cityAddressId", cityAddress.getAddress_areaId());
                            responseData.put("districtAddressId", districtAddressId);
                        }
                    }
                } else {
                    // 如果用户没有地址信息，提供默认的地址列表
                    List<Address> addressList = addressService.getRoot();
                    responseData.put("addressList", addressList);
                    responseData.put("cityList", new ArrayList<>());
                    responseData.put("districtList", new ArrayList<>());
                }
            } catch (Exception e) {
                logger.warn("获取地址信息失败：{}", e.getMessage());
                // 提供空的地址列表，不影响用户基本信息的获取
                responseData.put("addressList", new ArrayList<>());
                responseData.put("cityList", new ArrayList<>());
                responseData.put("districtList", new ArrayList<>());
            }

            return Result.success("获取用户信息成功", responseData);
        } catch (Exception e) {
            logger.error("获取用户信息失败：{}", e.getMessage());
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    // 更新用户信息-API接口
    @ApiOperation(value = "更新用户信息", notes = "更新当前登录用户的信息")
    @PostMapping(value = "api/user/update", produces = "application/json;charset=utf-8")
    @ResponseBody
    public Result<String> updateUserInfo(HttpServletRequest request,
                                       @RequestParam(value = "user_nickname") String user_nickname,
                                       @RequestParam(value = "user_realname") String user_realname,
                                       @RequestParam(value = "user_gender") String user_gender,
                                       @RequestParam(value = "user_birthday") String user_birthday,
                                       @RequestParam(value = "user_address") String user_address,
                                       @RequestParam(value = "user_profile_picture_src", required = false) String user_profile_picture_src,
                                       @RequestParam(value = "user_password") String user_password) {
        try {
            // 检查用户是否登录
            User user = checkUser(request);
            if (user == null) {
                return createUserAuthFailureResponse();
            }

            // 验证输入参数
            if (user_nickname == null || user_nickname.trim().isEmpty()) {
                return Result.error("昵称不能为空");
            }
            if (user_realname == null || user_realname.trim().isEmpty()) {
                return Result.error("真实姓名不能为空");
            }
            if (user_password == null || user_password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }

            // 创建用户对象
            if (user_profile_picture_src != null && "".equals(user_profile_picture_src)) {
                user_profile_picture_src = null;
            }

            User userUpdate = new User();
            userUpdate.setUser_id(user.getUser_id());
            userUpdate.setUser_nickname(user_nickname.trim());
            userUpdate.setUser_realname(user_realname.trim());
            userUpdate.setUser_gender(Byte.valueOf(user_gender));
            userUpdate.setUser_birthday(user_birthday);

            Address address = new Address();
            address.setAddress_areaId(user_address);
            userUpdate.setUser_address(address);
            userUpdate.setUser_profile_picture_src(user_profile_picture_src);
            userUpdate.setUser_password(user_password.trim());

            // 执行修改
            if (userService.update(userUpdate)) {
                return Result.success("用户信息更新成功");
            } else {
                return Result.error("用户信息更新失败");
            }
        } catch (Exception e) {
            logger.error("更新用户信息失败：{}", e.getMessage());
            return Result.error("更新用户信息失败：" + e.getMessage());
        }
    }

    // 上传用户头像-API接口
    @ApiOperation(value = "上传用户头像", notes = "上传用户头像图片")
    @PostMapping(value = "api/user/uploadAvatar", produces = "application/json;charset=utf-8")
    @ResponseBody
    public Result<Map<String, Object>> uploadUserAvatar(@RequestParam MultipartFile file, HttpServletRequest request) {
        try {
            // 检查用户是否登录
            User user = checkUser(request);
            if (user == null) {
                return createUserAuthFailureResponse();
            }

            String originalFileName = file.getOriginalFilename();
            if (StringUtils.isEmpty(originalFileName)) {
                return Result.error("请选择要上传的文件");
            }

            // 验证文件类型
            String extension = originalFileName.substring(originalFileName.lastIndexOf('.'));
            if (!extension.toLowerCase().matches("\\.(jpg|jpeg|png|gif)$")) {
                return Result.error("只支持jpg、jpeg、png、gif格式的图片");
            }

            // 验证文件大小（500KB）
            if (file.getSize() > 512000) {
                return Result.error("图片大小不能超过500KB");
            }

            String fileName = UUID.randomUUID() + extension;
            String filePath = request.getServletContext().getRealPath("/") + "res/images/item/userProfilePicture/" + fileName;

            // 文件上传
            file.transferTo(new File(filePath));

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("fileName", fileName);
            responseData.put("filePath", "/res/images/item/userProfilePicture/" + fileName);

            return Result.success("头像上传成功", responseData);
        } catch (IOException e) {
            logger.error("头像上传失败：{}", e.getMessage());
            return Result.error("头像上传失败：" + e.getMessage());
        } catch (Exception e) {
            logger.error("头像上传异常：{}", e.getMessage());
            return Result.error("头像上传异常：" + e.getMessage());
        }
    }
}
