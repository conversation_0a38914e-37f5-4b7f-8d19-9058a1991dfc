package com.xq.tmall.controller.fore;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 前台页面跳转控制器
 * 专门处理JSP页面跳转，不涉及数据处理
 */
@Api(tags = "前台页面跳转")
@Controller
public class ForePageController {

    // 转到前台天猫-主页
    @ApiOperation(value = "转到前台天猫-主页", notes = "转到前台天猫-主页")
    @GetMapping(value = {"", "/", "home"})
    public String goToHomePage() {
        return "fore/homePage";
    }

    // 转到前台天猫-错误页
    @ApiOperation(value = "转到前台天猫-错误页", notes = "转到前台天猫-错误页")
    @GetMapping(value = "error")
    public String goToErrorPage() {
        return "fore/errorPage";
    }

    // 转到前台天猫-登录页
    @ApiOperation(value = "转到前台天猫-登录页", notes = "转到前台天猫-登录页")
    @GetMapping(value = "login")
    public String goToLoginPage() {
        return "fore/loginPage";
    }

    // 转到前台天猫-注册页
    @ApiOperation(value = "转到前台天猫-注册页", notes = "转到前台天猫-注册页")
    @GetMapping(value = "register")
    public String goToRegisterPage() {
        return "fore/register";
    }

    // 转到前台天猫-用户详情页
    @ApiOperation(value = "转到前台天猫-用户详情页", notes = "转到前台天猫-用户详情页")
    @GetMapping(value = "userDetails")
    public String goToUserDetailsPage() {
        return "fore/userDetails";
    }


    // 转到前台天猫-订单详情页
    @ApiOperation(value = "转到前台天猫-订单详情页", notes = "转到前台天猫-订单详情页")
    @GetMapping(value = "order/{oid}")
    public String goToOrderDetailsPage() {
        return "fore/orderDetailsPage";
    }

    // 转到前台天猫-结算页
    @ApiOperation(value = "转到前台天猫-结算页", notes = "转到前台天猫-结算页")
    @GetMapping(value = "settlement")
    public String goToSettlementPage() {
        return "fore/settlementPage";
    }

    // 转到前台天猫-支付页
    @ApiOperation(value = "转到前台天猫-支付页", notes = "转到前台天猫-支付页")
    @GetMapping(value = "pay/{oid}")
    public String goToPayPage() {
        return "fore/payPage";
    }

    // 转到前台天猫-支付成功页
    @ApiOperation(value = "转到前台天猫-支付成功页", notes = "转到前台天猫-支付成功页")
    @GetMapping(value = "paysuccess")
    public String goToPaySuccessPage() {
        return "fore/productPaySuccessPage";
    }


}
