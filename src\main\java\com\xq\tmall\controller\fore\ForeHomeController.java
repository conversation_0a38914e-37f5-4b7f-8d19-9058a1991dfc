package com.xq.tmall.controller.fore;

import com.baomidou.mybatisplus.toolkit.CollectionUtils;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.entity.Category;
import com.xq.tmall.entity.Product;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.CategoryService;
import com.xq.tmall.service.ProductImageService;
import com.xq.tmall.service.ProductService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.JwtUtil;
import com.xq.tmall.util.OrderUtil;
import com.xq.tmall.util.PageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前台天猫-主页
 */
@Api(tags = "前台天猫-主页")
@RestController
@RequiredArgsConstructor
public class ForeHomeController extends BaseController {
    private final UserService userService;
    private final CategoryService categoryService;
    private final ProductService productService;
    private final ProductImageService productImageService;



    // 获取首页数据-API接口
    @ApiOperation(value = "获取首页数据", notes = "获取首页数据-JSON格式")
    @GetMapping(value = "api/home", produces = "application/json;charset=utf-8")
    public Result<Map<String, Object>> getHomeData(@RequestHeader(value = "Authorization", required = false) String token) {
        try {
            // 创建响应数据对象
            Map<String, Object> responseData = new HashMap<>();

            // 检查用户是否登录（通过JWT Token）
            User user = null;
            if (token != null && token.startsWith("Bearer ")) {
                String jwtToken = token.substring(7);
                if (JwtUtil.validateToken(jwtToken)) {
                    Integer userId = JwtUtil.getUserIdFromToken(jwtToken);
                    if (userId != null) {
                        user = userService.get(userId);
                    }
                }
            }
            responseData.put("user", user);

            // 获取产品分类列表
            List<Category> categoryList = categoryService.getList(null, null);
            // 获取每个分类下的产品列表
            if (CollectionUtils.isNotEmpty(categoryList)) {
                for (Category category : categoryList) {
                    // 获取分类下的产品集合，按产品ID倒序排序
                    Product product1 = new Product();
                    product1.setProduct_category(category);
                    List<Product> productList = productService.getList(product1, new Byte[]{0, 2}, new OrderUtil("product_id", true), new PageUtil(0, 8));
                    if (CollectionUtils.isNotEmpty(productList)) {
                        for (Product product : productList) {
                            // 获取产品预览图片信息
                            product.setSingleProductImageList(productImageService.getList(product.getProduct_id(), (byte) 0, new PageUtil(0, 1)));
                        }
                    }
                    category.setProductList(productList);
                }
            }
            responseData.put("categoryList", categoryList);

            // 获取促销产品列表
            List<Product> specialProductList = productService.getList(null, new Byte[]{2}, null, new PageUtil(0, 6));
            responseData.put("specialProductList", specialProductList);

            return Result.success("首页数据获取成功", responseData);
        } catch (Exception e) {
            return Result.error("获取首页数据失败：" + e.getMessage());
        }
    }

    // 获取主页分类下产品信息-API接口
    @ApiOperation(value = "获取主页分类下产品信息", notes = "获取主页分类下产品信息")
    @GetMapping(value = "product/nav/{category_id}", produces = "application/json;charset=utf-8")
    public Result<Map<String, Object>> getProductByNav(@PathVariable("category_id") Integer category_id) {
        try {
            if (category_id == null) {
                return Result.error("分类ID不能为空");
            }

            // 获取分类ID对应的产品标题数据
            Category category1 = new Category();
            category1.setCategory_id(category_id);
            Product product = new Product();
            product.setProduct_category(category1);
            List<Product> productList = productService.getTitle(product, new PageUtil(0, 40));

            // 将产品列表按每5个分组
            List<List<Product>> complexProductList = new ArrayList<>(8);
            List<Product> products = new ArrayList<>(5);
            if (CollectionUtils.isNotEmpty(productList)) {
                for (int i = 0; i < productList.size(); i++) {
                    // 如果临时集合中产品数达到5个，加入到产品二维集合中，并重新实例化临时集合
                    if (i % 5 == 0) {
                        complexProductList.add(products);
                        products = new ArrayList<>(5);
                    }
                    products.add(productList.get(i));
                }
            }
            complexProductList.add(products);

            // 构建响应数据
            Category category = new Category();
            category.setCategory_id(category_id);
            category.setComplexProductList(complexProductList);

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("success", true);
            responseData.put("category", category);

            return Result.success("获取分类产品成功", responseData);
        } catch (Exception e) {
            return Result.error("获取分类产品失败：" + e.getMessage());
        }
    }
}
