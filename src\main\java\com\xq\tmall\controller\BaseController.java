package com.xq.tmall.controller;

import com.xq.tmall.entity.Admin;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.AdminService;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.JwtUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;

/**
 * 基控制器
 * 重构后使用JWT认证替代session认证
 */
public class BaseController {
    //log4j
    protected Logger logger = LogManager.getLogger(LogManager.ROOT_LOGGER_NAME);

    @Autowired
    protected AdminService adminService;

    @Autowired
    protected UserService userService;

    /**
     * 从请求头中提取JWT Token
     * @param request HTTP请求对象
     * @return JWT Token字符串，如果不存在则返回null
     */
    protected String extractTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }

    /**
     * 检查管理员权限（基于JWT Token）
     * @param token JWT Token
     * @return 管理员对象，如果验证失败返回null
     */
    protected Admin checkAdmin(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.info("无管理权限，JWT Token为空");
            return null;
        }

        try {
            // 验证JWT Token
            if (!JwtUtil.validateToken(token)) {
                logger.info("无管理权限，JWT Token无效");
                return null;
            }

            // 从Token中获取用户ID
            Integer userId = JwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                logger.info("无管理权限，无法从Token中获取用户ID");
                return null;
            }

            // 查询管理员信息
            Admin admin = adminService.get(null, userId);
            if (admin == null) {
                logger.info("无管理权限，管理员不存在，用户ID：{}", userId);
                return null;
            }

            logger.info("管理员权限验证成功，管理员ID：{}", admin.getAdmin_id());
            return admin;
        } catch (Exception e) {
            logger.error("管理员权限验证失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查用户是否登录（基于JWT Token）
     * @param token JWT Token
     * @return 用户对象，如果验证失败返回null
     */
    protected User checkUser(String token) {
        if (token == null || token.trim().isEmpty()) {
            logger.info("用户未登录，JWT Token为空");
            return null;
        }

        try {
            // 验证JWT Token
            if (!JwtUtil.validateToken(token)) {
                logger.info("用户未登录，JWT Token无效");
                return null;
            }

            // 从Token中获取用户ID
            Integer userId = JwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                logger.info("用户未登录，无法从Token中获取用户ID");
                return null;
            }

            // 查询用户信息
            User user = userService.get(userId);
            if (user == null) {
                logger.info("用户未登录，用户不存在，用户ID：{}", userId);
                return null;
            }

            logger.info("用户已登录，用户ID：{}", user.getUser_id());
            return user;
        } catch (Exception e) {
            logger.error("用户登录验证失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查管理员权限（从HTTP请求中提取Token）
     * @param request HTTP请求对象
     * @return 管理员对象，如果验证失败返回null
     */
    protected Admin checkAdmin(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        return checkAdmin(token);
    }

    /**
     * 检查用户是否登录（从HTTP请求中提取Token）
     * @param request HTTP请求对象
     * @return 用户对象，如果验证失败返回null
     */
    protected User checkUser(HttpServletRequest request) {
        String token = extractTokenFromRequest(request);
        return checkUser(token);
    }

    /**
     * 创建认证失败的统一响应
     * @param message 错误消息
     * @return Result对象
     */
    protected <T> Result<T> createAuthFailureResponse(String message) {
        return Result.error(401, message);
    }

    /**
     * 创建管理员权限验证失败的响应
     * @return Result对象
     */
    protected <T> Result<T> createAdminAuthFailureResponse() {
        return createAuthFailureResponse("无管理员权限，请先登录");
    }

    /**
     * 创建用户登录验证失败的响应
     * @return Result对象
     */
    protected <T> Result<T> createUserAuthFailureResponse() {
        return createAuthFailureResponse("用户未登录，请先登录");
    }
}
