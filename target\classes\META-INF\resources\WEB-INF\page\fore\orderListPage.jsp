<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="include/header.jsp" %>
<head>
    <link href="${pageContext.request.contextPath}/res/css/fore/fore_orderList.css" rel="stylesheet"/>
    <title>已买到的宝贝</title>

    <!-- 传递参数给JavaScript -->
    <script>
        window.orderParams = {
            index: ${param.index != null ? param.index : 0},
            count: ${param.count != null ? param.count : 5},
            status: '${param.status != null ? param.status : ""}'
        };
    </script>
</head>
<body>

<script>
$(document).ready(function() {
    // 初始化订单列表页面
    OrderListPage.init();
});

const OrderListPage = {
    // 当前参数
    currentParams: {
        index: 0,
        count: 5,
        status: null
    },

    // 当前数据
    currentData: null,

    // 初始化
    init: function() {
        // 首先检查用户是否登录
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        if (!token) {
            window.location.href = '/tmall/login';
            return;
        }

        this.parseUrlParams();
        this.bindEvents();
        this.loadOrderData();
    },

    // 解析URL参数
    parseUrlParams: function() {
        var self = this;
        // 优先使用服务器传递的参数
        if (window.orderParams) {
            self.currentParams.index = window.orderParams.index || 0;
            self.currentParams.count = window.orderParams.count || 5;
            self.currentParams.status = window.orderParams.status !== '' ? parseInt(window.orderParams.status) : null;
        }

        // 解析URL参数作为备用
        var urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('status')) {
            self.currentParams.status = parseInt(urlParams.get('status'));
        }
    },

    // 绑定事件
    bindEvents: function() {
        var self = this;

        // 绑定取消订单确认按钮事件
        $('#btn-ok').click(function () {
            var orderCode = $("#order_id_hidden").val();
            $.ajax({
                url: "/tmall/order/close/" + orderCode,
                type: "PUT",
                data: null,
                dataType: "json",
                success: function (data) {
                    if (data.success !== true) {
                        alert("订单处理异常，请稍候再试！");
                    }
                    // 重新加载当前页面数据
                    self.loadOrderData();
                    $('#modalDiv').modal('hide');
                },
                error: function () {
                    alert("订单取消出现问题，请稍后再试！");
                    self.loadOrderData();
                    $('#modalDiv').modal('hide');
                }
            });
        });
    },

    // 加载订单数据
    loadOrderData: function() {
        var self = this;
        var token = localStorage.getItem('token') || sessionStorage.getItem('token');

        // 显示加载中
        $('#loadingIndicator').show();
        $('#orderListContainer').hide();
        $('#noOrdersMessage').hide();

        // 构建请求参数
        var url = '/tmall/api/order/' + self.currentParams.index + '/' + self.currentParams.count;
        if (self.currentParams.status !== null) {
            url += '?status=' + self.currentParams.status;
        }

        $.ajax({
            url: url,
            type: "GET",
            headers: {
                'Authorization': 'Bearer ' + token
            },
            dataType: "json",
            success: function (response) {
                $('#loadingIndicator').hide();

                if (response.code === 200 && response.data) {
                    self.currentData = response.data;
                    self.renderOrderList(response.data);
                } else {
                    if (response.code === 500 && response.message === "用户未登录") {
                        localStorage.removeItem('token');
                        sessionStorage.removeItem('token');
                        window.location.href = "/tmall/login";
                    } else {
                        self.showNoOrdersMessage(response.message || "获取订单列表失败");
                    }
                }
            },
            error: function (xhr, status, error) {
                $('#loadingIndicator').hide();
                console.error('加载订单列表失败:', error);
                if (xhr.status === 401) {
                    localStorage.removeItem('token');
                    sessionStorage.removeItem('token');
                    window.location.href = "/tmall/login";
                } else {
                    self.showNoOrdersMessage('网络错误，请稍后重试');
                }
            }
        });
    },

    // 渲染订单列表
    renderOrderList: function(data) {
        console.log('渲染订单列表，数据:', data);

        if (!data.productOrderList || data.productOrderList.length === 0) {
            this.showNoOrdersMessage();
            return;
        }

        // 渲染分类列表
        this.renderCategoryList(data.categoryList);

        // 渲染订单
        this.renderOrders(data.productOrderList);

        // 渲染分页
        this.renderPagination(data);

        // 更新标签页选中状态
        this.updateTabSelection();

        // 显示订单列表容器
        $('#orderListContainer').show();
    },

    // 渲染分类列表
    renderCategoryList: function(categoryList) {
        if (!categoryList || categoryList.length === 0) return;

        var html = '';
        for (var i = 0; i < categoryList.length; i++) {
            var category = categoryList[i];
            html += '<li><a href="/tmall/product?category_id=' + category.category_id + '">' + category.category_name + '</a></li>';
        }
        $('#categoryList').html(html);
    },

    // 显示无订单消息
    showNoOrdersMessage: function(message) {
        if (message) {
            $('#noOrdersMessage .error_msg').text(message);
        } else {
            $('#noOrdersMessage .error_msg').text('没有符合条件的订单，请尝试其他筛选条件。');
        }
        $('#noOrdersMessage').show();
    },

    // 取消订单
    closeOrder: function(orderCode) {
        if (isNaN(orderCode) || orderCode === null) {
            return;
        }
        $("#order_id_hidden").val(orderCode);
        $('#modalDiv').modal();
    },

    // 跳转到指定页
    goToPage: function(index) {
        if (index < 0 || (this.currentData && this.currentData.pageUtil && index >= this.currentData.pageUtil.totalPage)) {
            return; // 防止无效页码
        }
        this.currentParams.index = index;
        this.updateUrl();
        this.loadOrderData();
    },

    // 切换订单状态
    changeStatus: function(status) {
        this.currentParams.status = status;
        this.currentParams.index = 0; // 重置到第一页
        this.updateUrl();
        this.loadOrderData();
    },

    // 渲染订单
    renderOrders: function(productOrderList) {
        var html = '';

        for (var i = 0; i < productOrderList.length; i++) {
            var productOrder = productOrderList[i];
            var productOrderItemList = productOrder.productOrderItemList;

            // 订单信息行
            html += '<tr class="tr_order_info">';
            html += '<td colspan="6">';
            html += '<span class="span_pay_date">' + (productOrder.productOrder_pay_date || '') + '</span>';
            html += '<span class="span_order_code_title">订单号:</span>';
            html += '<span class="span_order_code">' + productOrder.productOrder_code + '</span>';
            html += '</td>';
            html += '</tr>';

            // 订单项行
            if (productOrderItemList && productOrderItemList.length > 0) {
                for (var j = 0; j < productOrderItemList.length; j++) {
                    var productOrderItem = productOrderItemList[j];
                    var product = productOrderItem.productOrderItem_product;
                    var unitPrice = (productOrderItem.productOrderItem_price / productOrderItem.productOrderItem_number).toFixed(2);

                    html += '<tr class="tr_orderItem_info">';

                    // 商品信息
                    html += '<td>';
                    if (product.singleProductImageList && product.singleProductImageList.length > 0) {
                        html += '<img class="orderItem_product_image" src="/tmall/res/images/item/productSinglePicture/' + product.singleProductImageList[0].productImage_src + '" style="width: 80px;height: 80px;"/>';
                    }
                    html += '<span class="orderItem_product_name">';
                    html += '<a href="/tmall/product/' + product.product_id + '">' + product.product_name + '</a>';
                    html += '</span>';
                    html += '</td>';

                    // 单价
                    html += '<td><span class="orderItem_product_price">￥' + unitPrice + '</span></td>';

                    // 数量
                    html += '<td><span class="orderItem_product_number">' + productOrderItem.productOrderItem_number + '</span></td>';

                    // 实付款
                    html += '<td class="td_order_content"><span class="orderItem_product_realPrice">￥' + productOrderItem.productOrderItem_price + '</span></td>';

                    // 第一个订单项显示订单状态和操作
                    if (j === 0) {
                        var rowspan = productOrderItemList.length;

                        // 交易状态
                        html += '<td class="td_order_content" rowspan="' + rowspan + '">';
                        html += OrderListPage.getStatusText(productOrder.productOrder_status);
                        html += '</td>';

                        // 交易操作
                        html += '<td class="td_order_content" rowspan="' + rowspan + '">';
                        html += OrderListPage.getActionButtons(productOrder);
                        html += '</td>';
                    }

                    // 评价按钮（仅对已完成且未评价的订单项显示）
                    if (productOrder.productOrder_status === 3 && productOrderItem.isReview !== null && !productOrderItem.isReview) {
                        html += '<td class="td_order_content">';
                        html += '<a class="order_btn review_btn" href="/tmall/review/' + productOrderItem.productOrderItem_id + '">评价</a>';
                        html += '</td>';
                    }

                    html += '</tr>';
                }
            }
        }

        $('#orderListBody').html(html);
    },

    // 获取订单状态文本
    getStatusText: function(status) {
        switch (status) {
            case 0:
                return '<span class="span_order_status" title="等待买家付款">等待买家付款</span>';
            case 1:
                return '<span class="span_order_status" title="买家已付款，等待卖家发货">等待卖家发货</span>';
            case 2:
                return '<span class="span_order_status" title="卖家已发货，等待买家确认">等待买家确认</span>';
            case 3:
                return '<span class="span_order_status" title="交易成功">交易成功</span>';
            default:
                return '<span class="td_error" title="交易关闭">交易关闭</span>';
        }
    },

    // 获取操作按钮
    getActionButtons: function(productOrder) {
        switch (productOrder.productOrder_status) {
            case 0:
                return '<a class="order_btn pay_btn" href="/tmall/order/pay/' + productOrder.productOrder_code + '">立即付款</a>' +
                       '<p class="order_close"><a class="order_close" href="javascript:void(0)" onclick="OrderListPage.closeOrder(\'' + productOrder.productOrder_code + '\')">取消订单</a></p>';
            case 1:
                return '<a class="order_btn delivery_btn" href="/tmall/order/delivery/' + productOrder.productOrder_code + '">提醒发货</a>';
            case 2:
                return '<a class="order_btn confirm_btn" href="/tmall/order/confirm/' + productOrder.productOrder_code + '">确认收货</a>';
            default:
                return '';
        }
    },

    // 渲染分页
    renderPagination: function(data) {
        var pageUtil = data.pageUtil;

        // 调试信息
        console.log('PageUtil对象:', pageUtil);
        if (pageUtil) {
            console.log('totalPage:', pageUtil.totalPage);
            console.log('index:', pageUtil.index);
            console.log('count:', pageUtil.count);
            console.log('total:', pageUtil.total);
        }

        if (!pageUtil) {
            console.log('PageUtil为空，不显示分页');
            $('#paginationTop').html('');
            $('#paginationBottom').html('');
            return;
        }

        if (pageUtil.totalPage <= 1) {
            console.log('总页数小于等于1，不显示分页');
            $('#paginationTop').html('');
            $('#paginationBottom').html('');
            return;
        }

        var html = '<div id="pageDiv"><ul>';

        // 首页按钮
        var firstDisabled = pageUtil.index === 0 ? 'class="disabled"' : '';
        html += '<li data-name="firstPage" ' + firstDisabled + '>';
        html += '<a href="javascript:void(0)" onclick="OrderListPage.goToPage(0)" aria-label="首页">';
        html += '<span aria-hidden="true">&laquo;</span></a></li>';

        // 上一页按钮
        var prevDisabled = pageUtil.index === 0 ? 'class="disabled"' : '';
        var prevIndex = pageUtil.index - 1;
        html += '<li data-name="prevPage" ' + prevDisabled + '>';
        html += '<a href="javascript:void(0)" onclick="OrderListPage.goToPage(' + prevIndex + ')" aria-label="上一页">';
        html += '<span aria-hidden="true">&lsaquo;</span></a></li>';

        // 页码按钮（显示当前页前后5页）
        var startPage = Math.max(0, pageUtil.index - 5);
        var endPage = Math.min(pageUtil.totalPage - 1, pageUtil.index + 5);

        for (var i = startPage; i <= endPage; i++) {
            var isCurrentPage = i === pageUtil.index ? 'class="pageThis"' : '';
            html += '<li ' + isCurrentPage + '>';
            html += '<a href="javascript:void(0)" onclick="OrderListPage.goToPage(' + i + ')">' + (i + 1) + '</a>';
            html += '</li>';
        }

        // 下一页按钮
        var nextDisabled = pageUtil.index >= pageUtil.totalPage - 1 ? 'class="disabled"' : '';
        var nextIndex = pageUtil.index + 1;
        html += '<li data-name="nextPage" ' + nextDisabled + '>';
        html += '<a href="javascript:void(0)" onclick="OrderListPage.goToPage(' + nextIndex + ')" aria-label="下一页">';
        html += '<span aria-hidden="true">&rsaquo;</span></a></li>';

        // 尾页按钮
        var lastDisabled = pageUtil.index >= pageUtil.totalPage - 1 ? 'class="disabled"' : '';
        var lastIndex = pageUtil.totalPage - 1;
        html += '<li data-name="lastPage" ' + lastDisabled + '>';
        html += '<a href="javascript:void(0)" onclick="OrderListPage.goToPage(' + lastIndex + ')" aria-label="尾页">';
        html += '<span aria-hidden="true">&raquo;</span></a></li>';

        html += '</ul></div>';

        $('#paginationTop').html(html);
        $('#paginationBottom').html(html);

        // 禁用已禁用的链接点击事件
        setTimeout(function() {
            $(".disabled>a,.pageThis>a").attr("onclick", null);
        }, 100);
    },

    // 更新标签页选中状态
    updateTabSelection: function() {
        $('.tabs_ul li').removeClass('tab_select');
        if (this.currentParams.status === null) {
            $('#tab_all').addClass('tab_select');
        } else {
            $('#tab_' + this.currentParams.status).addClass('tab_select');
        }
    },

    // 更新URL
    updateUrl: function() {
        var params = new URLSearchParams();

        if (this.currentParams.status !== null) {
            params.set('status', this.currentParams.status);
        }

        var newUrl = '/tmall/order/' + this.currentParams.index + '/' + this.currentParams.count +
                     (params.toString() ? '?' + params.toString() : '');
        window.history.pushState({}, '', newUrl);
    }
};

// 全局函数，保持向后兼容
function getPage(index) {
    OrderListPage.goToPage(index);
}

function changeStatus(status) {
    OrderListPage.changeStatus(status);
}

function closeOrder(orderCode) {
    OrderListPage.closeOrder(orderCode);
}
</script>
</head>
<body>
<nav>
    <%@ include file="include/navigator.jsp" %>
    <div class="header">
        <div id="mallLogo">
        </div>
        <div class="shopSearchHeader">
            <form action="${pageContext.request.contextPath}/product" method="get">
                <div class="shopSearchInput">
                    <input type="text" class="searchInput" name="product_name" placeholder="搜索 商品/品牌/店铺"
                           maxlength="50">
                    <input type="submit" value="搜 索" class="searchBtn">
                </div>
            </form>
            <ul id="categoryList">
                <!-- 分类列表将通过JavaScript动态加载 -->
            </ul>
        </div>
    </div>
</nav>

<div class="content">
    <!-- 加载中提示 -->
    <div id="loadingIndicator" style="text-align: center; padding: 50px;">
        <p>正在加载订单数据...</p>
    </div>

    <!-- 订单列表容器 -->
    <div id="orderListContainer" style="display: none;">
        <ul class="tabs_ul">
            <li id="tab_all"><a href="javascript:void(0)" onclick="OrderListPage.changeStatus(null)">所有订单</a></li>
            <li id="tab_0"><a href="javascript:void(0)" onclick="OrderListPage.changeStatus(0)" name="status=0">待付款</a></li>
            <li id="tab_1"><a href="javascript:void(0)" onclick="OrderListPage.changeStatus(1)" name="status=1">待发货</a></li>
            <li id="tab_2"><a href="javascript:void(0)" onclick="OrderListPage.changeStatus(2)" name="status=2">待收货</a></li>
            <li id="tab_3"><a href="javascript:void(0)" onclick="OrderListPage.changeStatus(3)" name="status=3">已完成</a></li>
        </ul>

        <div id="paginationTop">
            <!-- 分页信息将通过JavaScript动态加载 -->
        </div>

        <table class="table_orderList">
            <thead>
            <tr>
                <th>宝贝</th>
                <th width="80px">单价</th>
                <th width="80px">数量</th>
                <th width="140px">实付款</th>
                <th width="140px">交易状态</th>
                <th width="140px">交易操作</th>
            </tr>
            </thead>
            <tbody id="orderListBody">
                <!-- 订单列表将通过JavaScript动态加载 -->
            </tbody>
        </table>

        <div id="paginationBottom">
            <!-- 分页信息将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 无订单提示 -->
    <div id="noOrdersMessage" style="display: none;">
        <div class="no_search_result">
            <img src="${pageContext.request.contextPath}/res/images/fore/WebsiteImage/T1MQ1cXhtiXXXXXXXX-78-120.png"/>
            <span class="error_msg">没有符合条件的订单，请尝试其他筛选条件。</span>
        </div>
    </div>
</div>


<%-- 模态框 --%>
<div class="modal fade" id="modalDiv" tabindex="-1" role="dialog" aria-labelledby="modalDiv" aria-hidden="true"
     data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">提示</h4>
            </div>
            <div class="modal-body">您确定要取消该订单吗？取消订单后，不能恢复。</div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary" id="btn-ok">确定</button>
                <button type="button" class="btn btn-default" data-dismiss="modal" id="btn-close">关闭</button>
                <input type="hidden" id="order_id_hidden">
            </div>
        </div>
        <%-- /.modal-content --%>
    </div>
    <%-- /.modal --%>
</div>
<%@include file="include/footer_two.jsp" %>
<%@include file="include/footer.jsp" %>
</body>
