/*
SQLyog Ultimate v13.1.1 (64 bit)
MySQL - 5.5.27 : Database - tmalldemodb
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`tmalldemodb` /*!40100 DEFAULT CHARACTER SET latin1 */;

USE `tmalldemodb`;

/*Table structure for table `address` */

DROP TABLE IF EXISTS `address`;

CREATE TABLE `address` (
  `address_areaId` char(6) NOT NULL COMMENT '地区编码',
  `address_name` varchar(50) NOT NULL COMMENT '省市名称',
  `address_regionId` char(6) NOT NULL COMMENT '父级省市id',
  PRIMARY KEY (`address_areaId`) USING BTREE,
  KEY `address_regionId` (`address_regionId`) USING BTREE,
  CONSTRAINT `address_ibfk_1` FOREIGN KEY (`address_regionId`) REFERENCES `address` (`address_areaId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地址表';

/*Data for the table `address` */

insert  into `address`(`address_areaId`,`address_name`,`address_regionId`) values 
('110000','北京','110000'),
('110100','北京市','110000'),
('110101','东城区','110100'),
('110102','西城区','110100'),
('110105','朝阳区','110100'),
('110106','丰台区','110100'),
('110107','石景山区','110100'),
('110108','海淀区','110100'),
('110109','门头沟区','110100'),
('110111','房山区','110100'),
('110112','通州区','110100'),
('110113','顺义区','110100'),
('110114','昌平区','110100'),
('110115','大兴区','110100'),
('110116','怀柔区','110100'),
('110117','平谷区','110100'),
('110228','密云县','110100'),
('110229','延庆县','110100'),
('120000','天津','120000'),
('120100','天津市','120000'),
('120101','和平区','120100'),
('120102','河东区','120100'),
('120103','河西区','120100'),
('120104','南开区','120100'),
('120105','河北区','120100'),
('120106','红桥区','120100'),
('120110','东丽区','120100'),
('120111','西青区','120100'),
('120112','津南区','120100'),
('120113','北辰区','120100'),
('120114','武清区','120100'),
('120115','宝坻区','120100'),
('120116','滨海新区','120100'),
('120221','宁河县','120100'),
('120223','静海县','120100'),
('120225','蓟县','120100'),
('130000','河北省','130000'),
('130100','石家庄市','130000'),
('130102','长安区','130100'),
('130104','桥西区','130100'),
('130105','新华区','130100'),
('130107','井陉矿区','130100'),
('130108','裕华区','130100'),
('130109','藁城区','130100'),
('130110','鹿泉区','130100'),
('130111','栾城区','130100'),
('130121','井陉县','130100'),
('130123','正定县','130100'),
('130125','行唐县','130100'),
('130126','灵寿县','130100'),
('130127','高邑县','130100'),
('130128','深泽县','130100'),
('130129','赞皇县','130100'),
('130130','无极县','130100'),
('130131','平山县','130100'),
('130132','元氏县','130100'),
('130133','赵县','130100'),
('130181','辛集市','130100'),
('130183','晋州市','130100'),
('130184','新乐市','130100'),
('130200','唐山市','130000'),
('130202','路南区','130200'),
('130203','路北区','130200'),
('130204','古冶区','130200'),
('130205','开平区','130200'),
('130207','丰南区','130200'),
('130208','丰润区','130200'),
('130209','曹妃甸区','130200'),
('130223','滦县','130200'),
('130224','滦南县','130200'),
('130225','乐亭县','130200'),
('130227','迁西县','130200'),
('130229','玉田县','130200'),
('130281','遵化市','130200'),
('130283','迁安市','130200'),
('130300','秦皇岛市','130000'),
('130302','海港区','130300'),
('130303','山海关区','130300'),
('130304','北戴河区','130300'),
('130321','青龙满族自治县','130300'),
('130322','昌黎县','130300'),
('130323','抚宁县','130300'),
('130324','卢龙县','130300'),
('130400','邯郸市','130000'),
('130402','邯山区','130400'),
('130403','丛台区','130400'),
('130404','复兴区','130400'),
('130406','峰峰矿区','130400'),
('130421','邯郸县','130400'),
('130423','临漳县','130400'),
('130424','成安县','130400'),
('130425','大名县','130400'),
('130426','涉县','130400'),
('130427','磁县','130400'),
('130428','肥乡县','130400'),
('130429','永年县','130400'),
('130430','邱县','130400'),
('130431','鸡泽县','130400'),
('130432','广平县','130400'),
('130433','馆陶县','130400'),
('130434','魏县','130400'),
('130435','曲周县','130400'),
('130481','武安市','130400'),
('130500','邢台市','130000'),
('130502','桥东区','130500'),
('130503','桥西区','130500'),
('130521','邢台县','130500'),
('130522','临城县','130500'),
('130523','内丘县','130500'),
('130524','柏乡县','130500'),
('130525','隆尧县','130500'),
('130526','任县','130500'),
('130527','南和县','130500'),
('130528','宁晋县','130500'),
('130529','巨鹿县','130500'),
('130530','新河县','130500'),
('130531','广宗县','130500'),
('130532','平乡县','130500'),
('130533','威县','130500'),
('130534','清河县','130500'),
('130535','临西县','130500'),
('130581','南宫市','130500'),
('130582','沙河市','130500'),
('130600','保定市','130000'),
('130602','新市区','130600'),
('130603','北市区','130600'),
('130604','南市区','130600'),
('130621','满城县','130600'),
('130622','清苑县','130600'),
('130623','涞水县','130600'),
('130624','阜平县','130600'),
('130625','徐水县','130600'),
('130626','定兴县','130600'),
('130627','唐县','130600'),
('130628','高阳县','130600'),
('130629','容城县','130600'),
('130630','涞源县','130600'),
('130631','望都县','130600'),
('130632','安新县','130600'),
('130633','易县','130600'),
('130634','曲阳县','130600'),
('130635','蠡县','130600'),
('130636','顺平县','130600'),
('130637','博野县','130600'),
('130638','雄县','130600'),
('130681','涿州市','130600'),
('130682','定州市','130600'),
('130683','安国市','130600'),
('130684','高碑店市','130600'),
('130700','张家口市','130000'),
('130702','桥东区','130700'),
('130703','桥西区','130700'),
('130705','宣化区','130700'),
('130706','下花园区','130700'),
('130721','宣化县','130700'),
('130722','张北县','130700'),
('130723','康保县','130700'),
('130724','沽源县','130700'),
('130725','尚义县','130700'),
('130726','蔚县','130700'),
('130727','阳原县','130700'),
('130728','怀安县','130700'),
('130729','万全县','130700'),
('130730','怀来县','130700'),
('130731','涿鹿县','130700'),
('130732','赤城县','130700'),
('130733','崇礼县','130700'),
('130800','承德市','130000'),
('130802','双桥区','130800'),
('130803','双滦区','130800'),
('130804','鹰手营子矿区','130800'),
('130821','承德县','130800'),
('130822','兴隆县','130800'),
('130823','平泉县','130800'),
('130824','滦平县','130800'),
('130825','隆化县','130800'),
('130826','丰宁满族自治县','130800'),
('130827','宽城满族自治县','130800'),
('130828','围场满族蒙古族自治县','130800'),
('130900','沧州市','130000'),
('130902','新华区','130900'),
('130903','运河区','130900'),
('130921','沧县','130900'),
('130922','青县','130900'),
('130923','东光县','130900'),
('130924','海兴县','130900'),
('130925','盐山县','130900'),
('130926','肃宁县','130900'),
('130927','南皮县','130900'),
('130928','吴桥县','130900'),
('130929','献县','130900'),
('130930','孟村回族自治县','130900'),
('130981','泊头市','130900'),
('130982','任丘市','130900'),
('130983','黄骅市','130900'),
('130984','河间市','130900'),
('131000','廊坊市','130000'),
('131002','安次区','131000'),
('131003','广阳区','131000'),
('131022','固安县','131000'),
('131023','永清县','131000'),
('131024','香河县','131000'),
('131025','大城县','131000'),
('131026','文安县','131000'),
('131028','大厂回族自治县','131000'),
('131081','霸州市','131000'),
('131082','三河市','131000'),
('131100','衡水市','130000'),
('131102','桃城区','131100'),
('131121','枣强县','131100'),
('131122','武邑县','131100'),
('131123','武强县','131100'),
('131124','饶阳县','131100'),
('131125','安平县','131100'),
('131126','故城县','131100'),
('131127','景县','131100'),
('131128','阜城县','131100'),
('131181','冀州市','131100'),
('131182','深州市','131100'),
('140000','山西省','140000'),
('140100','太原市','140000'),
('140105','小店区','140100'),
('140106','迎泽区','140100'),
('140107','杏花岭区','140100'),
('140108','尖草坪区','140100'),
('140109','万柏林区','140100'),
('140110','晋源区','140100'),
('140121','清徐县','140100'),
('140122','阳曲县','140100'),
('140123','娄烦县','140100'),
('140181','古交市','140100'),
('140200','大同市','140000'),
('140202','城区','140200'),
('140203','矿区','140200'),
('140211','南郊区','140200'),
('140212','新荣区','140200'),
('140221','阳高县','140200'),
('140222','天镇县','140200'),
('140223','广灵县','140200'),
('140224','灵丘县','140200'),
('140225','浑源县','140200'),
('140226','左云县','140200'),
('140227','大同县','140200'),
('140300','阳泉市','140000'),
('140302','城区','140300'),
('140303','矿区','140300'),
('140311','郊区','140300'),
('140321','平定县','140300'),
('140322','盂县','140300'),
('140400','长治市','140000'),
('140402','城区','140400'),
('140411','郊区','140400'),
('140421','长治县','140400'),
('140423','襄垣县','140400'),
('140424','屯留县','140400'),
('140425','平顺县','140400'),
('140426','黎城县','140400'),
('140427','壶关县','140400'),
('140428','长子县','140400'),
('140429','武乡县','140400'),
('140430','沁县','140400'),
('140431','沁源县','140400'),
('140481','潞城市','140400'),
('140500','晋城市','140000'),
('140502','城区','140500'),
('140521','沁水县','140500'),
('140522','阳城县','140500'),
('140524','陵川县','140500'),
('140525','泽州县','140500'),
('140581','高平市','140500'),
('140600','朔州市','140000'),
('140602','朔城区','140600'),
('140603','平鲁区','140600'),
('140621','山阴县','140600'),
('140622','应县','140600'),
('140623','右玉县','140600'),
('140624','怀仁县','140600'),
('140700','晋中市','140000'),
('140702','榆次区','140700'),
('140721','榆社县','140700'),
('140722','左权县','140700'),
('140723','和顺县','140700'),
('140724','昔阳县','140700'),
('140725','寿阳县','140700'),
('140726','太谷县','140700'),
('140727','祁县','140700'),
('140728','平遥县','140700'),
('140729','灵石县','140700'),
('140781','介休市','140700'),
('140800','运城市','140000'),
('140802','盐湖区','140800'),
('140821','临猗县','140800'),
('140822','万荣县','140800'),
('140823','闻喜县','140800'),
('140824','稷山县','140800'),
('140825','新绛县','140800'),
('140826','绛县','140800'),
('140827','垣曲县','140800'),
('140828','夏县','140800'),
('140829','平陆县','140800'),
('140830','芮城县','140800'),
('140881','永济市','140800'),
('140882','河津市','140800'),
('140900','忻州市','140000'),
('140902','忻府区','140900'),
('140921','定襄县','140900'),
('140922','五台县','140900'),
('140923','代县','140900'),
('140924','繁峙县','140900'),
('140925','宁武县','140900'),
('140926','静乐县','140900'),
('140927','神池县','140900'),
('140928','五寨县','140900'),
('140929','岢岚县','140900'),
('140930','河曲县','140900'),
('140931','保德县','140900'),
('140932','偏关县','140900'),
('140981','原平市','140900'),
('141000','临汾市','140000'),
('141002','尧都区','141000'),
('141021','曲沃县','141000'),
('141022','翼城县','141000'),
('141023','襄汾县','141000'),
('141024','洪洞县','141000'),
('141025','古县','141000'),
('141026','安泽县','141000'),
('141027','浮山县','141000'),
('141028','吉县','141000'),
('141029','乡宁县','141000'),
('141030','大宁县','141000'),
('141031','隰县','141000'),
('141032','永和县','141000'),
('141033','蒲县','141000'),
('141034','汾西县','141000'),
('141081','侯马市','141000'),
('141082','霍州市','141000'),
('141100','吕梁市','140000'),
('141102','离石区','141100'),
('141121','文水县','141100'),
('141122','交城县','141100'),
('141123','兴县','141100'),
('141124','临县','141100'),
('141125','柳林县','141100'),
('141126','石楼县','141100'),
('141127','岚县','141100'),
('141128','方山县','141100'),
('141129','中阳县','141100'),
('141130','交口县','141100'),
('141181','孝义市','141100'),
('141182','汾阳市','141100'),
('150000','内蒙古自治区','150000'),
('150100','呼和浩特市','150000'),
('150102','新城区','150100'),
('150103','回民区','150100'),
('150104','玉泉区','150100'),
('150105','赛罕区','150100'),
('150121','土默特左旗','150100'),
('150122','托克托县','150100'),
('150123','和林格尔县','150100'),
('150124','清水河县','150100'),
('150125','武川县','150100'),
('150200','包头市','150000'),
('150202','东河区','150200'),
('150203','昆都仑区','150200'),
('150204','青山区','150200'),
('150205','石拐区','150200'),
('150206','白云鄂博矿区','150200'),
('150207','九原区','150200'),
('150221','土默特右旗','150200'),
('150222','固阳县','150200'),
('150223','达尔罕茂明安联合旗','150200'),
('150300','乌海市','150000'),
('150302','海勃湾区','150300'),
('150303','海南区','150300'),
('150304','乌达区','150300'),
('150400','赤峰市','150000'),
('150402','红山区','150400'),
('150403','元宝山区','150400'),
('150404','松山区','150400'),
('150421','阿鲁科尔沁旗','150400'),
('150422','巴林左旗','150400'),
('150423','巴林右旗','150400'),
('150424','林西县','150400'),
('150425','克什克腾旗','150400'),
('150426','翁牛特旗','150400'),
('150428','喀喇沁旗','150400'),
('150429','宁城县','150400'),
('150430','敖汉旗','150400'),
('150500','通辽市','150000'),
('150502','科尔沁区','150500'),
('150521','科尔沁左翼中旗','150500'),
('150522','科尔沁左翼后旗','150500'),
('150523','开鲁县','150500'),
('150524','库伦旗','150500'),
('150525','奈曼旗','150500'),
('150526','扎鲁特旗','150500'),
('150581','霍林郭勒市','150500'),
('150600','鄂尔多斯市','150000'),
('150602','东胜区','150600'),
('150621','达拉特旗','150600'),
('150622','准格尔旗','150600'),
('150623','鄂托克前旗','150600'),
('150624','鄂托克旗','150600'),
('150625','杭锦旗','150600'),
('150626','乌审旗','150600'),
('150627','伊金霍洛旗','150600'),
('150700','呼伦贝尔市','150000'),
('150702','海拉尔区','150700'),
('150703','扎赉诺尔区','150700'),
('150721','阿荣旗','150700'),
('150722','莫力达瓦达斡尔族自治旗','150700'),
('150723','鄂伦春自治旗','150700'),
('150724','鄂温克族自治旗','150700'),
('150725','陈巴尔虎旗','150700'),
('150726','新巴尔虎左旗','150700'),
('150727','新巴尔虎右旗','150700'),
('150781','满洲里市','150700'),
('150782','牙克石市','150700'),
('150783','扎兰屯市','150700'),
('150784','额尔古纳市','150700'),
('150785','根河市','150700'),
('150800','巴彦淖尔市','150000'),
('150802','临河区','150800'),
('150821','五原县','150800'),
('150822','磴口县','150800'),
('150823','乌拉特前旗','150800'),
('150824','乌拉特中旗','150800'),
('150825','乌拉特后旗','150800'),
('150826','杭锦后旗','150800'),
('150900','乌兰察布市','150000'),
('150902','集宁区','150900'),
('150921','卓资县','150900'),
('150922','化德县','150900'),
('150923','商都县','150900'),
('150924','兴和县','150900'),
('150925','凉城县','150900'),
('150926','察哈尔右翼前旗','150900'),
('150927','察哈尔右翼中旗','150900'),
('150928','察哈尔右翼后旗','150900'),
('150929','四子王旗','150900'),
('150981','丰镇市','150900'),
('152200','兴安盟','150000'),
('152201','乌兰浩特市','152200'),
('152202','阿尔山市','152200'),
('152221','科尔沁右翼前旗','152200'),
('152222','科尔沁右翼中旗','152200'),
('152223','扎赉特旗','152200'),
('152224','突泉县','152200'),
('152500','锡林郭勒盟','150000'),
('152501','二连浩特市','152500'),
('152502','锡林浩特市','152500'),
('152522','阿巴嘎旗','152500'),
('152523','苏尼特左旗','152500'),
('152524','苏尼特右旗','152500'),
('152525','东乌珠穆沁旗','152500'),
('152526','西乌珠穆沁旗','152500'),
('152527','太仆寺旗','152500'),
('152528','镶黄旗','152500'),
('152529','正镶白旗','152500'),
('152530','正蓝旗','152500'),
('152531','多伦县','152500'),
('152900','阿拉善盟','150000'),
('152921','阿拉善左旗','152900'),
('152922','阿拉善右旗','152900'),
('152923','额济纳旗','152900'),
('210000','辽宁省','210000'),
('210100','沈阳市','210000'),
('210102','和平区','210100'),
('210103','沈河区','210100'),
('210104','大东区','210100'),
('210105','皇姑区','210100'),
('210106','铁西区','210100'),
('210111','苏家屯区','210100'),
('210112','浑南区','210100'),
('210113','沈北新区','210100'),
('210114','于洪区','210100'),
('210122','辽中县','210100'),
('210123','康平县','210100'),
('210124','法库县','210100'),
('210181','新民市','210100'),
('210200','大连市','210000'),
('210202','中山区','210200'),
('210203','西岗区','210200'),
('210204','沙河口区','210200'),
('210211','甘井子区','210200'),
('210212','旅顺口区','210200'),
('210213','金州区','210200'),
('210224','长海县','210200'),
('210281','瓦房店市','210200'),
('210282','普兰店市','210200'),
('210283','庄河市','210200'),
('210300','鞍山市','210000'),
('210302','铁东区','210300'),
('210303','铁西区','210300'),
('210304','立山区','210300'),
('210311','千山区','210300'),
('210321','台安县','210300'),
('210323','岫岩满族自治县','210300'),
('210381','海城市','210300'),
('210400','抚顺市','210000'),
('210402','新抚区','210400'),
('210403','东洲区','210400'),
('210404','望花区','210400'),
('210411','顺城区','210400'),
('210421','抚顺县','210400'),
('210422','新宾满族自治县','210400'),
('210423','清原满族自治县','210400'),
('210500','本溪市','210000'),
('210502','平山区','210500'),
('210503','溪湖区','210500'),
('210504','明山区','210500'),
('210505','南芬区','210500'),
('210521','本溪满族自治县','210500'),
('210522','桓仁满族自治县','210500'),
('210600','丹东市','210000'),
('210602','元宝区','210600'),
('210603','振兴区','210600'),
('210604','振安区','210600'),
('210624','宽甸满族自治县','210600'),
('210681','东港市','210600'),
('210682','凤城市','210600'),
('210700','锦州市','210000'),
('210702','古塔区','210700'),
('210703','凌河区','210700'),
('210711','太和区','210700'),
('210726','黑山县','210700'),
('210727','义县','210700'),
('210781','凌海市','210700'),
('210782','北镇市','210700'),
('210800','营口市','210000'),
('210802','站前区','210800'),
('210803','西市区','210800'),
('210804','鲅鱼圈区','210800'),
('210811','老边区','210800'),
('210881','盖州市','210800'),
('210882','大石桥市','210800'),
('210900','阜新市','210000'),
('210902','海州区','210900'),
('210903','新邱区','210900'),
('210904','太平区','210900'),
('210905','清河门区','210900'),
('210911','细河区','210900'),
('210921','阜新蒙古族自治县','210900'),
('210922','彰武县','210900'),
('211000','辽阳市','210000'),
('211002','白塔区','211000'),
('211003','文圣区','211000'),
('211004','宏伟区','211000'),
('211005','弓长岭区','211000'),
('211011','太子河区','211000'),
('211021','辽阳县','211000'),
('211081','灯塔市','211000'),
('211100','盘锦市','210000'),
('211102','双台子区','211100'),
('211103','兴隆台区','211100'),
('211121','大洼县','211100'),
('211122','盘山县','211100'),
('211200','铁岭市','210000'),
('211202','银州区','211200'),
('211204','清河区','211200'),
('211221','铁岭县','211200'),
('211223','西丰县','211200'),
('211224','昌图县','211200'),
('211281','调兵山市','211200'),
('211282','开原市','211200'),
('211300','朝阳市','210000'),
('211302','双塔区','211300'),
('211303','龙城区','211300'),
('211321','朝阳县','211300'),
('211322','建平县','211300'),
('211324','喀喇沁左翼蒙古族自治县','211300'),
('211381','北票市','211300'),
('211382','凌源市','211300'),
('211400','葫芦岛市','210000'),
('211402','连山区','211400'),
('211403','龙港区','211400'),
('211404','南票区','211400'),
('211421','绥中县','211400'),
('211422','建昌县','211400'),
('211481','兴城市','211400'),
('211500','金普新区','210000'),
('211501','金州新区','211500'),
('211502','普湾新区','211500'),
('211503','保税区','211500'),
('220000','吉林省','220000'),
('220100','长春市','220000'),
('220102','南关区','220100'),
('220103','宽城区','220100'),
('220104','朝阳区','220100'),
('220105','二道区','220100'),
('220106','绿园区','220100'),
('220112','双阳区','220100'),
('220113','九台区','220100'),
('220122','农安县','220100'),
('220182','榆树市','220100'),
('220183','德惠市','220100'),
('220200','吉林市','220000'),
('220202','昌邑区','220200'),
('220203','龙潭区','220200'),
('220204','船营区','220200'),
('220211','丰满区','220200'),
('220221','永吉县','220200'),
('220281','蛟河市','220200'),
('220282','桦甸市','220200'),
('220283','舒兰市','220200'),
('220284','磐石市','220200'),
('220300','四平市','220000'),
('220302','铁西区','220300'),
('220303','铁东区','220300'),
('220322','梨树县','220300'),
('220323','伊通满族自治县','220300'),
('220381','公主岭市','220300'),
('220382','双辽市','220300'),
('220400','辽源市','220000'),
('220402','龙山区','220400'),
('220403','西安区','220400'),
('220421','东丰县','220400'),
('220422','东辽县','220400'),
('220500','通化市','220000'),
('220502','东昌区','220500'),
('220503','二道江区','220500'),
('220521','通化县','220500'),
('220523','辉南县','220500'),
('220524','柳河县','220500'),
('220581','梅河口市','220500'),
('220582','集安市','220500'),
('220600','白山市','220000'),
('220602','浑江区','220600'),
('220605','江源区','220600'),
('220621','抚松县','220600'),
('220622','靖宇县','220600'),
('220623','长白朝鲜族自治县','220600'),
('220681','临江市','220600'),
('220700','松原市','220000'),
('220702','宁江区','220700'),
('220721','前郭尔罗斯蒙古族自治县','220700'),
('220722','长岭县','220700'),
('220723','乾安县','220700'),
('220781','扶余市','220700'),
('220800','白城市','220000'),
('220802','洮北区','220800'),
('220821','镇赉县','220800'),
('220822','通榆县','220800'),
('220881','洮南市','220800'),
('220882','大安市','220800'),
('222400','延边朝鲜族自治州','220000'),
('222401','延吉市','222400'),
('222402','图们市','222400'),
('222403','敦化市','222400'),
('222404','珲春市','222400'),
('222405','龙井市','222400'),
('222406','和龙市','222400'),
('222424','汪清县','222400'),
('222426','安图县','222400'),
('230000','黑龙江省','230000'),
('230100','哈尔滨市','230000'),
('230102','道里区','230100'),
('230103','南岗区','230100'),
('230104','道外区','230100'),
('230108','平房区','230100'),
('230109','松北区','230100'),
('230110','香坊区','230100'),
('230111','呼兰区','230100'),
('230112','阿城区','230100'),
('230113','双城区','230100'),
('230123','依兰县','230100'),
('230124','方正县','230100'),
('230125','宾县','230100'),
('230126','巴彦县','230100'),
('230127','木兰县','230100'),
('230128','通河县','230100'),
('230129','延寿县','230100'),
('230183','尚志市','230100'),
('230184','五常市','230100'),
('230200','齐齐哈尔市','230000'),
('230202','龙沙区','230200'),
('230203','建华区','230200'),
('230204','铁锋区','230200'),
('230205','昂昂溪区','230200'),
('230206','富拉尔基区','230200'),
('230207','碾子山区','230200'),
('230208','梅里斯达斡尔族区','230200'),
('230221','龙江县','230200'),
('230223','依安县','230200'),
('230224','泰来县','230200'),
('230225','甘南县','230200'),
('230227','富裕县','230200'),
('230229','克山县','230200'),
('230230','克东县','230200'),
('230231','拜泉县','230200'),
('230281','讷河市','230200'),
('230300','鸡西市','230000'),
('230302','鸡冠区','230300'),
('230303','恒山区','230300'),
('230304','滴道区','230300'),
('230305','梨树区','230300'),
('230306','城子河区','230300'),
('230307','麻山区','230300'),
('230321','鸡东县','230300'),
('230381','虎林市','230300'),
('230382','密山市','230300'),
('230400','鹤岗市','230000'),
('230402','向阳区','230400'),
('230403','工农区','230400'),
('230404','南山区','230400'),
('230405','兴安区','230400'),
('230406','东山区','230400'),
('230407','兴山区','230400'),
('230421','萝北县','230400'),
('230422','绥滨县','230400'),
('230500','双鸭山市','230000'),
('230502','尖山区','230500'),
('230503','岭东区','230500'),
('230505','四方台区','230500'),
('230506','宝山区','230500'),
('230521','集贤县','230500'),
('230522','友谊县','230500'),
('230523','宝清县','230500'),
('230524','饶河县','230500'),
('230600','大庆市','230000'),
('230602','萨尔图区','230600'),
('230603','龙凤区','230600'),
('230604','让胡路区','230600'),
('230605','红岗区','230600'),
('230606','大同区','230600'),
('230621','肇州县','230600'),
('230622','肇源县','230600'),
('230623','林甸县','230600'),
('230624','杜尔伯特蒙古族自治县','230600'),
('230700','伊春市','230000'),
('230702','伊春区','230700'),
('230703','南岔区','230700'),
('230704','友好区','230700'),
('230705','西林区','230700'),
('230706','翠峦区','230700'),
('230707','新青区','230700'),
('230708','美溪区','230700'),
('230709','金山屯区','230700'),
('230710','五营区','230700'),
('230711','乌马河区','230700'),
('230712','汤旺河区','230700'),
('230713','带岭区','230700'),
('230714','乌伊岭区','230700'),
('230715','红星区','230700'),
('230716','上甘岭区','230700'),
('230722','嘉荫县','230700'),
('230781','铁力市','230700'),
('230800','佳木斯市','230000'),
('230803','向阳区','230800'),
('230804','前进区','230800'),
('230805','东风区','230800'),
('230811','郊区','230800'),
('230822','桦南县','230800'),
('230826','桦川县','230800'),
('230828','汤原县','230800'),
('230833','抚远县','230800'),
('230881','同江市','230800'),
('230882','富锦市','230800'),
('230900','七台河市','230000'),
('230902','新兴区','230900'),
('230903','桃山区','230900'),
('230904','茄子河区','230900'),
('230921','勃利县','230900'),
('231000','牡丹江市','230000'),
('231002','东安区','231000'),
('231003','阳明区','231000'),
('231004','爱民区','231000'),
('231005','西安区','231000'),
('231024','东宁县','231000'),
('231025','林口县','231000'),
('231081','绥芬河市','231000'),
('231083','海林市','231000'),
('231084','宁安市','231000'),
('231085','穆棱市','231000'),
('231100','黑河市','230000'),
('231102','爱辉区','231100'),
('231121','嫩江县','231100'),
('231123','逊克县','231100'),
('231124','孙吴县','231100'),
('231181','北安市','231100'),
('231182','五大连池市','231100'),
('231200','绥化市','230000'),
('231202','北林区','231200'),
('231221','望奎县','231200'),
('231222','兰西县','231200'),
('231223','青冈县','231200'),
('231224','庆安县','231200'),
('231225','明水县','231200'),
('231226','绥棱县','231200'),
('231281','安达市','231200'),
('231282','肇东市','231200'),
('231283','海伦市','231200'),
('232700','大兴安岭地区','230000'),
('232701','加格达奇区','232700'),
('232702','新林区','232700'),
('232703','松岭区','232700'),
('232704','呼中区','232700'),
('232721','呼玛县','232700'),
('232722','塔河县','232700'),
('232723','漠河县','232700'),
('310000','上海','310000'),
('310100','上海市','310000'),
('310101','黄浦区','310100'),
('310104','徐汇区','310100'),
('310105','长宁区','310100'),
('310106','静安区','310100'),
('310107','普陀区','310100'),
('310108','闸北区','310100'),
('310109','虹口区','310100'),
('310110','杨浦区','310100'),
('310112','闵行区','310100'),
('310113','宝山区','310100'),
('310114','嘉定区','310100'),
('310115','浦东新区','310100'),
('310116','金山区','310100'),
('310117','松江区','310100'),
('310118','青浦区','310100'),
('310120','奉贤区','310100'),
('310230','崇明县','310100'),
('320000','江苏省','320000'),
('320100','南京市','320000'),
('320102','玄武区','320100'),
('320104','秦淮区','320100'),
('320105','建邺区','320100'),
('320106','鼓楼区','320100'),
('320111','浦口区','320100'),
('320113','栖霞区','320100'),
('320114','雨花台区','320100'),
('320115','江宁区','320100'),
('320116','六合区','320100'),
('320117','溧水区','320100'),
('320118','高淳区','320100'),
('320200','无锡市','320000'),
('320202','崇安区','320200'),
('320203','南长区','320200'),
('320204','北塘区','320200'),
('320205','锡山区','320200'),
('320206','惠山区','320200'),
('320211','滨湖区','320200'),
('320281','江阴市','320200'),
('320282','宜兴市','320200'),
('320300','徐州市','320000'),
('320302','鼓楼区','320300'),
('320303','云龙区','320300'),
('320305','贾汪区','320300'),
('320311','泉山区','320300'),
('320312','铜山区','320300'),
('320321','丰县','320300'),
('320322','沛县','320300'),
('320324','睢宁县','320300'),
('320381','新沂市','320300'),
('320382','邳州市','320300'),
('320400','常州市','320000'),
('320402','天宁区','320400'),
('320404','钟楼区','320400'),
('320405','戚墅堰区','320400'),
('320411','新北区','320400'),
('320412','武进区','320400'),
('320481','溧阳市','320400'),
('320482','金坛市','320400'),
('320500','苏州市','320000'),
('320505','虎丘区','320500'),
('320506','吴中区','320500'),
('320507','相城区','320500'),
('320508','姑苏区','320500'),
('320509','吴江区','320500'),
('320581','常熟市','320500'),
('320582','张家港市','320500'),
('320583','昆山市','320500'),
('320585','太仓市','320500'),
('320600','南通市','320000'),
('320602','崇川区','320600'),
('320611','港闸区','320600'),
('320612','通州区','320600'),
('320621','海安县','320600'),
('320623','如东县','320600'),
('320681','启东市','320600'),
('320682','如皋市','320600'),
('320684','海门市','320600'),
('320700','连云港市','320000'),
('320703','连云区','320700'),
('320706','海州区','320700'),
('320707','赣榆区','320700'),
('320722','东海县','320700'),
('320723','灌云县','320700'),
('320724','灌南县','320700'),
('320800','淮安市','320000'),
('320802','清河区','320800'),
('320803','淮安区','320800'),
('320804','淮阴区','320800'),
('320811','清浦区','320800'),
('320826','涟水县','320800'),
('320829','洪泽县','320800'),
('320830','盱眙县','320800'),
('320831','金湖县','320800'),
('320900','盐城市','320000'),
('320902','亭湖区','320900'),
('320903','盐都区','320900'),
('320921','响水县','320900'),
('320922','滨海县','320900'),
('320923','阜宁县','320900'),
('320924','射阳县','320900'),
('320925','建湖县','320900'),
('320981','东台市','320900'),
('320982','大丰市','320900'),
('321000','扬州市','320000'),
('321002','广陵区','321000'),
('321003','邗江区','321000'),
('321012','江都区','321000'),
('321023','宝应县','321000'),
('321081','仪征市','321000'),
('321084','高邮市','321000'),
('321100','镇江市','320000'),
('321102','京口区','321100'),
('321111','润州区','321100'),
('321112','丹徒区','321100'),
('321181','丹阳市','321100'),
('321182','扬中市','321100'),
('321183','句容市','321100'),
('321200','泰州市','320000'),
('321202','海陵区','321200'),
('321203','高港区','321200'),
('321204','姜堰区','321200'),
('321281','兴化市','321200'),
('321282','靖江市','321200'),
('321283','泰兴市','321200'),
('321300','宿迁市','320000'),
('321302','宿城区','321300'),
('321311','宿豫区','321300'),
('321322','沭阳县','321300'),
('321323','泗阳县','321300'),
('321324','泗洪县','321300'),
('330000','浙江省','330000'),
('330100','杭州市','330000'),
('330102','上城区','330100'),
('330103','下城区','330100'),
('330104','江干区','330100'),
('330105','拱墅区','330100'),
('330106','西湖区','330100'),
('330108','滨江区','330100'),
('330109','萧山区','330100'),
('330110','余杭区','330100'),
('330122','桐庐县','330100'),
('330127','淳安县','330100'),
('330182','建德市','330100'),
('330183','富阳区','330100'),
('330185','临安市','330100'),
('330200','宁波市','330000'),
('330203','海曙区','330200'),
('330204','江东区','330200'),
('330205','江北区','330200'),
('330206','北仑区','330200'),
('330211','镇海区','330200'),
('330212','鄞州区','330200'),
('330225','象山县','330200'),
('330226','宁海县','330200'),
('330281','余姚市','330200'),
('330282','慈溪市','330200'),
('330283','奉化市','330200'),
('330300','温州市','330000'),
('330302','鹿城区','330300'),
('330303','龙湾区','330300'),
('330304','瓯海区','330300'),
('330322','洞头县','330300'),
('330324','永嘉县','330300'),
('330326','平阳县','330300'),
('330327','苍南县','330300'),
('330328','文成县','330300'),
('330329','泰顺县','330300'),
('330381','瑞安市','330300'),
('330382','乐清市','330300'),
('330400','嘉兴市','330000'),
('330402','南湖区','330400'),
('330411','秀洲区','330400'),
('330421','嘉善县','330400'),
('330424','海盐县','330400'),
('330481','海宁市','330400'),
('330482','平湖市','330400'),
('330483','桐乡市','330400'),
('330500','湖州市','330000'),
('330502','吴兴区','330500'),
('330503','南浔区','330500'),
('330521','德清县','330500'),
('330522','长兴县','330500'),
('330523','安吉县','330500'),
('330600','绍兴市','330000'),
('330602','越城区','330600'),
('330603','柯桥区','330600'),
('330604','上虞区','330600'),
('330624','新昌县','330600'),
('330681','诸暨市','330600'),
('330683','嵊州市','330600'),
('330700','金华市','330000'),
('330702','婺城区','330700'),
('330703','金东区','330700'),
('330723','武义县','330700'),
('330726','浦江县','330700'),
('330727','磐安县','330700'),
('330781','兰溪市','330700'),
('330782','义乌市','330700'),
('330783','东阳市','330700'),
('330784','永康市','330700'),
('330800','衢州市','330000'),
('330802','柯城区','330800'),
('330803','衢江区','330800'),
('330822','常山县','330800'),
('330824','开化县','330800'),
('330825','龙游县','330800'),
('330881','江山市','330800'),
('330900','舟山市','330000'),
('330902','定海区','330900'),
('330903','普陀区','330900'),
('330921','岱山县','330900'),
('330922','嵊泗县','330900'),
('331000','台州市','330000'),
('331002','椒江区','331000'),
('331003','黄岩区','331000'),
('331004','路桥区','331000'),
('331021','玉环县','331000'),
('331022','三门县','331000'),
('331023','天台县','331000'),
('331024','仙居县','331000'),
('331081','温岭市','331000'),
('331082','临海市','331000'),
('331100','丽水市','330000'),
('331102','莲都区','331100'),
('331121','青田县','331100'),
('331122','缙云县','331100'),
('331123','遂昌县','331100'),
('331124','松阳县','331100'),
('331125','云和县','331100'),
('331126','庆元县','331100'),
('331127','景宁畲族自治县','331100'),
('331181','龙泉市','331100'),
('331200','舟山群岛新区','330000'),
('331201','金塘岛','331200'),
('331202','六横岛','331200'),
('331203','衢山岛','331200'),
('331204','舟山本岛西北部','331200'),
('331205','岱山岛西南部','331200'),
('331206','泗礁岛','331200'),
('331207','朱家尖岛','331200'),
('331208','洋山岛','331200'),
('331209','长涂岛','331200'),
('331210','虾峙岛','331200'),
('340000','安徽省','340000'),
('340100','合肥市','340000'),
('340102','瑶海区','340100'),
('340103','庐阳区','340100'),
('340104','蜀山区','340100'),
('340111','包河区','340100'),
('340121','长丰县','340100'),
('340122','肥东县','340100'),
('340123','肥西县','340100'),
('340124','庐江县','340100'),
('340181','巢湖市','340100'),
('340200','芜湖市','340000'),
('340202','镜湖区','340200'),
('340203','弋江区','340200'),
('340207','鸠江区','340200'),
('340208','三山区','340200'),
('340221','芜湖县','340200'),
('340222','繁昌县','340200'),
('340223','南陵县','340200'),
('340225','无为县','340200'),
('340300','蚌埠市','340000'),
('340302','龙子湖区','340300'),
('340303','蚌山区','340300'),
('340304','禹会区','340300'),
('340311','淮上区','340300'),
('340321','怀远县','340300'),
('340322','五河县','340300'),
('340323','固镇县','340300'),
('340400','淮南市','340000'),
('340402','大通区','340400'),
('340403','田家庵区','340400'),
('340404','谢家集区','340400'),
('340405','八公山区','340400'),
('340406','潘集区','340400'),
('340421','凤台县','340400'),
('340500','马鞍山市','340000'),
('340503','花山区','340500'),
('340504','雨山区','340500'),
('340506','博望区','340500'),
('340521','当涂县','340500'),
('340522','含山县','340500'),
('340523','和县','340500'),
('340600','淮北市','340000'),
('340602','杜集区','340600'),
('340603','相山区','340600'),
('340604','烈山区','340600'),
('340621','濉溪县','340600'),
('340700','铜陵市','340000'),
('340702','铜官山区','340700'),
('340703','狮子山区','340700'),
('340711','郊区','340700'),
('340721','铜陵县','340700'),
('340800','安庆市','340000'),
('340802','迎江区','340800'),
('340803','大观区','340800'),
('340811','宜秀区','340800'),
('340822','怀宁县','340800'),
('340823','枞阳县','340800'),
('340824','潜山县','340800'),
('340825','太湖县','340800'),
('340826','宿松县','340800'),
('340827','望江县','340800'),
('340828','岳西县','340800'),
('340881','桐城市','340800'),
('341000','黄山市','340000'),
('341002','屯溪区','341000'),
('341003','黄山区','341000'),
('341004','徽州区','341000'),
('341021','歙县','341000'),
('341022','休宁县','341000'),
('341023','黟县','341000'),
('341024','祁门县','341000'),
('341100','滁州市','340000'),
('341102','琅琊区','341100'),
('341103','南谯区','341100'),
('341122','来安县','341100'),
('341124','全椒县','341100'),
('341125','定远县','341100'),
('341126','凤阳县','341100'),
('341181','天长市','341100'),
('341182','明光市','341100'),
('341200','阜阳市','340000'),
('341202','颍州区','341200'),
('341203','颍东区','341200'),
('341204','颍泉区','341200'),
('341221','临泉县','341200'),
('341222','太和县','341200'),
('341225','阜南县','341200'),
('341226','颍上县','341200'),
('341282','界首市','341200'),
('341300','宿州市','340000'),
('341302','埇桥区','341300'),
('341321','砀山县','341300'),
('341322','萧县','341300'),
('341323','灵璧县','341300'),
('341324','泗县','341300'),
('341500','六安市','340000'),
('341502','金安区','341500'),
('341503','裕安区','341500'),
('341521','寿县','341500'),
('341522','霍邱县','341500'),
('341523','舒城县','341500'),
('341524','金寨县','341500'),
('341525','霍山县','341500'),
('341600','亳州市','340000'),
('341602','谯城区','341600'),
('341621','涡阳县','341600'),
('341622','蒙城县','341600'),
('341623','利辛县','341600'),
('341700','池州市','340000'),
('341702','贵池区','341700'),
('341721','东至县','341700'),
('341722','石台县','341700'),
('341723','青阳县','341700'),
('341800','宣城市','340000'),
('341802','宣州区','341800'),
('341821','郎溪县','341800'),
('341822','广德县','341800'),
('341823','泾县','341800'),
('341824','绩溪县','341800'),
('341825','旌德县','341800'),
('341881','宁国市','341800'),
('350000','福建省','350000'),
('350100','福州市','350000'),
('350102','鼓楼区','350100'),
('350103','台江区','350100'),
('350104','仓山区','350100'),
('350105','马尾区','350100'),
('350111','晋安区','350100'),
('350121','闽侯县','350100'),
('350122','连江县','350100'),
('350123','罗源县','350100'),
('350124','闽清县','350100'),
('350125','永泰县','350100'),
('350128','平潭县','350100'),
('350181','福清市','350100'),
('350182','长乐市','350100'),
('350200','厦门市','350000'),
('350203','思明区','350200'),
('350205','海沧区','350200'),
('350206','湖里区','350200'),
('350211','集美区','350200'),
('350212','同安区','350200'),
('350213','翔安区','350200'),
('350300','莆田市','350000'),
('350302','城厢区','350300'),
('350303','涵江区','350300'),
('350304','荔城区','350300'),
('350305','秀屿区','350300'),
('350322','仙游县','350300'),
('350400','三明市','350000'),
('350402','梅列区','350400'),
('350403','三元区','350400'),
('350421','明溪县','350400'),
('350423','清流县','350400'),
('350424','宁化县','350400'),
('350425','大田县','350400'),
('350426','尤溪县','350400'),
('350427','沙县','350400'),
('350428','将乐县','350400'),
('350429','泰宁县','350400'),
('350430','建宁县','350400'),
('350481','永安市','350400'),
('350500','泉州市','350000'),
('350502','鲤城区','350500'),
('350503','丰泽区','350500'),
('350504','洛江区','350500'),
('350505','泉港区','350500'),
('350521','惠安县','350500'),
('350524','安溪县','350500'),
('350525','永春县','350500'),
('350526','德化县','350500'),
('350527','金门县','350500'),
('350581','石狮市','350500'),
('350582','晋江市','350500'),
('350583','南安市','350500'),
('350600','漳州市','350000'),
('350602','芗城区','350600'),
('350603','龙文区','350600'),
('350622','云霄县','350600'),
('350623','漳浦县','350600'),
('350624','诏安县','350600'),
('350625','长泰县','350600'),
('350626','东山县','350600'),
('350627','南靖县','350600'),
('350628','平和县','350600'),
('350629','华安县','350600'),
('350681','龙海市','350600'),
('350700','南平市','350000'),
('350702','延平区','350700'),
('350703','建阳区','350700'),
('350721','顺昌县','350700'),
('350722','浦城县','350700'),
('350723','光泽县','350700'),
('350724','松溪县','350700'),
('350725','政和县','350700'),
('350781','邵武市','350700'),
('350782','武夷山市','350700'),
('350783','建瓯市','350700'),
('350800','龙岩市','350000'),
('350802','新罗区','350800'),
('350821','长汀县','350800'),
('350822','永定区','350800'),
('350823','上杭县','350800'),
('350824','武平县','350800'),
('350825','连城县','350800'),
('350881','漳平市','350800'),
('350900','宁德市','350000'),
('350902','蕉城区','350900'),
('350921','霞浦县','350900'),
('350922','古田县','350900'),
('350923','屏南县','350900'),
('350924','寿宁县','350900'),
('350925','周宁县','350900'),
('350926','柘荣县','350900'),
('350981','福安市','350900'),
('350982','福鼎市','350900'),
('360000','江西省','360000'),
('360100','南昌市','360000'),
('360102','东湖区','360100'),
('360103','西湖区','360100'),
('360104','青云谱区','360100'),
('360105','湾里区','360100'),
('360111','青山湖区','360100'),
('360121','南昌县','360100'),
('360122','新建县','360100'),
('360123','安义县','360100'),
('360124','进贤县','360100'),
('360200','景德镇市','360000'),
('360202','昌江区','360200'),
('360203','珠山区','360200'),
('360222','浮梁县','360200'),
('360281','乐平市','360200'),
('360300','萍乡市','360000'),
('360302','安源区','360300'),
('360313','湘东区','360300'),
('360321','莲花县','360300'),
('360322','上栗县','360300'),
('360323','芦溪县','360300'),
('360400','九江市','360000'),
('360402','庐山区','360400'),
('360403','浔阳区','360400'),
('360421','九江县','360400'),
('360423','武宁县','360400'),
('360424','修水县','360400'),
('360425','永修县','360400'),
('360426','德安县','360400'),
('360427','星子县','360400'),
('360428','都昌县','360400'),
('360429','湖口县','360400'),
('360430','彭泽县','360400'),
('360481','瑞昌市','360400'),
('360482','共青城市','360400'),
('360500','新余市','360000'),
('360502','渝水区','360500'),
('360521','分宜县','360500'),
('360600','鹰潭市','360000'),
('360602','月湖区','360600'),
('360622','余江县','360600'),
('360681','贵溪市','360600'),
('360700','赣州市','360000'),
('360702','章贡区','360700'),
('360703','南康区','360700'),
('360721','赣县','360700'),
('360722','信丰县','360700'),
('360723','大余县','360700'),
('360724','上犹县','360700'),
('360725','崇义县','360700'),
('360726','安远县','360700'),
('360727','龙南县','360700'),
('360728','定南县','360700'),
('360729','全南县','360700'),
('360730','宁都县','360700'),
('360731','于都县','360700'),
('360732','兴国县','360700'),
('360733','会昌县','360700'),
('360734','寻乌县','360700'),
('360735','石城县','360700'),
('360781','瑞金市','360700'),
('360800','吉安市','360000'),
('360802','吉州区','360800'),
('360803','青原区','360800'),
('360821','吉安县','360800'),
('360822','吉水县','360800'),
('360823','峡江县','360800'),
('360824','新干县','360800'),
('360825','永丰县','360800'),
('360826','泰和县','360800'),
('360827','遂川县','360800'),
('360828','万安县','360800'),
('360829','安福县','360800'),
('360830','永新县','360800'),
('360881','井冈山市','360800'),
('360900','宜春市','360000'),
('360902','袁州区','360900'),
('360921','奉新县','360900'),
('360922','万载县','360900'),
('360923','上高县','360900'),
('360924','宜丰县','360900'),
('360925','靖安县','360900'),
('360926','铜鼓县','360900'),
('360981','丰城市','360900'),
('360982','樟树市','360900'),
('360983','高安市','360900'),
('361000','抚州市','360000'),
('361002','临川区','361000'),
('361021','南城县','361000'),
('361022','黎川县','361000'),
('361023','南丰县','361000'),
('361024','崇仁县','361000'),
('361025','乐安县','361000'),
('361026','宜黄县','361000'),
('361027','金溪县','361000'),
('361028','资溪县','361000'),
('361029','东乡县','361000'),
('361030','广昌县','361000'),
('361100','上饶市','360000'),
('361102','信州区','361100'),
('361121','上饶县','361100'),
('361122','广丰县','361100'),
('361123','玉山县','361100'),
('361124','铅山县','361100'),
('361125','横峰县','361100'),
('361126','弋阳县','361100'),
('361127','余干县','361100'),
('361128','鄱阳县','361100'),
('361129','万年县','361100'),
('361130','婺源县','361100'),
('361181','德兴市','361100'),
('370000','山东省','370000'),
('370100','济南市','370000'),
('370102','历下区','370100'),
('370103','市中区','370100'),
('370104','槐荫区','370100'),
('370105','天桥区','370100'),
('370112','历城区','370100'),
('370113','长清区','370100'),
('370124','平阴县','370100'),
('370125','济阳县','370100'),
('370126','商河县','370100'),
('370181','章丘市','370100'),
('370200','青岛市','370000'),
('370202','市南区','370200'),
('370203','市北区','370200'),
('370211','黄岛区','370200'),
('370212','崂山区','370200'),
('370213','李沧区','370200'),
('370214','城阳区','370200'),
('370281','胶州市','370200'),
('370282','即墨市','370200'),
('370283','平度市','370200'),
('370285','莱西市','370200'),
('370286','西海岸新区','370200'),
('370300','淄博市','370000'),
('370302','淄川区','370300'),
('370303','张店区','370300'),
('370304','博山区','370300'),
('370305','临淄区','370300'),
('370306','周村区','370300'),
('370321','桓台县','370300'),
('370322','高青县','370300'),
('370323','沂源县','370300'),
('370400','枣庄市','370000'),
('370402','市中区','370400'),
('370403','薛城区','370400'),
('370404','峄城区','370400'),
('370405','台儿庄区','370400'),
('370406','山亭区','370400'),
('370481','滕州市','370400'),
('370500','东营市','370000'),
('370502','东营区','370500'),
('370503','河口区','370500'),
('370521','垦利县','370500'),
('370522','利津县','370500'),
('370523','广饶县','370500'),
('370600','烟台市','370000'),
('370602','芝罘区','370600'),
('370611','福山区','370600'),
('370612','牟平区','370600'),
('370613','莱山区','370600'),
('370634','长岛县','370600'),
('370681','龙口市','370600'),
('370682','莱阳市','370600'),
('370683','莱州市','370600'),
('370684','蓬莱市','370600'),
('370685','招远市','370600'),
('370686','栖霞市','370600'),
('370687','海阳市','370600'),
('370700','潍坊市','370000'),
('370702','潍城区','370700'),
('370703','寒亭区','370700'),
('370704','坊子区','370700'),
('370705','奎文区','370700'),
('370724','临朐县','370700'),
('370725','昌乐县','370700'),
('370781','青州市','370700'),
('370782','诸城市','370700'),
('370783','寿光市','370700'),
('370784','安丘市','370700'),
('370785','高密市','370700'),
('370786','昌邑市','370700'),
('370800','济宁市','370000'),
('370811','任城区','370800'),
('370812','兖州区','370800'),
('370826','微山县','370800'),
('370827','鱼台县','370800'),
('370828','金乡县','370800'),
('370829','嘉祥县','370800'),
('370830','汶上县','370800'),
('370831','泗水县','370800'),
('370832','梁山县','370800'),
('370881','曲阜市','370800'),
('370883','邹城市','370800'),
('370900','泰安市','370000'),
('370902','泰山区','370900'),
('370911','岱岳区','370900'),
('370921','宁阳县','370900'),
('370923','东平县','370900'),
('370982','新泰市','370900'),
('370983','肥城市','370900'),
('371000','威海市','370000'),
('371002','环翠区','371000'),
('371003','文登区','371000'),
('371082','荣成市','371000'),
('371083','乳山市','371000'),
('371100','日照市','370000'),
('371102','东港区','371100'),
('371103','岚山区','371100'),
('371121','五莲县','371100'),
('371122','莒县','371100'),
('371200','莱芜市','370000'),
('371202','莱城区','371200'),
('371203','钢城区','371200'),
('371300','临沂市','370000'),
('371302','兰山区','371300'),
('371311','罗庄区','371300'),
('371312','河东区','371300'),
('371321','沂南县','371300'),
('371322','郯城县','371300'),
('371323','沂水县','371300'),
('371324','兰陵县','371300'),
('371325','费县','371300'),
('371326','平邑县','371300'),
('371327','莒南县','371300'),
('371328','蒙阴县','371300'),
('371329','临沭县','371300'),
('371400','德州市','370000'),
('371402','德城区','371400'),
('371403','陵城区','371400'),
('371422','宁津县','371400'),
('371423','庆云县','371400'),
('371424','临邑县','371400'),
('371425','齐河县','371400'),
('371426','平原县','371400'),
('371427','夏津县','371400'),
('371428','武城县','371400'),
('371481','乐陵市','371400'),
('371482','禹城市','371400'),
('371500','聊城市','370000'),
('371502','东昌府区','371500'),
('371521','阳谷县','371500'),
('371522','莘县','371500'),
('371523','茌平县','371500'),
('371524','东阿县','371500'),
('371525','冠县','371500'),
('371526','高唐县','371500'),
('371581','临清市','371500'),
('371600','滨州市','370000'),
('371602','滨城区','371600'),
('371603','沾化区','371600'),
('371621','惠民县','371600'),
('371622','阳信县','371600'),
('371623','无棣县','371600'),
('371625','博兴县','371600'),
('371626','邹平县','371600'),
('371627','北海新区','371600'),
('371700','菏泽市','370000'),
('371702','牡丹区','371700'),
('371721','曹县','371700'),
('371722','单县','371700'),
('371723','成武县','371700'),
('371724','巨野县','371700'),
('371725','郓城县','371700'),
('371726','鄄城县','371700'),
('371727','定陶县','371700'),
('371728','东明县','371700'),
('410000','河南省','410000'),
('410100','郑州市','410000'),
('410102','中原区','410100'),
('410103','二七区','410100'),
('410104','管城回族区','410100'),
('410105','金水区','410100'),
('410106','上街区','410100'),
('410108','惠济区','410100'),
('410122','中牟县','410100'),
('410181','巩义市','410100'),
('410182','荥阳市','410100'),
('410183','新密市','410100'),
('410184','新郑市','410100'),
('410185','登封市','410100'),
('410200','开封市','410000'),
('410202','龙亭区','410200'),
('410203','顺河回族区','410200'),
('410204','鼓楼区','410200'),
('410205','禹王台区','410200'),
('410212','祥符区','410200'),
('410221','杞县','410200'),
('410222','通许县','410200'),
('410223','尉氏县','410200'),
('410225','兰考县','410200'),
('410300','洛阳市','410000'),
('410302','老城区','410300'),
('410303','西工区','410300'),
('410304','瀍河回族区','410300'),
('410305','涧西区','410300'),
('410306','吉利区','410300'),
('410311','洛龙区','410300'),
('410322','孟津县','410300'),
('410323','新安县','410300'),
('410324','栾川县','410300'),
('410325','嵩县','410300'),
('410326','汝阳县','410300'),
('410327','宜阳县','410300'),
('410328','洛宁县','410300'),
('410329','伊川县','410300'),
('410381','偃师市','410300'),
('410400','平顶山市','410000'),
('410402','新华区','410400'),
('410403','卫东区','410400'),
('410404','石龙区','410400'),
('410411','湛河区','410400'),
('410421','宝丰县','410400'),
('410422','叶县','410400'),
('410423','鲁山县','410400'),
('410425','郏县','410400'),
('410481','舞钢市','410400'),
('410482','汝州市','410400'),
('410500','安阳市','410000'),
('410502','文峰区','410500'),
('410503','北关区','410500'),
('410505','殷都区','410500'),
('410506','龙安区','410500'),
('410522','安阳县','410500'),
('410523','汤阴县','410500'),
('410526','滑县','410500'),
('410527','内黄县','410500'),
('410581','林州市','410500'),
('410600','鹤壁市','410000'),
('410602','鹤山区','410600'),
('410603','山城区','410600'),
('410611','淇滨区','410600'),
('410621','浚县','410600'),
('410622','淇县','410600'),
('410700','新乡市','410000'),
('410702','红旗区','410700'),
('410703','卫滨区','410700'),
('410704','凤泉区','410700'),
('410711','牧野区','410700'),
('410721','新乡县','410700'),
('410724','获嘉县','410700'),
('410725','原阳县','410700'),
('410726','延津县','410700'),
('410727','封丘县','410700'),
('410728','长垣县','410700'),
('410781','卫辉市','410700'),
('410782','辉县市','410700'),
('410800','焦作市','410000'),
('410802','解放区','410800'),
('410803','中站区','410800'),
('410804','马村区','410800'),
('410811','山阳区','410800'),
('410821','修武县','410800'),
('410822','博爱县','410800'),
('410823','武陟县','410800'),
('410825','温县','410800'),
('410882','沁阳市','410800'),
('410883','孟州市','410800'),
('410900','濮阳市','410000'),
('410902','华龙区','410900'),
('410922','清丰县','410900'),
('410923','南乐县','410900'),
('410926','范县','410900'),
('410927','台前县','410900'),
('410928','濮阳县','410900'),
('411000','许昌市','410000'),
('411002','魏都区','411000'),
('411023','许昌县','411000'),
('411024','鄢陵县','411000'),
('411025','襄城县','411000'),
('411081','禹州市','411000'),
('411082','长葛市','411000'),
('411100','漯河市','410000'),
('411102','源汇区','411100'),
('411103','郾城区','411100'),
('411104','召陵区','411100'),
('411121','舞阳县','411100'),
('411122','临颍县','411100'),
('411200','三门峡市','410000'),
('411202','湖滨区','411200'),
('411221','渑池县','411200'),
('411222','陕县','411200'),
('411224','卢氏县','411200'),
('411281','义马市','411200'),
('411282','灵宝市','411200'),
('411300','南阳市','410000'),
('411302','宛城区','411300'),
('411303','卧龙区','411300'),
('411321','南召县','411300'),
('411322','方城县','411300'),
('411323','西峡县','411300'),
('411324','镇平县','411300'),
('411325','内乡县','411300'),
('411326','淅川县','411300'),
('411327','社旗县','411300'),
('411328','唐河县','411300'),
('411329','新野县','411300'),
('411330','桐柏县','411300'),
('411381','邓州市','411300'),
('411400','商丘市','410000'),
('411402','梁园区','411400'),
('411403','睢阳区','411400'),
('411421','民权县','411400'),
('411422','睢县','411400'),
('411423','宁陵县','411400'),
('411424','柘城县','411400'),
('411425','虞城县','411400'),
('411426','夏邑县','411400'),
('411481','永城市','411400'),
('411500','信阳市','410000'),
('411502','浉河区','411500'),
('411503','平桥区','411500'),
('411521','罗山县','411500'),
('411522','光山县','411500'),
('411523','新县','411500'),
('411524','商城县','411500'),
('411525','固始县','411500'),
('411526','潢川县','411500'),
('411527','淮滨县','411500'),
('411528','息县','411500'),
('411600','周口市','410000'),
('411602','川汇区','411600'),
('411621','扶沟县','411600'),
('411622','西华县','411600'),
('411623','商水县','411600'),
('411624','沈丘县','411600'),
('411625','郸城县','411600'),
('411626','淮阳县','411600'),
('411627','太康县','411600'),
('411628','鹿邑县','411600'),
('411681','项城市','411600'),
('411700','驻马店市','410000'),
('411702','驿城区','411700'),
('411721','西平县','411700'),
('411722','上蔡县','411700'),
('411723','平舆县','411700'),
('411724','正阳县','411700'),
('411725','确山县','411700'),
('411726','泌阳县','411700'),
('411727','汝南县','411700'),
('411728','遂平县','411700'),
('411729','新蔡县','411700'),
('419000','直辖县级','410000'),
('419001','济源市','419000'),
('420000','湖北省','420000'),
('420100','武汉市','420000'),
('420102','江岸区','420100'),
('420103','江汉区','420100'),
('420104','硚口区','420100'),
('420105','汉阳区','420100'),
('420106','武昌区','420100'),
('420107','青山区','420100'),
('420111','洪山区','420100'),
('420112','东西湖区','420100'),
('420113','汉南区','420100'),
('420114','蔡甸区','420100'),
('420115','江夏区','420100'),
('420116','黄陂区','420100'),
('420117','新洲区','420100'),
('420200','黄石市','420000'),
('420202','黄石港区','420200'),
('420203','西塞山区','420200'),
('420204','下陆区','420200'),
('420205','铁山区','420200'),
('420222','阳新县','420200'),
('420281','大冶市','420200'),
('420300','十堰市','420000'),
('420302','茅箭区','420300'),
('420303','张湾区','420300'),
('420304','郧阳区','420300'),
('420322','郧西县','420300'),
('420323','竹山县','420300'),
('420324','竹溪县','420300'),
('420325','房县','420300'),
('420381','丹江口市','420300'),
('420500','宜昌市','420000'),
('420502','西陵区','420500'),
('420503','伍家岗区','420500'),
('420504','点军区','420500'),
('420505','猇亭区','420500'),
('420506','夷陵区','420500'),
('420525','远安县','420500'),
('420526','兴山县','420500'),
('420527','秭归县','420500'),
('420528','长阳土家族自治县','420500'),
('420529','五峰土家族自治县','420500'),
('420581','宜都市','420500'),
('420582','当阳市','420500'),
('420583','枝江市','420500'),
('420600','襄阳市','420000'),
('420602','襄城区','420600'),
('420606','樊城区','420600'),
('420607','襄州区','420600'),
('420624','南漳县','420600'),
('420625','谷城县','420600'),
('420626','保康县','420600'),
('420682','老河口市','420600'),
('420683','枣阳市','420600'),
('420684','宜城市','420600'),
('420700','鄂州市','420000'),
('420702','梁子湖区','420700'),
('420703','华容区','420700'),
('420704','鄂城区','420700'),
('420800','荆门市','420000'),
('420802','东宝区','420800'),
('420804','掇刀区','420800'),
('420821','京山县','420800'),
('420822','沙洋县','420800'),
('420881','钟祥市','420800'),
('420900','孝感市','420000'),
('420902','孝南区','420900'),
('420921','孝昌县','420900'),
('420922','大悟县','420900'),
('420923','云梦县','420900'),
('420981','应城市','420900'),
('420982','安陆市','420900'),
('420984','汉川市','420900'),
('421000','荆州市','420000'),
('421002','沙市区','421000'),
('421003','荆州区','421000'),
('421022','公安县','421000'),
('421023','监利县','421000'),
('421024','江陵县','421000'),
('421081','石首市','421000'),
('421083','洪湖市','421000'),
('421087','松滋市','421000'),
('421100','黄冈市','420000'),
('421102','黄州区','421100'),
('421121','团风县','421100'),
('421122','红安县','421100'),
('421123','罗田县','421100'),
('421124','英山县','421100'),
('421125','浠水县','421100'),
('421126','蕲春县','421100'),
('421127','黄梅县','421100'),
('421181','麻城市','421100'),
('421182','武穴市','421100'),
('421200','咸宁市','420000'),
('421202','咸安区','421200'),
('421221','嘉鱼县','421200'),
('421222','通城县','421200'),
('421223','崇阳县','421200'),
('421224','通山县','421200'),
('421281','赤壁市','421200'),
('421300','随州市','420000'),
('421303','曾都区','421300'),
('421321','随县','421300'),
('421381','广水市','421300'),
('422800','恩施土家族苗族自治州','420000'),
('422801','恩施市','422800'),
('422802','利川市','422800'),
('422822','建始县','422800'),
('422823','巴东县','422800'),
('422825','宣恩县','422800'),
('422826','咸丰县','422800'),
('422827','来凤县','422800'),
('422828','鹤峰县','422800'),
('429000','直辖县级','420000'),
('429004','仙桃市','429000'),
('429005','潜江市','429000'),
('429006','天门市','429000'),
('429021','神农架林区','429000'),
('430000','湖南省','430000'),
('430100','长沙市','430000'),
('430102','芙蓉区','430100'),
('430103','天心区','430100'),
('430104','岳麓区','430100'),
('430105','开福区','430100'),
('430111','雨花区','430100'),
('430112','望城区','430100'),
('430121','长沙县','430100'),
('430124','宁乡县','430100'),
('430181','浏阳市','430100'),
('430200','株洲市','430000'),
('430202','荷塘区','430200'),
('430203','芦淞区','430200'),
('430204','石峰区','430200'),
('430211','天元区','430200'),
('430221','株洲县','430200'),
('430223','攸县','430200'),
('430224','茶陵县','430200'),
('430225','炎陵县','430200'),
('430281','醴陵市','430200'),
('430300','湘潭市','430000'),
('430302','雨湖区','430300'),
('430304','岳塘区','430300'),
('430321','湘潭县','430300'),
('430381','湘乡市','430300'),
('430382','韶山市','430300'),
('430400','衡阳市','430000'),
('430405','珠晖区','430400'),
('430406','雁峰区','430400'),
('430407','石鼓区','430400'),
('430408','蒸湘区','430400'),
('430412','南岳区','430400'),
('430421','衡阳县','430400'),
('430422','衡南县','430400'),
('430423','衡山县','430400'),
('430424','衡东县','430400'),
('430426','祁东县','430400'),
('430481','耒阳市','430400'),
('430482','常宁市','430400'),
('430500','邵阳市','430000'),
('430502','双清区','430500'),
('430503','大祥区','430500'),
('430511','北塔区','430500'),
('430521','邵东县','430500'),
('430522','新邵县','430500'),
('430523','邵阳县','430500'),
('430524','隆回县','430500'),
('430525','洞口县','430500'),
('430527','绥宁县','430500'),
('430528','新宁县','430500'),
('430529','城步苗族自治县','430500'),
('430581','武冈市','430500'),
('430600','岳阳市','430000'),
('430602','岳阳楼区','430600'),
('430603','云溪区','430600'),
('430611','君山区','430600'),
('430621','岳阳县','430600'),
('430623','华容县','430600'),
('430624','湘阴县','430600'),
('430626','平江县','430600'),
('430681','汨罗市','430600'),
('430682','临湘市','430600'),
('430700','常德市','430000'),
('430702','武陵区','430700'),
('430703','鼎城区','430700'),
('430721','安乡县','430700'),
('430722','汉寿县','430700'),
('430723','澧县','430700'),
('430724','临澧县','430700'),
('430725','桃源县','430700'),
('430726','石门县','430700'),
('430781','津市市','430700'),
('430800','张家界市','430000'),
('430802','永定区','430800'),
('430811','武陵源区','430800'),
('430821','慈利县','430800'),
('430822','桑植县','430800'),
('430900','益阳市','430000'),
('430902','资阳区','430900'),
('430903','赫山区','430900'),
('430921','南县','430900'),
('430922','桃江县','430900'),
('430923','安化县','430900'),
('430981','沅江市','430900'),
('431000','郴州市','430000'),
('431002','北湖区','431000'),
('431003','苏仙区','431000'),
('431021','桂阳县','431000'),
('431022','宜章县','431000'),
('431023','永兴县','431000'),
('431024','嘉禾县','431000'),
('431025','临武县','431000'),
('431026','汝城县','431000'),
('431027','桂东县','431000'),
('431028','安仁县','431000'),
('431081','资兴市','431000'),
('431100','永州市','430000'),
('431102','零陵区','431100'),
('431103','冷水滩区','431100'),
('431121','祁阳县','431100'),
('431122','东安县','431100'),
('431123','双牌县','431100'),
('431124','道县','431100'),
('431125','江永县','431100'),
('431126','宁远县','431100'),
('431127','蓝山县','431100'),
('431128','新田县','431100'),
('431129','江华瑶族自治县','431100'),
('431200','怀化市','430000'),
('431202','鹤城区','431200'),
('431221','中方县','431200'),
('431222','沅陵县','431200'),
('431223','辰溪县','431200'),
('431224','溆浦县','431200'),
('431225','会同县','431200'),
('431226','麻阳苗族自治县','431200'),
('431227','新晃侗族自治县','431200'),
('431228','芷江侗族自治县','431200'),
('431229','靖州苗族侗族自治县','431200'),
('431230','通道侗族自治县','431200'),
('431281','洪江市','431200'),
('431300','娄底市','430000'),
('431302','娄星区','431300'),
('431321','双峰县','431300'),
('431322','新化县','431300'),
('431381','冷水江市','431300'),
('431382','涟源市','431300'),
('433100','湘西土家族苗族自治州','430000'),
('433101','吉首市','433100'),
('433122','泸溪县','433100'),
('433123','凤凰县','433100'),
('433124','花垣县','433100'),
('433125','保靖县','433100'),
('433126','古丈县','433100'),
('433127','永顺县','433100'),
('433130','龙山县','433100'),
('440000','广东省','440000'),
('440100','广州市','440000'),
('440103','荔湾区','440100'),
('440104','越秀区','440100'),
('440105','海珠区','440100'),
('440106','天河区','440100'),
('440111','白云区','440100'),
('440112','黄埔区','440100'),
('440113','番禺区','440100'),
('440114','花都区','440100'),
('440115','南沙区','440100'),
('440117','从化区','440100'),
('440118','增城区','440100'),
('440200','韶关市','440000'),
('440203','武江区','440200'),
('440204','浈江区','440200'),
('440205','曲江区','440200'),
('440222','始兴县','440200'),
('440224','仁化县','440200'),
('440229','翁源县','440200'),
('440232','乳源瑶族自治县','440200'),
('440233','新丰县','440200'),
('440281','乐昌市','440200'),
('440282','南雄市','440200'),
('440300','深圳市','440000'),
('440303','罗湖区','440300'),
('440304','福田区','440300'),
('440305','南山区','440300'),
('440306','宝安区','440300'),
('440307','龙岗区','440300'),
('440308','盐田区','440300'),
('440309','光明新区','440300'),
('440310','坪山新区','440300'),
('440311','大鹏新区','440300'),
('440312','龙华新区','440300'),
('440400','珠海市','440000'),
('440402','香洲区','440400'),
('440403','斗门区','440400'),
('440404','金湾区','440400'),
('440500','汕头市','440000'),
('440507','龙湖区','440500'),
('440511','金平区','440500'),
('440512','濠江区','440500'),
('440513','潮阳区','440500'),
('440514','潮南区','440500'),
('440515','澄海区','440500'),
('440523','南澳县','440500'),
('440600','佛山市','440000'),
('440604','禅城区','440600'),
('440605','南海区','440600'),
('440606','顺德区','440600'),
('440607','三水区','440600'),
('440608','高明区','440600'),
('440700','江门市','440000'),
('440703','蓬江区','440700'),
('440704','江海区','440700'),
('440705','新会区','440700'),
('440781','台山市','440700'),
('440783','开平市','440700'),
('440784','鹤山市','440700'),
('440785','恩平市','440700'),
('440800','湛江市','440000'),
('440802','赤坎区','440800'),
('440803','霞山区','440800'),
('440804','坡头区','440800'),
('440811','麻章区','440800'),
('440823','遂溪县','440800'),
('440825','徐闻县','440800'),
('440881','廉江市','440800'),
('440882','雷州市','440800'),
('440883','吴川市','440800'),
('440900','茂名市','440000'),
('440902','茂南区','440900'),
('440904','电白区','440900'),
('440981','高州市','440900'),
('440982','化州市','440900'),
('440983','信宜市','440900'),
('441200','肇庆市','440000'),
('441202','端州区','441200'),
('441203','鼎湖区','441200'),
('441223','广宁县','441200'),
('441224','怀集县','441200'),
('441225','封开县','441200'),
('441226','德庆县','441200'),
('441283','高要市','441200'),
('441284','四会市','441200'),
('441300','惠州市','440000'),
('441302','惠城区','441300'),
('441303','惠阳区','441300'),
('441322','博罗县','441300'),
('441323','惠东县','441300'),
('441324','龙门县','441300'),
('441400','梅州市','440000'),
('441402','梅江区','441400'),
('441403','梅县区','441400'),
('441422','大埔县','441400'),
('441423','丰顺县','441400'),
('441424','五华县','441400'),
('441426','平远县','441400'),
('441427','蕉岭县','441400'),
('441481','兴宁市','441400'),
('441500','汕尾市','440000'),
('441502','城区','441500'),
('441521','海丰县','441500'),
('441523','陆河县','441500'),
('441581','陆丰市','441500'),
('441600','河源市','440000'),
('441602','源城区','441600'),
('441621','紫金县','441600'),
('441622','龙川县','441600'),
('441623','连平县','441600'),
('441624','和平县','441600'),
('441625','东源县','441600'),
('441700','阳江市','440000'),
('441702','江城区','441700'),
('441704','阳东区','441700'),
('441721','阳西县','441700'),
('441781','阳春市','441700'),
('441800','清远市','440000'),
('441802','清城区','441800'),
('441803','清新区','441800'),
('441821','佛冈县','441800'),
('441823','阳山县','441800'),
('441825','连山壮族瑶族自治县','441800'),
('441826','连南瑶族自治县','441800'),
('441881','英德市','441800'),
('441882','连州市','441800'),
('441900','东莞市','440000'),
('441901','莞城区','441900'),
('441902','南城区','441900'),
('441904','万江区','441900'),
('441905','石碣镇','441900'),
('441906','石龙镇','441900'),
('441907','茶山镇','441900'),
('441908','石排镇','441900'),
('441909','企石镇','441900'),
('441910','横沥镇','441900'),
('441911','桥头镇','441900'),
('441912','谢岗镇','441900'),
('441913','东坑镇','441900'),
('441914','常平镇','441900'),
('441915','寮步镇','441900'),
('441916','大朗镇','441900'),
('441917','麻涌镇','441900'),
('441918','中堂镇','441900'),
('441919','高埗镇','441900'),
('441920','樟木头镇','441900'),
('441921','大岭山镇','441900'),
('441922','望牛墩镇','441900'),
('441923','黄江镇','441900'),
('441924','洪梅镇','441900'),
('441925','清溪镇','441900'),
('441926','沙田镇','441900'),
('441927','道滘镇','441900'),
('441928','塘厦镇','441900'),
('441929','虎门镇','441900'),
('441930','厚街镇','441900'),
('441931','凤岗镇','441900'),
('441932','长安镇','441900'),
('442000','中山市','440000'),
('442001','石岐区','442000'),
('442004','南区','442000'),
('442005','五桂山区','442000'),
('442006','火炬开发区','442000'),
('442007','黄圃镇','442000'),
('442008','南头镇','442000'),
('442009','东凤镇','442000'),
('442010','阜沙镇','442000'),
('442011','小榄镇','442000'),
('442012','东升镇','442000'),
('442013','古镇镇','442000'),
('442014','横栏镇','442000'),
('442015','三角镇','442000'),
('442016','民众镇','442000'),
('442017','南朗镇','442000'),
('442018','港口镇','442000'),
('442019','大涌镇','442000'),
('442020','沙溪镇','442000'),
('442021','三乡镇','442000'),
('442022','板芙镇','442000'),
('442023','神湾镇','442000'),
('442024','坦洲镇','442000'),
('445100','潮州市','440000'),
('445102','湘桥区','445100'),
('445103','潮安区','445100'),
('445122','饶平县','445100'),
('445200','揭阳市','440000'),
('445202','榕城区','445200'),
('445203','揭东区','445200'),
('445222','揭西县','445200'),
('445224','惠来县','445200'),
('445281','普宁市','445200'),
('445300','云浮市','440000'),
('445302','云城区','445300'),
('445303','云安区','445300'),
('445321','新兴县','445300'),
('445322','郁南县','445300'),
('445381','罗定市','445300'),
('450000','广西壮族自治区','450000'),
('450100','南宁市','450000'),
('450102','兴宁区','450100'),
('450103','青秀区','450100'),
('450105','江南区','450100'),
('450107','西乡塘区','450100'),
('450108','良庆区','450100'),
('450109','邕宁区','450100'),
('450122','武鸣县','450100'),
('450123','隆安县','450100'),
('450124','马山县','450100'),
('450125','上林县','450100'),
('450126','宾阳县','450100'),
('450127','横县','450100'),
('450128','埌东新区','450100'),
('450200','柳州市','450000'),
('450202','城中区','450200'),
('450203','鱼峰区','450200'),
('450204','柳南区','450200'),
('450205','柳北区','450200'),
('450221','柳江县','450200'),
('450222','柳城县','450200'),
('450223','鹿寨县','450200'),
('450224','融安县','450200'),
('450225','融水苗族自治县','450200'),
('450226','三江侗族自治县','450200'),
('450227','柳东新区','450200'),
('450300','桂林市','450000'),
('450302','秀峰区','450300'),
('450303','叠彩区','450300'),
('450304','象山区','450300'),
('450305','七星区','450300'),
('450311','雁山区','450300'),
('450312','临桂区','450300'),
('450321','阳朔县','450300'),
('450323','灵川县','450300'),
('450324','全州县','450300'),
('450325','兴安县','450300'),
('450326','永福县','450300'),
('450327','灌阳县','450300'),
('450328','龙胜各族自治县','450300'),
('450329','资源县','450300'),
('450330','平乐县','450300'),
('450331','荔浦县','450300'),
('450332','恭城瑶族自治县','450300'),
('450400','梧州市','450000'),
('450403','万秀区','450400'),
('450405','长洲区','450400'),
('450406','龙圩区','450400'),
('450421','苍梧县','450400'),
('450422','藤县','450400'),
('450423','蒙山县','450400'),
('450481','岑溪市','450400'),
('450500','北海市','450000'),
('450502','海城区','450500'),
('450503','银海区','450500'),
('450512','铁山港区','450500'),
('450521','合浦县','450500'),
('450600','防城港市','450000'),
('450602','港口区','450600'),
('450603','防城区','450600'),
('450621','上思县','450600'),
('450681','东兴市','450600'),
('450700','钦州市','450000'),
('450702','钦南区','450700'),
('450703','钦北区','450700'),
('450721','灵山县','450700'),
('450722','浦北县','450700'),
('450800','贵港市','450000'),
('450802','港北区','450800'),
('450803','港南区','450800'),
('450804','覃塘区','450800'),
('450821','平南县','450800'),
('450881','桂平市','450800'),
('450900','玉林市','450000'),
('450902','玉州区','450900'),
('450903','福绵区','450900'),
('450904','玉东新区','450900'),
('450921','容县','450900'),
('450922','陆川县','450900'),
('450923','博白县','450900'),
('450924','兴业县','450900'),
('450981','北流市','450900'),
('451000','百色市','450000'),
('451002','右江区','451000'),
('451021','田阳县','451000'),
('451022','田东县','451000'),
('451023','平果县','451000'),
('451024','德保县','451000'),
('451025','靖西县','451000'),
('451026','那坡县','451000'),
('451027','凌云县','451000'),
('451028','乐业县','451000'),
('451029','田林县','451000'),
('451030','西林县','451000'),
('451031','隆林各族自治县','451000'),
('451100','贺州市','450000'),
('451102','八步区','451100'),
('451121','昭平县','451100'),
('451122','钟山县','451100'),
('451123','富川瑶族自治县','451100'),
('451124','平桂管理区','451100'),
('451200','河池市','450000'),
('451202','金城江区','451200'),
('451221','南丹县','451200'),
('451222','天峨县','451200'),
('451223','凤山县','451200'),
('451224','东兰县','451200'),
('451225','罗城仫佬族自治县','451200'),
('451226','环江毛南族自治县','451200'),
('451227','巴马瑶族自治县','451200'),
('451228','都安瑶族自治县','451200'),
('451229','大化瑶族自治县','451200'),
('451281','宜州市','451200'),
('451300','来宾市','450000'),
('451302','兴宾区','451300'),
('451321','忻城县','451300'),
('451322','象州县','451300'),
('451323','武宣县','451300'),
('451324','金秀瑶族自治县','451300'),
('451381','合山市','451300'),
('451400','崇左市','450000'),
('451402','江州区','451400'),
('451421','扶绥县','451400'),
('451422','宁明县','451400'),
('451423','龙州县','451400'),
('451424','大新县','451400'),
('451425','天等县','451400'),
('451481','凭祥市','451400'),
('460000','海南省','460000'),
('460100','海口市','460000'),
('460105','秀英区','460100'),
('460106','龙华区','460100'),
('460107','琼山区','460100'),
('460108','美兰区','460100'),
('460200','三亚市','460000'),
('460202','海棠区','460200'),
('460203','吉阳区','460200'),
('460204','天涯区','460200'),
('460205','崖州区','460200'),
('460300','三沙市','460000'),
('460321','西沙群岛','460300'),
('460322','南沙群岛','460300'),
('460323','中沙群岛','460300'),
('469000','直辖县级','460000'),
('469001','五指山市','469000'),
('469002','琼海市','469000'),
('469003','儋州市','469000'),
('469005','文昌市','469000'),
('469006','万宁市','469000'),
('469007','东方市','469000'),
('469021','定安县','469000'),
('469022','屯昌县','469000'),
('469023','澄迈县','469000'),
('469024','临高县','469000'),
('469025','白沙黎族自治县','469000'),
('469026','昌江黎族自治县','469000'),
('469027','乐东黎族自治县','469000'),
('469028','陵水黎族自治县','469000'),
('469029','保亭黎族苗族自治县','469000'),
('469030','琼中黎族苗族自治县','469000'),
('500000','重庆','500000'),
('500100','重庆市','500000'),
('500101','万州区','500100'),
('500102','涪陵区','500100'),
('500103','渝中区','500100'),
('500104','大渡口区','500100'),
('500105','江北区','500100'),
('500106','沙坪坝区','500100'),
('500107','九龙坡区','500100'),
('500108','南岸区','500100'),
('500109','北碚区','500100'),
('500110','綦江区','500100'),
('500111','大足区','500100'),
('500112','渝北区','500100'),
('500113','巴南区','500100'),
('500114','黔江区','500100'),
('500115','长寿区','500100'),
('500116','江津区','500100'),
('500117','合川区','500100'),
('500118','永川区','500100'),
('500119','南川区','500100'),
('500120','璧山区','500100'),
('500151','铜梁区','500100'),
('500223','潼南县','500100'),
('500226','荣昌县','500100'),
('500228','梁平县','500100'),
('500229','城口县','500100'),
('500230','丰都县','500100'),
('500231','垫江县','500100'),
('500232','武隆县','500100'),
('500233','忠县','500100'),
('500234','开县','500100'),
('500235','云阳县','500100'),
('500236','奉节县','500100'),
('500237','巫山县','500100'),
('500238','巫溪县','500100'),
('500240','石柱土家族自治县','500100'),
('500241','秀山土家族苗族自治县','500100'),
('500242','酉阳土家族苗族自治县','500100'),
('500243','彭水苗族土家族自治县','500100'),
('500300','两江新区','500000'),
('500301','北部新区','500300'),
('500302','保税港区','500300'),
('500303','工业园区','500300'),
('510000','四川省','510000'),
('510100','成都市','510000'),
('510104','锦江区','510100'),
('510105','青羊区','510100'),
('510106','金牛区','510100'),
('510107','武侯区','510100'),
('510108','成华区','510100'),
('510112','龙泉驿区','510100'),
('510113','青白江区','510100'),
('510114','新都区','510100'),
('510115','温江区','510100'),
('510121','金堂县','510100'),
('510122','双流县','510100'),
('510124','郫县','510100'),
('510129','大邑县','510100'),
('510131','蒲江县','510100'),
('510132','新津县','510100'),
('510181','都江堰市','510100'),
('510182','彭州市','510100'),
('510183','邛崃市','510100'),
('510184','崇州市','510100'),
('510300','自贡市','510000'),
('510302','自流井区','510300'),
('510303','贡井区','510300'),
('510304','大安区','510300'),
('510311','沿滩区','510300'),
('510321','荣县','510300'),
('510322','富顺县','510300'),
('510400','攀枝花市','510000'),
('510402','东区','510400'),
('510403','西区','510400'),
('510411','仁和区','510400'),
('510421','米易县','510400'),
('510422','盐边县','510400'),
('510500','泸州市','510000'),
('510502','江阳区','510500'),
('510503','纳溪区','510500'),
('510504','龙马潭区','510500'),
('510521','泸县','510500'),
('510522','合江县','510500'),
('510524','叙永县','510500'),
('510525','古蔺县','510500'),
('510600','德阳市','510000'),
('510603','旌阳区','510600'),
('510623','中江县','510600'),
('510626','罗江县','510600'),
('510681','广汉市','510600'),
('510682','什邡市','510600'),
('510683','绵竹市','510600'),
('510700','绵阳市','510000'),
('510703','涪城区','510700'),
('510704','游仙区','510700'),
('510722','三台县','510700'),
('510723','盐亭县','510700'),
('510724','安县','510700'),
('510725','梓潼县','510700'),
('510726','北川羌族自治县','510700'),
('510727','平武县','510700'),
('510781','江油市','510700'),
('510800','广元市','510000'),
('510802','利州区','510800'),
('510811','昭化区','510800'),
('510812','朝天区','510800'),
('510821','旺苍县','510800'),
('510822','青川县','510800'),
('510823','剑阁县','510800'),
('510824','苍溪县','510800'),
('510900','遂宁市','510000'),
('510903','船山区','510900'),
('510904','安居区','510900'),
('510921','蓬溪县','510900'),
('510922','射洪县','510900'),
('510923','大英县','510900'),
('511000','内江市','510000'),
('511002','市中区','511000'),
('511011','东兴区','511000'),
('511024','威远县','511000'),
('511025','资中县','511000'),
('511028','隆昌县','511000'),
('511100','乐山市','510000'),
('511102','市中区','511100'),
('511111','沙湾区','511100'),
('511112','五通桥区','511100'),
('511113','金口河区','511100'),
('511123','犍为县','511100'),
('511124','井研县','511100'),
('511126','夹江县','511100'),
('511129','沐川县','511100'),
('511132','峨边彝族自治县','511100'),
('511133','马边彝族自治县','511100'),
('511181','峨眉山市','511100'),
('511300','南充市','510000'),
('511302','顺庆区','511300'),
('511303','高坪区','511300'),
('511304','嘉陵区','511300'),
('511321','南部县','511300'),
('511322','营山县','511300'),
('511323','蓬安县','511300'),
('511324','仪陇县','511300'),
('511325','西充县','511300'),
('511381','阆中市','511300'),
('511400','眉山市','510000'),
('511402','东坡区','511400'),
('511403','彭山区','511400'),
('511421','仁寿县','511400'),
('511423','洪雅县','511400'),
('511424','丹棱县','511400'),
('511425','青神县','511400'),
('511500','宜宾市','510000'),
('511502','翠屏区','511500'),
('511503','南溪区','511500'),
('511521','宜宾县','511500'),
('511523','江安县','511500'),
('511524','长宁县','511500'),
('511525','高县','511500'),
('511526','珙县','511500'),
('511527','筠连县','511500'),
('511528','兴文县','511500'),
('511529','屏山县','511500'),
('511600','广安市','510000'),
('511602','广安区','511600'),
('511603','前锋区','511600'),
('511621','岳池县','511600'),
('511622','武胜县','511600'),
('511623','邻水县','511600'),
('511681','华蓥市','511600'),
('511700','达州市','510000'),
('511702','通川区','511700'),
('511703','达川区','511700'),
('511722','宣汉县','511700'),
('511723','开江县','511700'),
('511724','大竹县','511700'),
('511725','渠县','511700'),
('511781','万源市','511700'),
('511800','雅安市','510000'),
('511802','雨城区','511800'),
('511803','名山区','511800'),
('511822','荥经县','511800'),
('511823','汉源县','511800'),
('511824','石棉县','511800'),
('511825','天全县','511800'),
('511826','芦山县','511800'),
('511827','宝兴县','511800'),
('511900','巴中市','510000'),
('511902','巴州区','511900'),
('511903','恩阳区','511900'),
('511921','通江县','511900'),
('511922','南江县','511900'),
('511923','平昌县','511900'),
('512000','资阳市','510000'),
('512002','雁江区','512000'),
('512021','安岳县','512000'),
('512022','乐至县','512000'),
('512081','简阳市','512000'),
('513200','阿坝藏族羌族自治州','510000'),
('513221','汶川县','513200'),
('513222','理县','513200'),
('513223','茂县','513200'),
('513224','松潘县','513200'),
('513225','九寨沟县','513200'),
('513226','金川县','513200'),
('513227','小金县','513200'),
('513228','黑水县','513200'),
('513229','马尔康县','513200'),
('513230','壤塘县','513200'),
('513231','阿坝县','513200'),
('513232','若尔盖县','513200'),
('513233','红原县','513200'),
('513300','甘孜藏族自治州','510000'),
('513321','康定县','513300'),
('513322','泸定县','513300'),
('513323','丹巴县','513300'),
('513324','九龙县','513300'),
('513325','雅江县','513300'),
('513326','道孚县','513300'),
('513327','炉霍县','513300'),
('513328','甘孜县','513300'),
('513329','新龙县','513300'),
('513330','德格县','513300'),
('513331','白玉县','513300'),
('513332','石渠县','513300'),
('513333','色达县','513300'),
('513334','理塘县','513300'),
('513335','巴塘县','513300'),
('513336','乡城县','513300'),
('513337','稻城县','513300'),
('513338','得荣县','513300'),
('513400','凉山彝族自治州','510000'),
('513401','西昌市','513400'),
('513422','木里藏族自治县','513400'),
('513423','盐源县','513400'),
('513424','德昌县','513400'),
('513425','会理县','513400'),
('513426','会东县','513400'),
('513427','宁南县','513400'),
('513428','普格县','513400'),
('513429','布拖县','513400'),
('513430','金阳县','513400'),
('513431','昭觉县','513400'),
('513432','喜德县','513400'),
('513433','冕宁县','513400'),
('513434','越西县','513400'),
('513435','甘洛县','513400'),
('513436','美姑县','513400'),
('513437','雷波县','513400'),
('520000','贵州省','520000'),
('520100','贵阳市','520000'),
('520102','南明区','520100'),
('520103','云岩区','520100'),
('520111','花溪区','520100'),
('520112','乌当区','520100'),
('520113','白云区','520100'),
('520115','观山湖区','520100'),
('520121','开阳县','520100'),
('520122','息烽县','520100'),
('520123','修文县','520100'),
('520181','清镇市','520100'),
('520200','六盘水市','520000'),
('520201','钟山区','520200'),
('520203','六枝特区','520200'),
('520221','水城县','520200'),
('520222','盘县','520200'),
('520300','遵义市','520000'),
('520302','红花岗区','520300'),
('520303','汇川区','520300'),
('520321','遵义县','520300'),
('520322','桐梓县','520300'),
('520323','绥阳县','520300'),
('520324','正安县','520300'),
('520325','道真仡佬族苗族自治县','520300'),
('520326','务川仡佬族苗族自治县','520300'),
('520327','凤冈县','520300'),
('520328','湄潭县','520300'),
('520329','余庆县','520300'),
('520330','习水县','520300'),
('520381','赤水市','520300'),
('520382','仁怀市','520300'),
('520400','安顺市','520000'),
('520402','西秀区','520400'),
('520421','平坝区','520400'),
('520422','普定县','520400'),
('520423','镇宁布依族苗族自治县','520400'),
('520424','关岭布依族苗族自治县','520400'),
('520425','紫云苗族布依族自治县','520400'),
('520500','毕节市','520000'),
('520502','七星关区','520500'),
('520521','大方县','520500'),
('520522','黔西县','520500'),
('520523','金沙县','520500'),
('520524','织金县','520500'),
('520525','纳雍县','520500'),
('520526','威宁彝族回族苗族自治县','520500'),
('520527','赫章县','520500'),
('520600','铜仁市','520000'),
('520602','碧江区','520600'),
('520603','万山区','520600'),
('520621','江口县','520600'),
('520622','玉屏侗族自治县','520600'),
('520623','石阡县','520600'),
('520624','思南县','520600'),
('520625','印江土家族苗族自治县','520600'),
('520626','德江县','520600'),
('520627','沿河土家族自治县','520600'),
('520628','松桃苗族自治县','520600'),
('522300','黔西南布依族苗族自治州','520000'),
('522301','兴义市 ','522300'),
('522322','兴仁县','522300'),
('522323','普安县','522300'),
('522324','晴隆县','522300'),
('522325','贞丰县','522300'),
('522326','望谟县','522300'),
('522327','册亨县','522300'),
('522328','安龙县','522300'),
('522600','黔东南苗族侗族自治州','520000'),
('522601','凯里市','522600'),
('522622','黄平县','522600'),
('522623','施秉县','522600'),
('522624','三穗县','522600'),
('522625','镇远县','522600'),
('522626','岑巩县','522600'),
('522627','天柱县','522600'),
('522628','锦屏县','522600'),
('522629','剑河县','522600'),
('522630','台江县','522600'),
('522631','黎平县','522600'),
('522632','榕江县','522600'),
('522633','从江县','522600'),
('522634','雷山县','522600'),
('522635','麻江县','522600'),
('522636','丹寨县','522600'),
('522700','黔南布依族苗族自治州','520000'),
('522701','都匀市','522700'),
('522702','福泉市','522700'),
('522722','荔波县','522700'),
('522723','贵定县','522700'),
('522725','瓮安县','522700'),
('522726','独山县','522700'),
('522727','平塘县','522700'),
('522728','罗甸县','522700'),
('522729','长顺县','522700'),
('522730','龙里县','522700'),
('522731','惠水县','522700'),
('522732','三都水族自治县','522700'),
('530000','云南省','530000'),
('530100','昆明市','530000'),
('530102','五华区','530100'),
('530103','盘龙区','530100'),
('530111','官渡区','530100'),
('530112','西山区','530100'),
('530113','东川区','530100'),
('530114','呈贡区','530100'),
('530122','晋宁县','530100'),
('530124','富民县','530100'),
('530125','宜良县','530100'),
('530126','石林彝族自治县','530100'),
('530127','嵩明县','530100'),
('530128','禄劝彝族苗族自治县','530100'),
('530129','寻甸回族彝族自治县 ','530100'),
('530181','安宁市','530100'),
('530300','曲靖市','530000'),
('530302','麒麟区','530300'),
('530321','马龙县','530300'),
('530322','陆良县','530300'),
('530323','师宗县','530300'),
('530324','罗平县','530300'),
('530325','富源县','530300'),
('530326','会泽县','530300'),
('530328','沾益县','530300'),
('530381','宣威市','530300'),
('530400','玉溪市','530000'),
('530402','红塔区','530400'),
('530421','江川县','530400'),
('530422','澄江县','530400'),
('530423','通海县','530400'),
('530424','华宁县','530400'),
('530425','易门县','530400'),
('530426','峨山彝族自治县','530400'),
('530427','新平彝族傣族自治县','530400'),
('530428','元江哈尼族彝族傣族自治县','530400'),
('530500','保山市','530000'),
('530502','隆阳区','530500'),
('530521','施甸县','530500'),
('530522','腾冲县','530500'),
('530523','龙陵县','530500'),
('530524','昌宁县','530500'),
('530600','昭通市','530000'),
('530602','昭阳区','530600'),
('530621','鲁甸县','530600'),
('530622','巧家县','530600'),
('530623','盐津县','530600'),
('530624','大关县','530600'),
('530625','永善县','530600'),
('530626','绥江县','530600'),
('530627','镇雄县','530600'),
('530628','彝良县','530600'),
('530629','威信县','530600'),
('530630','水富县','530600'),
('530700','丽江市','530000'),
('530702','古城区','530700'),
('530721','玉龙纳西族自治县','530700'),
('530722','永胜县','530700'),
('530723','华坪县','530700'),
('530724','宁蒗彝族自治县','530700'),
('530800','普洱市','530000'),
('530802','思茅区','530800'),
('530821','宁洱哈尼族彝族自治县','530800'),
('530822','墨江哈尼族自治县','530800'),
('530823','景东彝族自治县','530800'),
('530824','景谷傣族彝族自治县','530800'),
('530825','镇沅彝族哈尼族拉祜族自治县','530800'),
('530826','江城哈尼族彝族自治县','530800'),
('530827','孟连傣族拉祜族佤族自治县','530800'),
('530828','澜沧拉祜族自治县','530800'),
('530829','西盟佤族自治县','530800'),
('530900','临沧市','530000'),
('530902','临翔区','530900'),
('530921','凤庆县','530900'),
('530922','云县','530900'),
('530923','永德县','530900'),
('530924','镇康县','530900'),
('530925','双江拉祜族佤族布朗族傣族自治县','530900'),
('530926','耿马傣族佤族自治县','530900'),
('530927','沧源佤族自治县','530900'),
('532300','楚雄彝族自治州','530000'),
('532301','楚雄市','532300'),
('532322','双柏县','532300'),
('532323','牟定县','532300'),
('532324','南华县','532300'),
('532325','姚安县','532300'),
('532326','大姚县','532300'),
('532327','永仁县','532300'),
('532328','元谋县','532300'),
('532329','武定县','532300'),
('532331','禄丰县','532300'),
('532500','红河哈尼族彝族自治州','530000'),
('532501','个旧市','532500'),
('532502','开远市','532500'),
('532503','蒙自市','532500'),
('532504','弥勒市','532500'),
('532523','屏边苗族自治县','532500'),
('532524','建水县','532500'),
('532525','石屏县','532500'),
('532527','泸西县','532500'),
('532528','元阳县','532500'),
('532529','红河县','532500'),
('532530','金平苗族瑶族傣族自治县','532500'),
('532531','绿春县','532500'),
('532532','河口瑶族自治县','532500'),
('532600','文山壮族苗族自治州','530000'),
('532601','文山市','532600'),
('532622','砚山县','532600'),
('532623','西畴县','532600'),
('532624','麻栗坡县','532600'),
('532625','马关县','532600'),
('532626','丘北县','532600'),
('532627','广南县','532600'),
('532628','富宁县','532600'),
('532800','西双版纳傣族自治州','530000'),
('532801','景洪市','532800'),
('532822','勐海县','532800'),
('532823','勐腊县','532800'),
('532900','大理白族自治州','530000'),
('532901','大理市','532900'),
('532922','漾濞彝族自治县','532900'),
('532923','祥云县','532900'),
('532924','宾川县','532900'),
('532925','弥渡县','532900'),
('532926','南涧彝族自治县','532900'),
('532927','巍山彝族回族自治县','532900'),
('532928','永平县','532900'),
('532929','云龙县','532900'),
('532930','洱源县','532900'),
('532931','剑川县','532900'),
('532932','鹤庆县','532900'),
('533100','德宏傣族景颇族自治州','530000'),
('533102','瑞丽市','533100'),
('533103','芒市','533100'),
('533122','梁河县','533100'),
('533123','盈江县','533100'),
('533124','陇川县','533100'),
('533300','怒江傈僳族自治州','530000'),
('533321','泸水县','533300'),
('533323','福贡县','533300'),
('533324','贡山独龙族怒族自治县','533300'),
('533325','兰坪白族普米族自治县','533300'),
('533400','迪庆藏族自治州','530000'),
('533421','香格里拉市','533400'),
('533422','德钦县','533400'),
('533423','维西傈僳族自治县','533400'),
('540000','西藏自治区','540000'),
('540100','拉萨市','540000'),
('540102','城关区','540100'),
('540121','林周县','540100'),
('540122','当雄县','540100'),
('540123','尼木县','540100'),
('540124','曲水县','540100'),
('540125','堆龙德庆县','540100'),
('540126','达孜县','540100'),
('540127','墨竹工卡县','540100'),
('540200','日喀则市','540000'),
('540202','桑珠孜区','540200'),
('540221','南木林县','540200'),
('540222','江孜县','540200'),
('540223','定日县','540200'),
('540224','萨迦县','540200'),
('540225','拉孜县','540200'),
('540226','昂仁县','540200'),
('540227','谢通门县','540200'),
('540228','白朗县','540200'),
('540229','仁布县','540200'),
('540230','康马县','540200'),
('540231','定结县','540200'),
('540232','仲巴县','540200'),
('540233','亚东县','540200'),
('540234','吉隆县','540200'),
('540235','聂拉木县','540200'),
('540236','萨嘎县','540200'),
('540237','岗巴县','540200'),
('540300','昌都市','540000'),
('540302','卡若区','540300'),
('540321','江达县','540300'),
('540322','贡觉县','540300'),
('540323','类乌齐县','540300'),
('540324','丁青县','540300'),
('540325','察雅县','540300'),
('540326','八宿县','540300'),
('540327','左贡县','540300'),
('540328','芒康县','540300'),
('540329','洛隆县','540300'),
('540330','边坝县','540300'),
('542200','山南地区','540000'),
('542221','乃东县','542200'),
('542222','扎囊县','542200'),
('542223','贡嘎县','542200'),
('542224','桑日县','542200'),
('542225','琼结县','542200'),
('542226','曲松县','542200'),
('542227','措美县','542200'),
('542228','洛扎县','542200'),
('542229','加查县','542200'),
('542231','隆子县','542200'),
('542232','错那县','542200'),
('542233','浪卡子县','542200'),
('542400','那曲地区','540000'),
('542421','那曲县','542400'),
('542422','嘉黎县','542400'),
('542423','比如县','542400'),
('542424','聂荣县','542400'),
('542425','安多县','542400'),
('542426','申扎县','542400'),
('542427','索县','542400'),
('542428','班戈县','542400'),
('542429','巴青县','542400'),
('542430','尼玛县','542400'),
('542431','双湖县','542400'),
('542500','阿里地区','540000'),
('542521','普兰县','542500'),
('542522','札达县','542500'),
('542523','噶尔县','542500'),
('542524','日土县','542500'),
('542525','革吉县','542500'),
('542526','改则县','542500'),
('542527','措勤县','542500'),
('542600','林芝地区','540000'),
('542621','林芝县','542600'),
('542622','工布江达县','542600'),
('542623','米林县','542600'),
('542624','墨脱县','542600'),
('542625','波密县','542600'),
('542626','察隅县','542600'),
('542627','朗县','542600'),
('610000','陕西省','610000'),
('610100','西安市','610000'),
('610102','新城区','610100'),
('610103','碑林区','610100'),
('610104','莲湖区','610100'),
('610111','灞桥区','610100'),
('610112','未央区','610100'),
('610113','雁塔区','610100'),
('610114','阎良区','610100'),
('610115','临潼区','610100'),
('610116','长安区','610100'),
('610122','蓝田县','610100'),
('610124','周至县','610100'),
('610125','户县','610100'),
('610126','高陵区','610100'),
('610200','铜川市','610000'),
('610202','王益区','610200'),
('610203','印台区','610200'),
('610204','耀州区','610200'),
('610222','宜君县','610200'),
('610300','宝鸡市','610000'),
('610302','渭滨区','610300'),
('610303','金台区','610300'),
('610304','陈仓区','610300'),
('610322','凤翔县','610300'),
('610323','岐山县','610300'),
('610324','扶风县','610300'),
('610326','眉县','610300'),
('610327','陇县','610300'),
('610328','千阳县','610300'),
('610329','麟游县','610300'),
('610330','凤县','610300'),
('610331','太白县','610300'),
('610400','咸阳市','610000'),
('610402','秦都区','610400'),
('610403','杨陵区','610400'),
('610404','渭城区','610400'),
('610422','三原县','610400'),
('610423','泾阳县','610400'),
('610424','乾县','610400'),
('610425','礼泉县','610400'),
('610426','永寿县','610400'),
('610427','彬县','610400'),
('610428','长武县','610400'),
('610429','旬邑县','610400'),
('610430','淳化县','610400'),
('610431','武功县','610400'),
('610481','兴平市','610400'),
('610500','渭南市','610000'),
('610502','临渭区','610500'),
('610521','华县','610500'),
('610522','潼关县','610500'),
('610523','大荔县','610500'),
('610524','合阳县','610500'),
('610525','澄城县','610500'),
('610526','蒲城县','610500'),
('610527','白水县','610500'),
('610528','富平县','610500'),
('610581','韩城市','610500'),
('610582','华阴市','610500'),
('610600','延安市','610000'),
('610602','宝塔区','610600'),
('610621','延长县','610600'),
('610622','延川县','610600'),
('610623','子长县','610600'),
('610624','安塞县','610600'),
('610625','志丹县','610600'),
('610626','吴起县','610600'),
('610627','甘泉县','610600'),
('610628','富县','610600'),
('610629','洛川县','610600'),
('610630','宜川县','610600'),
('610631','黄龙县','610600'),
('610632','黄陵县','610600'),
('610700','汉中市','610000'),
('610702','汉台区','610700'),
('610721','南郑县','610700'),
('610722','城固县','610700'),
('610723','洋县','610700'),
('610724','西乡县','610700'),
('610725','勉县','610700'),
('610726','宁强县','610700'),
('610727','略阳县','610700'),
('610728','镇巴县','610700'),
('610729','留坝县','610700'),
('610730','佛坪县','610700'),
('610800','榆林市','610000'),
('610802','榆阳区','610800'),
('610821','神木县','610800'),
('610822','府谷县','610800'),
('610823','横山县','610800'),
('610824','靖边县','610800'),
('610825','定边县','610800'),
('610826','绥德县','610800'),
('610827','米脂县','610800'),
('610828','佳县','610800'),
('610829','吴堡县','610800'),
('610830','清涧县','610800'),
('610831','子洲县','610800'),
('610900','安康市','610000'),
('610902','汉滨区','610900'),
('610921','汉阴县','610900'),
('610922','石泉县','610900'),
('610923','宁陕县','610900'),
('610924','紫阳县','610900'),
('610925','岚皋县','610900'),
('610926','平利县','610900'),
('610927','镇坪县','610900'),
('610928','旬阳县','610900'),
('610929','白河县','610900'),
('611000','商洛市','610000'),
('611002','商州区','611000'),
('611021','洛南县','611000'),
('611022','丹凤县','611000'),
('611023','商南县','611000'),
('611024','山阳县','611000'),
('611025','镇安县','611000'),
('611026','柞水县','611000'),
('611100','西咸新区','610000'),
('611101','空港新城','611100'),
('611102','沣东新城','611100'),
('611103','秦汉新城','611100'),
('611104','沣西新城','611100'),
('611105','泾河新城','611100'),
('620000','甘肃省','620000'),
('620100','兰州市','620000'),
('620102','城关区','620100'),
('620103','七里河区','620100'),
('620104','西固区','620100'),
('620105','安宁区','620100'),
('620111','红古区','620100'),
('620121','永登县','620100'),
('620122','皋兰县','620100'),
('620123','榆中县','620100'),
('620200','嘉峪关市','620000'),
('620201','雄关区','620200'),
('620202','长城区','620200'),
('620203','镜铁区','620200'),
('620300','金昌市','620000'),
('620302','金川区','620300'),
('620321','永昌县','620300'),
('620400','白银市','620000'),
('620402','白银区','620400'),
('620403','平川区','620400'),
('620421','靖远县','620400'),
('620422','会宁县','620400'),
('620423','景泰县','620400'),
('620500','天水市','620000'),
('620502','秦州区','620500'),
('620503','麦积区','620500'),
('620521','清水县','620500'),
('620522','秦安县','620500'),
('620523','甘谷县','620500'),
('620524','武山县','620500'),
('620525','张家川回族自治县','620500'),
('620600','武威市','620000'),
('620602','凉州区','620600'),
('620621','民勤县','620600'),
('620622','古浪县','620600'),
('620623','天祝藏族自治县','620600'),
('620700','张掖市','620000'),
('620702','甘州区','620700'),
('620721','肃南裕固族自治县','620700'),
('620722','民乐县','620700'),
('620723','临泽县','620700'),
('620724','高台县','620700'),
('620725','山丹县','620700'),
('620800','平凉市','620000'),
('620802','崆峒区','620800'),
('620821','泾川县','620800'),
('620822','灵台县','620800'),
('620823','崇信县','620800'),
('620824','华亭县','620800'),
('620825','庄浪县','620800'),
('620826','静宁县','620800'),
('620900','酒泉市','620000'),
('620902','肃州区','620900'),
('620921','金塔县','620900'),
('620922','瓜州县','620900'),
('620923','肃北蒙古族自治县','620900'),
('620924','阿克塞哈萨克族自治县','620900'),
('620981','玉门市','620900'),
('620982','敦煌市','620900'),
('621000','庆阳市','620000'),
('621002','西峰区','621000'),
('621021','庆城县','621000'),
('621022','环县','621000'),
('621023','华池县','621000'),
('621024','合水县','621000'),
('621025','正宁县','621000'),
('621026','宁县','621000'),
('621027','镇原县','621000'),
('621100','定西市','620000'),
('621102','安定区','621100'),
('621121','通渭县','621100'),
('621122','陇西县','621100'),
('621123','渭源县','621100'),
('621124','临洮县','621100'),
('621125','漳县','621100'),
('621126','岷县','621100'),
('621200','陇南市','620000'),
('621202','武都区','621200'),
('621221','成县','621200'),
('621222','文县','621200'),
('621223','宕昌县','621200'),
('621224','康县','621200'),
('621225','西和县','621200'),
('621226','礼县','621200'),
('621227','徽县','621200'),
('621228','两当县','621200'),
('622900','临夏回族自治州','620000'),
('622901','临夏市','622900'),
('622921','临夏县','622900'),
('622922','康乐县','622900'),
('622923','永靖县','622900'),
('622924','广河县','622900'),
('622925','和政县','622900'),
('622926','东乡族自治县','622900'),
('622927','积石山保安族东乡族撒拉族自治县','622900'),
('623000','甘南藏族自治州','620000'),
('623001','合作市','623000'),
('623021','临潭县','623000'),
('623022','卓尼县','623000'),
('623023','舟曲县','623000'),
('623024','迭部县','623000'),
('623025','玛曲县','623000'),
('623026','碌曲县','623000'),
('623027','夏河县','623000'),
('630000','青海省','630000'),
('630100','西宁市','630000'),
('630102','城东区','630100'),
('630103','城中区','630100'),
('630104','城西区','630100'),
('630105','城北区','630100'),
('630121','大通回族土族自治县','630100'),
('630122','湟中县','630100'),
('630123','湟源县','630100'),
('630200','海东市','630000'),
('630202','乐都区','630200'),
('630221','平安县','630200'),
('630222','民和回族土族自治县','630200'),
('630223','互助土族自治县','630200'),
('630224','化隆回族自治县','630200'),
('630225','循化撒拉族自治县','630200'),
('632200','海北藏族自治州','630000'),
('632221','门源回族自治县','632200'),
('632222','祁连县','632200'),
('632223','海晏县','632200'),
('632224','刚察县','632200'),
('632300','黄南藏族自治州','630000'),
('632321','同仁县','632300'),
('632322','尖扎县','632300'),
('632323','泽库县','632300'),
('632324','河南蒙古族自治县','632300'),
('632500','海南藏族自治州','630000'),
('632521','共和县','632500'),
('632522','同德县','632500'),
('632523','贵德县','632500'),
('632524','兴海县','632500'),
('632525','贵南县','632500'),
('632600','果洛藏族自治州','630000'),
('632621','玛沁县','632600'),
('632622','班玛县','632600'),
('632623','甘德县','632600'),
('632624','达日县','632600'),
('632625','久治县','632600'),
('632626','玛多县','632600'),
('632700','玉树藏族自治州','630000'),
('632701','玉树市','632700'),
('632722','杂多县','632700'),
('632723','称多县','632700'),
('632724','治多县','632700'),
('632725','囊谦县','632700'),
('632726','曲麻莱县','632700'),
('632800','海西蒙古族藏族自治州','630000'),
('632801','格尔木市','632800'),
('632802','德令哈市','632800'),
('632821','乌兰县','632800'),
('632822','都兰县','632800'),
('632823','天峻县','632800'),
('640000','宁夏回族自治区','640000'),
('640100','银川市','640000'),
('640104','兴庆区','640100'),
('640105','西夏区','640100'),
('640106','金凤区','640100'),
('640121','永宁县','640100'),
('640122','贺兰县','640100'),
('640181','灵武市','640100'),
('640200','石嘴山市','640000'),
('640202','大武口区','640200'),
('640205','惠农区','640200'),
('640221','平罗县','640200'),
('640300','吴忠市','640000'),
('640302','利通区','640300'),
('640303','红寺堡区','640300'),
('640323','盐池县','640300'),
('640324','同心县','640300'),
('640381','青铜峡市','640300'),
('640400','固原市','640000'),
('640402','原州区','640400'),
('640422','西吉县','640400'),
('640423','隆德县','640400'),
('640424','泾源县','640400'),
('640425','彭阳县','640400'),
('640500','中卫市','640000'),
('640502','沙坡头区','640500'),
('640521','中宁县','640500'),
('640522','海原县','640500'),
('650000','新疆维吾尔自治区','650000'),
('650100','乌鲁木齐市','650000'),
('650102','天山区','650100'),
('650103','沙依巴克区','650100'),
('650104','新市区','650100'),
('650105','水磨沟区','650100'),
('650106','头屯河区','650100'),
('650107','达坂城区','650100'),
('650109','米东区','650100'),
('650121','乌鲁木齐县','650100'),
('650200','克拉玛依市','650000'),
('650202','独山子区','650200'),
('650203','克拉玛依区','650200'),
('650204','白碱滩区','650200'),
('650205','乌尔禾区','650200'),
('652100','吐鲁番地区','650000'),
('652101','吐鲁番市','652100'),
('652122','鄯善县','652100'),
('652123','托克逊县','652100'),
('652200','哈密地区','650000'),
('652201','哈密市','652200'),
('652222','巴里坤哈萨克自治县','652200'),
('652223','伊吾县','652200'),
('652300','昌吉回族自治州','650000'),
('652301','昌吉市','652300'),
('652302','阜康市','652300'),
('652323','呼图壁县','652300'),
('652324','玛纳斯县','652300'),
('652325','奇台县','652300'),
('652327','吉木萨尔县','652300'),
('652328','木垒哈萨克自治县','652300'),
('652700','博尔塔拉蒙古自治州','650000'),
('652701','博乐市','652700'),
('652702','阿拉山口市','652700'),
('652722','精河县','652700'),
('652723','温泉县','652700'),
('652800','巴音郭楞蒙古自治州','650000'),
('652801','库尔勒市','652800'),
('652822','轮台县','652800'),
('652823','尉犁县','652800'),
('652824','若羌县','652800'),
('652825','且末县','652800'),
('652826','焉耆回族自治县','652800'),
('652827','和静县','652800'),
('652828','和硕县','652800'),
('652829','博湖县','652800'),
('652900','阿克苏地区','650000'),
('652901','阿克苏市','652900'),
('652922','温宿县','652900'),
('652923','库车县','652900'),
('652924','沙雅县','652900'),
('652925','新和县','652900'),
('652926','拜城县','652900'),
('652927','乌什县','652900'),
('652928','阿瓦提县','652900'),
('652929','柯坪县','652900'),
('653000','克孜勒苏柯尔克孜自治州','650000'),
('653001','阿图什市','653000'),
('653022','阿克陶县','653000'),
('653023','阿合奇县','653000'),
('653024','乌恰县','653000'),
('653100','喀什地区','650000'),
('653101','喀什市','653100'),
('653121','疏附县','653100'),
('653122','疏勒县','653100'),
('653123','英吉沙县','653100'),
('653124','泽普县','653100'),
('653125','莎车县','653100'),
('653126','叶城县','653100'),
('653127','麦盖提县','653100'),
('653128','岳普湖县','653100'),
('653129','伽师县','653100'),
('653130','巴楚县','653100'),
('653131','塔什库尔干塔吉克自治县','653100'),
('653200','和田地区','650000'),
('653201','和田市','653200'),
('653221','和田县','653200'),
('653222','墨玉县','653200'),
('653223','皮山县','653200'),
('653224','洛浦县','653200'),
('653225','策勒县','653200'),
('653226','于田县','653200'),
('653227','民丰县','653200'),
('654000','伊犁哈萨克自治州','650000'),
('654002','伊宁市','654000'),
('654003','奎屯市','654000'),
('654004','霍尔果斯市','654000'),
('654021','伊宁县','654000'),
('654022','察布查尔锡伯自治县','654000'),
('654023','霍城县','654000'),
('654024','巩留县','654000'),
('654025','新源县','654000'),
('654026','昭苏县','654000'),
('654027','特克斯县','654000'),
('654028','尼勒克县','654000'),
('654200','塔城地区','650000'),
('654201','塔城市','654200'),
('654202','乌苏市','654200'),
('654221','额敏县','654200'),
('654223','沙湾县','654200'),
('654224','托里县','654200'),
('654225','裕民县','654200'),
('654226','和布克赛尔蒙古自治县','654200'),
('654300','阿勒泰地区','650000'),
('654301','阿勒泰市','654300'),
('654321','布尔津县','654300'),
('654322','富蕴县','654300'),
('654323','福海县','654300'),
('654324','哈巴河县','654300'),
('654325','青河县','654300'),
('654326','吉木乃县','654300'),
('659000','直辖县级','650000'),
('659001','石河子市','659000'),
('659002','阿拉尔市','659000'),
('659003','图木舒克市','659000'),
('659004','五家渠市','659000'),
('659005','北屯市','659000'),
('659006','铁门关市','659000'),
('659007','双河市','659000'),
('710000','台湾','710000'),
('710100','台北市','710000'),
('710101','松山区','710100'),
('710102','信义区','710100'),
('710103','大安区','710100'),
('710104','中山区','710100'),
('710105','中正区','710100'),
('710106','大同区','710100'),
('710107','万华区','710100'),
('710108','文山区','710100'),
('710109','南港区','710100'),
('710110','内湖区','710100'),
('710111','士林区','710100'),
('710112','北投区','710100'),
('710200','高雄市','710000'),
('710201','盐埕区','710200'),
('710202','鼓山区','710200'),
('710203','左营区','710200'),
('710204','楠梓区','710200'),
('710205','三民区','710200'),
('710206','新兴区','710200'),
('710207','前金区','710200'),
('710208','苓雅区','710200'),
('710209','前镇区','710200'),
('710210','旗津区','710200'),
('710211','小港区','710200'),
('710212','凤山区','710200'),
('710213','林园区','710200'),
('710214','大寮区','710200'),
('710215','大树区','710200'),
('710216','大社区','710200'),
('710217','仁武区','710200'),
('710218','鸟松区','710200'),
('710219','冈山区','710200'),
('710220','桥头区','710200'),
('710221','燕巢区','710200'),
('710222','田寮区','710200'),
('710223','阿莲区','710200'),
('710224','路竹区','710200'),
('710225','湖内区','710200'),
('710226','茄萣区','710200'),
('710227','永安区','710200'),
('710228','弥陀区','710200'),
('710229','梓官区','710200'),
('710230','旗山区','710200'),
('710231','美浓区','710200'),
('710232','六龟区','710200'),
('710233','甲仙区','710200'),
('710234','杉林区','710200'),
('710235','内门区','710200'),
('710236','茂林区','710200'),
('710237','桃源区','710200'),
('710238','那玛夏区','710200'),
('710300','基隆市','710000'),
('710301','中正区','710300'),
('710302','七堵区','710300'),
('710303','暖暖区','710300'),
('710304','仁爱区','710300'),
('710305','中山区','710300'),
('710306','安乐区','710300'),
('710307','信义区','710300'),
('710400','台中市','710000'),
('710401','中区','710400'),
('710402','东区','710400'),
('710403','南区','710400'),
('710404','西区','710400'),
('710405','北区','710400'),
('710406','西屯区','710400'),
('710407','南屯区','710400'),
('710408','北屯区','710400'),
('710409','丰原区','710400'),
('710410','东势区','710400'),
('710411','大甲区','710400'),
('710412','清水区','710400'),
('710413','沙鹿区','710400'),
('710414','梧栖区','710400'),
('710415','后里区','710400'),
('710416','神冈区','710400'),
('710417','潭子区','710400'),
('710418','大雅区','710400'),
('710419','新社区','710400'),
('710420','石冈区','710400'),
('710421','外埔区','710400'),
('710422','大安区','710400'),
('710423','乌日区','710400'),
('710424','大肚区','710400'),
('710425','龙井区','710400'),
('710426','雾峰区','710400'),
('710427','太平区','710400'),
('710428','大里区','710400'),
('710429','和平区','710400'),
('710500','台南市','710000'),
('710501','东区','710500'),
('710502','南区','710500'),
('710504','北区','710500'),
('710506','安南区','710500'),
('710507','安平区','710500'),
('710508','中西区','710500'),
('710509','新营区','710500'),
('710510','盐水区','710500'),
('710511','白河区','710500'),
('710512','柳营区','710500'),
('710513','后壁区','710500'),
('710514','东山区','710500'),
('710515','麻豆区','710500'),
('710516','下营区','710500'),
('710517','六甲区','710500'),
('710518','官田区','710500'),
('710519','大内区','710500'),
('710520','佳里区','710500'),
('710521','学甲区','710500'),
('710522','西港区','710500'),
('710523','七股区','710500'),
('710524','将军区','710500'),
('710525','北门区','710500'),
('710526','新化区','710500'),
('710527','善化区','710500'),
('710528','新市区','710500'),
('710529','安定区','710500'),
('710530','山上区','710500'),
('710531','玉井区','710500'),
('710532','楠西区','710500'),
('710533','南化区','710500'),
('710534','左镇区','710500'),
('710535','仁德区','710500'),
('710536','归仁区','710500'),
('710537','关庙区','710500'),
('710538','龙崎区','710500'),
('710539','永康区','710500'),
('710600','新竹市','710000'),
('710601','东区','710600'),
('710602','北区','710600'),
('710603','香山区','710600'),
('710700','嘉义市','710000'),
('710701','东区','710700'),
('710702','西区','710700'),
('710800','新北市','710000'),
('710801','板桥区','710800'),
('710802','三重区','710800'),
('710803','中和区','710800'),
('710804','永和区','710800'),
('710805','新庄区','710800'),
('710806','新店区','710800'),
('710807','树林区','710800'),
('710808','莺歌区','710800'),
('710809','三峡区','710800'),
('710810','淡水区','710800'),
('710811','汐止区','710800'),
('710812','瑞芳区','710800'),
('710813','土城区','710800'),
('710814','芦洲区','710800'),
('710815','五股区','710800'),
('710816','泰山区','710800'),
('710817','林口区','710800'),
('710818','深坑区','710800'),
('710819','石碇区','710800'),
('710820','坪林区','710800'),
('710821','三芝区','710800'),
('710822','石门区','710800'),
('710823','八里区','710800'),
('710824','平溪区','710800'),
('710825','双溪区','710800'),
('710826','贡寮区','710800'),
('710827','金山区','710800'),
('710828','万里区','710800'),
('710829','乌来区','710800'),
('712200','宜兰县','710000'),
('712201','宜兰市','712200'),
('712221','罗东镇','712200'),
('712222','苏澳镇','712200'),
('712223','头城镇','712200'),
('712224','礁溪乡','712200'),
('712225','壮围乡','712200'),
('712226','员山乡','712200'),
('712227','冬山乡','712200'),
('712228','五结乡','712200'),
('712229','三星乡','712200'),
('712230','大同乡','712200'),
('712231','南澳乡','712200'),
('712300','桃园县','710000'),
('712301','桃园市','712300'),
('712302','中坜市','712300'),
('712303','平镇市','712300'),
('712304','八德市','712300'),
('712305','杨梅市','712300'),
('712306','芦竹市','712300'),
('712321','大溪镇','712300'),
('712324','大园乡','712300'),
('712325','龟山乡','712300'),
('712327','龙潭乡','712300'),
('712329','新屋乡','712300'),
('712330','观音乡','712300'),
('712331','复兴乡','712300'),
('712400','新竹县','710000'),
('712401','竹北市','712400'),
('712421','竹东镇','712400'),
('712422','新埔镇','712400'),
('712423','关西镇','712400'),
('712424','湖口乡','712400'),
('712425','新丰乡','712400'),
('712426','芎林乡','712400'),
('712427','横山乡','712400'),
('712428','北埔乡','712400'),
('712429','宝山乡','712400'),
('712430','峨眉乡','712400'),
('712431','尖石乡','712400'),
('712432','五峰乡','712400'),
('712500','苗栗县','710000'),
('712501','苗栗市','712500'),
('712521','苑里镇','712500'),
('712522','通霄镇','712500'),
('712523','竹南镇','712500'),
('712524','头份镇','712500'),
('712525','后龙镇','712500'),
('712526','卓兰镇','712500'),
('712527','大湖乡','712500'),
('712528','公馆乡','712500'),
('712529','铜锣乡','712500'),
('712530','南庄乡','712500'),
('712531','头屋乡','712500'),
('712532','三义乡','712500'),
('712533','西湖乡','712500'),
('712534','造桥乡','712500'),
('712535','三湾乡','712500'),
('712536','狮潭乡','712500'),
('712537','泰安乡','712500'),
('712700','彰化县','710000'),
('712701','彰化市','712700'),
('712721','鹿港镇','712700'),
('712722','和美镇','712700'),
('712723','线西乡','712700'),
('712724','伸港乡','712700'),
('712725','福兴乡','712700'),
('712726','秀水乡','712700'),
('712727','花坛乡','712700'),
('712728','芬园乡','712700'),
('712729','员林镇','712700'),
('712730','溪湖镇','712700'),
('712731','田中镇','712700'),
('712732','大村乡','712700'),
('712733','埔盐乡','712700'),
('712734','埔心乡','712700'),
('712735','永靖乡','712700'),
('712736','社头乡','712700'),
('712737','二水乡','712700'),
('712738','北斗镇','712700'),
('712739','二林镇','712700'),
('712740','田尾乡','712700'),
('712741','埤头乡','712700'),
('712742','芳苑乡','712700'),
('712743','大城乡','712700'),
('712744','竹塘乡','712700'),
('712745','溪州乡','712700'),
('712800','南投县','710000'),
('712801','南投市','712800'),
('712821','埔里镇','712800'),
('712822','草屯镇','712800'),
('712823','竹山镇','712800'),
('712824','集集镇','712800'),
('712825','名间乡','712800'),
('712826','鹿谷乡','712800'),
('712827','中寮乡','712800'),
('712828','鱼池乡','712800'),
('712829','国姓乡','712800'),
('712830','水里乡','712800'),
('712831','信义乡','712800'),
('712832','仁爱乡','712800'),
('712900','云林县','710000'),
('712901','斗六市','712900'),
('712921','斗南镇','712900'),
('712922','虎尾镇','712900'),
('712923','西螺镇','712900'),
('712924','土库镇','712900'),
('712925','北港镇','712900'),
('712926','古坑乡','712900'),
('712927','大埤乡','712900'),
('712928','莿桐乡','712900'),
('712929','林内乡','712900'),
('712930','二仑乡','712900'),
('712931','仑背乡','712900'),
('712932','麦寮乡','712900'),
('712933','东势乡','712900'),
('712934','褒忠乡','712900'),
('712935','台西乡','712900'),
('712936','元长乡','712900'),
('712937','四湖乡','712900'),
('712938','口湖乡','712900'),
('712939','水林乡','712900'),
('713000','嘉义县','710000'),
('713001','太保市','713000'),
('713002','朴子市','713000'),
('713023','布袋镇','713000'),
('713024','大林镇','713000'),
('713025','民雄乡','713000'),
('713026','溪口乡','713000'),
('713027','新港乡','713000'),
('713028','六脚乡','713000'),
('713029','东石乡','713000'),
('713030','义竹乡','713000'),
('713031','鹿草乡','713000'),
('713032','水上乡','713000'),
('713033','中埔乡','713000'),
('713034','竹崎乡','713000'),
('713035','梅山乡','713000'),
('713036','番路乡','713000'),
('713037','大埔乡','713000'),
('713038','阿里山乡','713000'),
('713300','屏东县','710000'),
('713301','屏东市','713300'),
('713321','潮州镇','713300'),
('713322','东港镇','713300'),
('713323','恒春镇','713300'),
('713324','万丹乡','713300'),
('713325','长治乡','713300'),
('713326','麟洛乡','713300'),
('713327','九如乡','713300'),
('713328','里港乡','713300'),
('713329','盐埔乡','713300'),
('713330','高树乡','713300'),
('713331','万峦乡','713300'),
('713332','内埔乡','713300'),
('713333','竹田乡','713300'),
('713334','新埤乡','713300'),
('713335','枋寮乡','713300'),
('713336','新园乡','713300'),
('713337','崁顶乡','713300'),
('713338','林边乡','713300'),
('713339','南州乡','713300'),
('713340','佳冬乡','713300'),
('713341','琉球乡','713300'),
('713342','车城乡','713300'),
('713343','满州乡','713300'),
('713344','枋山乡','713300'),
('713345','三地门乡','713300'),
('713346','雾台乡','713300'),
('713347','玛家乡','713300'),
('713348','泰武乡','713300'),
('713349','来义乡','713300'),
('713350','春日乡','713300'),
('713351','狮子乡','713300'),
('713352','牡丹乡','713300'),
('713400','台东县','710000'),
('713401','台东市','713400'),
('713421','成功镇','713400'),
('713422','关山镇','713400'),
('713423','卑南乡','713400'),
('713424','鹿野乡','713400'),
('713425','池上乡','713400'),
('713426','东河乡','713400'),
('713427','长滨乡','713400'),
('713428','太麻里乡','713400'),
('713429','大武乡','713400'),
('713430','绿岛乡','713400'),
('713431','海端乡','713400'),
('713432','延平乡','713400'),
('713433','金峰乡','713400'),
('713434','达仁乡','713400'),
('713435','兰屿乡','713400'),
('713500','花莲县','710000'),
('713501','花莲市','713500'),
('713521','凤林镇','713500'),
('713522','玉里镇','713500'),
('713523','新城乡','713500'),
('713524','吉安乡','713500'),
('713525','寿丰乡','713500'),
('713526','光复乡','713500'),
('713527','丰滨乡','713500'),
('713528','瑞穗乡','713500'),
('713529','富里乡','713500'),
('713530','秀林乡','713500'),
('713531','万荣乡','713500'),
('713532','卓溪乡','713500'),
('713600','澎湖县','710000'),
('713601','马公市','713600'),
('713621','湖西乡','713600'),
('713622','白沙乡','713600'),
('713623','西屿乡','713600'),
('713624','望安乡','713600'),
('713625','七美乡','713600'),
('713700','金门县','710000'),
('713701','金城镇','713700'),
('713702','金湖镇','713700'),
('713703','金沙镇','713700'),
('713704','金宁乡','713700'),
('713705','烈屿乡','713700'),
('713706','乌丘乡','713700'),
('713800','连江县','710000'),
('713801','南竿乡','713800'),
('713802','北竿乡','713800'),
('713803','莒光乡','713800'),
('713804','东引乡','713800'),
('810000','香港特别行政区','810000'),
('810100','香港岛','810000'),
('810101','中西区','810100'),
('810102','湾仔区','810100'),
('810103','东区','810100'),
('810104','南区','810100'),
('810200','九龙','810000'),
('810201','油尖旺区','810200'),
('810202','深水埗区','810200'),
('810203','九龙城区','810200'),
('810204','黄大仙区','810200'),
('810205','观塘区','810200'),
('810300','新界','810000'),
('810301','荃湾区','810300'),
('810302','屯门区','810300'),
('810303','元朗区','810300'),
('810304','北区','810300'),
('810305','大埔区','810300'),
('810306','西贡区','810300'),
('810307','沙田区','810300'),
('810308','葵青区','810300'),
('810309','离岛区','810300'),
('820000','澳门特别行政区','820000'),
('820100','澳门半岛','820000'),
('820101','花地玛堂区','820100'),
('820102','圣安多尼堂区','820100'),
('820103','大堂区','820100'),
('820104','望德堂区','820100'),
('820105','风顺堂区','820100'),
('820200','氹仔岛','820000'),
('820201','嘉模堂区','820200'),
('820300','路环岛','820000'),
('820301','圣方济各堂区','820300');

/*Table structure for table `admin` */

DROP TABLE IF EXISTS `admin`;

CREATE TABLE `admin` (
  `admin_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `admin_name` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '账户名',
  `admin_nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `admin_password` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '密码',
  `admin_profile_picture_src` varchar(255) DEFAULT NULL COMMENT '头像地址',
  PRIMARY KEY (`admin_id`) USING BTREE,
  UNIQUE KEY `un_admin_name` (`admin_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='管理员表';

/*Data for the table `admin` */

insert  into `admin`(`admin_id`,`admin_name`,`admin_nickname`,`admin_password`,`admin_profile_picture_src`) values 
(1,'admin','admin','1234567','554f0b04-2b32-4597-b780-8e092900cf40.png'),
(2,'demilehan','酒窝','123456','3dafa98f-c13e-4f28-83b9-34f3b579dcd0.jpg'),
(3,'lihao','李浩','123456','3dafa98f-c13e-4f28-83b9-34f3b579dcd0.jpg'),
(4,'a1209577113','如有巧合丶','123456','2bd5d0af-40db-4d2d-a8ec-564e9f4e39b2.jpg');

/*Table structure for table `category` */

DROP TABLE IF EXISTS `category`;

CREATE TABLE `category` (
  `category_id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(20) NOT NULL COMMENT '类别名称',
  `category_image_src` varchar(255) NOT NULL COMMENT '类别图片',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识(1删除 0未删除）',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='类别表';

/*Data for the table `category` */

insert  into `category`(`category_id`,`category_name`,`category_image_src`,`del_flag`) values 
(1,'女装','65d4300d-a44a-4ce6-a344-81fedfcc7bf8.jpg',0),
(2,'男装 /运动户外','ba33100c-bfe6-4ab2-ae1f-9d1c66863122.jpg',0),
(3,'女鞋 /男鞋 /箱包','4dc606ab-adf8-4398-a63c-0f900a08d906.jpg',0),
(4,'美妆 /个人护理','817a9e79-d4d4-401a-95dc-a69a2d597a32.jpg',0),
(5,'腕表 /眼镜 /珠宝饰品','3a7105fa-d756-4d11-97f3-3b1548190c7d.jpg',0),
(6,'手机 /数码 /电脑办公','610c7837-844b-46f9-a70b-7103e5e1ed3d.jpg',0),
(7,'母婴玩具','994f2ffc-b6f3-4bb5-b1ea-bfd885d900e9.jpg',1),
(8,'零食 /茶酒 /进口食品','b04b76b5-af79-48a6-a802-1a91a8327b5c.jpg',0),
(9,'生鲜水果','0b341c64-eaab-4e63-a518-758fa969c36d.jpg',1),
(10,'大家电 /生活电器','d983b737-27b4-4ff7-bb8d-6654e72869e2.jpg',0),
(11,'家居建材','7171f8b1-7fef-4b95-9477-34cd726ecc76.jpg',1),
(12,'汽车 /配件 /用品','27512309-957f-4065-b022-7956075e9de0.jpg',1),
(13,'家纺 /家饰 /鲜花','e9b80ef3-7a74-4ee9-aba1-61f5506f6dd9.jpg',1),
(14,'医药保健','0029c7ee-6c66-4b3a-887b-475ef3a6bdee.jpg',1),
(15,'厨具 /收纳 /宠物','da6f2676-68fc-411c-87d7-490db6b25b31.jpg',1),
(16,'图书音像','4601eb3f-2a7a-45d2-809d-8d0ba2260aed.jpg',1),
(30,'11','edd15747-6ea4-446e-aa81-21c5a31472c3.jpg',1),
(31,'男装','bcc01042-4092-4603-97fb-c814c0710c32.png',1);

/*Table structure for table `product` */

DROP TABLE IF EXISTS `product`;

CREATE TABLE `product` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `product_title` varchar(100) DEFAULT NULL COMMENT '产品标题',
  `product_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `product_sale_price` decimal(10,2) NOT NULL COMMENT '促销价',
  `product_create_date` datetime NOT NULL COMMENT '创建日期',
  `product_category_id` int(11) DEFAULT NULL COMMENT '类别id',
  `product_isEnabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可用',
  PRIMARY KEY (`product_id`) USING BTREE,
  KEY `product_ibfk_1` (`product_category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品表';

/*Data for the table `product` */

insert  into `product`(`product_id`,`product_name`,`product_title`,`product_price`,`product_sale_price`,`product_create_date`,`product_category_id`,`product_isEnabled`) values 
(1,'2018春装新款韩版潮学生宽松薄款卫衣女长袖ins超火的上衣服外套','春装新款百搭~~',119.00,79.00,'2018-04-24 10:36:23',1,0),
(2,'乔琪诺牛仔卫衣女中长款连帽套头中袖韩版宽松2018新款连衣裙夏','乔琪诺原创设计 送运费险',270.00,199.00,'2018-04-24 10:54:59',1,0),
(3,'花花公子春季男士长袖衬衫免烫中青年休闲衬衣潮流男装印花衬衣','送运费险 放心购物',288.00,139.00,'2018-04-24 10:58:08',2,0),
(4,'人本高帮帆布鞋内增高布鞋高邦高腰单鞋牛仔布系带学院风学生款女','帆布鞋',150.00,75.00,'2018-04-24 11:00:25',3,0),
(5,'2018新款ins超火短款连帽卫衣女夏春季bf风薄款帽衫潮学生宽松','潮流卫衣',108.00,69.00,'2018-04-24 11:06:45',1,0),
(6,'花花公子牛仔裤男修身款 秋冬季直筒商务休闲弹力男士牛仔长裤子','花花公子正品 默认圆通 送运费险',259.00,159.00,'2018-04-24 11:08:18',2,0),
(7,'人本韩版女式低帮百搭帆布鞋系带浅口学生白色小白鞋文艺清新单鞋','低帮帆布鞋',169.00,59.00,'2018-04-24 11:15:40',3,0),
(8,'花花公子长袖衬衫男士印花翻领薄款商务休闲衬衣中青年秋款男装潮','潮男焕新 舒适体验 帅气有型 赠运费险',239.00,139.00,'2018-04-24 11:16:57',2,0),
(9,'早春新款韩版宽松长袖卫衣女连帽2018超火ins上衣学生百搭慵懒风','早春新款宽松连帽卫衣',142.00,79.00,'2018-04-24 11:18:19',1,0),
(10,'花花公子春季卫衣男装圆领套头长袖t恤青年男士简约绣花打底衫潮','花花公子正品 默认圆通 送运费险',258.00,139.00,'2018-04-24 11:22:48',2,0),
(11,'手绘单鞋韩版学生高帮女童帆布板鞋休闲男女鞋春秋夜光球鞋女','送运费险 画工细腻 做工精致 穿着舒适',90.00,59.80,'2018-04-24 11:27:43',3,0),
(12,'【明星同款】妖精的口袋ins半袖t恤夏装2018新款圆领纯棉短袖女J=','【宋佳同款】圆领宽松 趣味大片印花',179.00,129.00,'2018-04-24 11:31:17',1,0),
(13,'韩版潮帆布鞋女 平跟学生板鞋 女鞋 高帮运动鞋牛仔休闲秋鞋子','韩版潮帆',139.00,59.00,'2018-04-24 11:34:30',3,0),
(14,'YOVCA手绘情侣鞋平底大码板鞋高帮帆布鞋女鞋男鞋休闲鞋动漫周边','YOVCA手绘',59.00,59.00,'2018-04-24 11:38:17',3,0),
(15,'逸阳女裤2018新款夏季牛仔裤女毛边显瘦弹力铅笔小脚裤九分裤2015','小脚睫毛裤脚 时尚磨白 隐藏美人心机',159.00,159.00,'2018-04-24 11:39:50',1,0),
(16,'花花公子男士短袖衬衫条纹修身商务休闲青年免烫短袖衬衣男寸衫潮','花花公子正品 默认圆通 送运费险',239.00,139.00,'2018-04-24 11:43:28',2,0),
(17,'春季韩版原宿学生帆布鞋女港风板鞋平底运动鞋情侣休闲鞋软妹鞋潮','涂鸦不规则 韩版潮鞋 个性板鞋',228.00,68.00,'2018-04-24 11:44:15',3,0),
(18,'Koradior/珂莱蒂尔品牌女装2018春装新款碎花高腰雪纺v领连衣裙女','碎花短袖 雪纺v领连衣裙',1887.00,1136.00,'2018-04-24 11:47:27',1,0),
(19,'热风2017春新款小白鞋女低帮鞋系带时尚女休闲鞋H14W7110','支持30天退换货 （不影响二次销售情况下）',129.00,99.00,'2018-04-24 11:50:06',3,0),
(20,'七匹狼短袖衬衫 2018夏季新款男士中青年纯色商务休闲白衬衣男装','纯色百搭款',379.00,199.00,'2018-04-24 11:53:05',2,0),
(21,'2018新款运动服套装女装潮韩版时尚春秋季大码衣服休闲卫衣两件套','关注店铺送10元无门槛 领券下单立减',298.00,139.00,'2018-04-24 11:54:29',1,0),
(22,'CHARLES＆KEITH春夏单鞋女CK1-60900081通勤尖头浅口猫跟小白鞋','优雅猫跟 气质尖头',339.00,329.00,'2018-04-24 11:55:24',3,0),
(23,'运动套装女夏装2018新款潮时尚女装短袖七分裤两件套夏天休闲衣服','不起球 不掉色 30天无理由退换货',198.00,138.00,'2018-04-24 11:59:36',1,0),
(24,'CHARLES＆KEITH平底单鞋女CK1-70900076芭蕾舞绑带穆勒尖头小白鞋','优雅绑带装饰',339.00,329.00,'2018-04-24 12:01:09',3,0),
(25,'菲西娅阔腿裤套装女2018春装新款女装针织衫时尚休闲两件套潮夏季','潮流套装',755.00,528.00,'2018-04-24 12:04:55',1,0),
(26,'男士白衬衫短袖修身韩版潮流帅气2018新款夏季休闲百搭印花衬衣男','收藏有惊喜 赠运费险 长袖短袖随心选',155.00,89.00,'2018-04-24 12:07:01',2,0),
(27,'Nike 耐克官方NIKE FREE METCON 男子训练鞋AH8141','专为体能训练 和力量训练打造',899.00,899.00,'2018-04-24 12:09:12',3,0),
(28,'SELECTED思莱德纯棉简约立领男士长款防风风衣外套BL|417121501','7天退差价 防风按扣 斜拉链门襟',999.00,399.60,'2018-04-24 12:11:56',2,0),
(29,'茵曼旗舰店纯棉长袖衬衫曼茵2018春装新款印花衬衣女上衣女装寸衫','舒适亲肤新疆纯棉 少女水杯印花图案',599.00,169.00,'2018-04-24 12:12:26',1,0),
(30,'Nike 耐克官方 NIKE FLEX ESSENTIAL TR女子训练鞋 924344','轻盈支撑',469.00,469.00,'2018-04-24 12:16:27',3,0),
(31,'针织衫女开衫2018春装新款宽松韩版中长款2017秋装学生毛衣外套潮','简约口袋设计 中长款版型毛衣外套',228.00,118.00,'2018-04-24 12:17:44',1,0),
(32,'衣品天成男士短袖T恤夏季男装半袖衣服潮流韩版修身纯棉圆领体恤','潮流韩版 字母短袖t恤男士',208.00,78.00,'2018-04-24 12:20:19',2,0),
(33,'衣品天成2018夏季新款t恤男士短袖圆领纯色纯棉潮流韩版半袖体恤','圆领半袖 体恤男士',208.00,78.90,'2018-04-24 12:25:03',2,0),
(34,'白色T恤女短袖蕾丝上衣棉立方2018夏新款女装修身韩版圆领雪纺衫','绣花公主袖 甜美荷叶边',125.00,59.00,'2018-04-24 12:25:07',1,0),
(35,'衣品天成2018新款男士T恤潮流夏季印花半截袖青年韩版短袖体恤','胸口个性印花装饰 时髦双色任你选',100.00,79.90,'2018-04-24 12:29:55',2,0),
(36,'润乙一夏装2018新款女雪纺衫上衣超仙甜美碎花V领荷叶chic一字肩','新款女雪纺衫',268.00,139.00,'2018-04-24 12:30:53',1,0),
(37,'衣品天成2018夏季新款男士短袖T恤潮韩版假两件体恤早春打底小衫','胸口个性字母印花 袖口个性撞色 假两件',238.00,79.90,'2018-04-24 12:37:42',2,0),
(38,'短袖女2018新款夏装纯棉T恤女白色韩范宽松学生ulzzang百搭半袖女','纯棉面料 两件可以领优惠卷10元',98.00,35.00,'2018-04-24 12:40:09',1,0),
(39,'GXG男装 2018夏季新品时尚胶印黑色休闲短裤男五分裤#182822204','个性胶印设计 腰间抽绳',429.00,259.00,'2018-04-24 12:46:01',2,0),
(40,'宽松短袖t恤女简约夏季印花ulzzang百搭2018新款黄色学生大码上衣','1件49.9 2件69.9',158.00,69.90,'2018-04-24 12:49:01',1,0),
(41,'小猪佩奇社会人佩琪短袖t恤成人衣服恶搞联名抖音潮牌tee男女半袖','小猪佩奇',198.00,49.00,'2018-04-24 12:54:41',1,0),
(42,'【2018年生肖纪念版】OPPO R11S 全面屏 拍照4G手机 oppor11s','直降200元 6期免息',2799.00,2599.00,'2018-04-28 14:11:44',6,2),
(43,'赵薇酒庄梦陇法国红酒原装进口干红葡萄酒2支装波尔多绝代双骄III','领券立减20元',496.00,288.00,'2018-04-28 14:19:30',8,2),
(44,'Xiaomi/小米 红米5A新品老人机拍照官网4a升级款小巧简约智能手机','4.27-5.1 2+16G直降30元',599.00,569.00,'2018-04-28 14:54:52',6,2),
(45,'【渐变樱粉金热卖中】Huawei/华为 P20 全面屏徕卡双摄正品4G手机','旗舰新品 新一代徕卡双摄 AI摄影大师',4288.00,4088.00,'2018-04-28 15:00:28',6,2),
(46,'360扫地机器人家用全自动吸尘器智能超薄静音拖地机擦地一体机','黑色新品首发 领券直降200 抢豪礼',2499.00,1599.00,'2018-04-28 15:53:35',10,2),
(47,'【领券低至919】Meizu/魅族 魅蓝 Note6 疾速双摄 快充大电池','满1299元领券 立减150元',999.00,999.00,'2018-04-28 16:08:27',6,2),
(48,'【母亲节】雅诗兰黛小棕瓶眼霜 肌透修护眼部精华霜15ml 淡化细纹','小棕瓶眼霜 多效修护 年轻睛采乍现',490.00,490.00,'2018-04-28 16:17:43',4,0),
(49,'娇韵诗隔离 清透防晒乳SPF30PA++++（粉色）30ml透亮','物理防晒 自然透亮 清透无压力',420.00,420.00,'2018-04-28 16:24:55',4,0),
(50,'娇韵诗V脸精华塑颜紧致精华露50ml双支套装 提拉小脸','双支立减250元',990.00,990.00,'2018-04-28 16:30:12',4,0),
(51,'娇韵诗青春赋活分龄精华水200ml 补水保湿定制年轻抗皱滋润修护','活力光泽 肌肤吹弹可破 增强肌肤弹性',360.00,360.00,'2018-04-28 16:36:11',4,0),
(52,'娇韵诗全明星眼霜15ml淡化细纹 提拉紧致舒缓眼肌肤','一步修护多重问题 提拉眼角 改善眼袋',500.00,500.00,'2018-04-28 16:39:27',4,0),
(53,'娇韵诗孕产准妈妈护理套装预防淡化修护身体纹提拉紧致','美肌套装 预防淡化身体纹路',1090.00,1090.00,'2018-04-28 16:42:55',4,0),
(54,'娇韵诗恒润奇肌保湿凝露50ml持久补水缓解干燥面霜','卓效滋养 细腻柔滑',470.00,470.00,'2018-04-28 16:47:13',4,0),
(55,'IPSA茵芙莎三色遮瑕膏 遮盖脸部痘印痘痘斑点雀斑黑眼圈遮瑕盘','全新升级 瑕疵隐形',290.00,290.00,'2018-04-28 16:54:57',4,0),
(56,'IPSA茵芙莎蓝瘦子防晒霜 水润倍护防晒日乳EX 面部隔离防紫外线女','双重倍护',280.00,280.00,'2018-04-28 16:57:30',4,0),
(57,'IPSA茵芙莎洗面奶 舒缓净润洁面乳 温和清洁脆弱修护保湿女男日本','温和洁净 舒缓修护',230.00,230.00,'2018-04-28 17:00:22',4,0),
(58,'IPSA茵芙莎水乳套装UL 流金水自律循环美肌液护肤品套装 林允同款','臻享2件礼',1200.00,1200.00,'2018-04-28 17:05:44',4,0),
(59,'雅诗兰黛大牌洗面奶红石榴泡沫洁面乳125ml清洁保湿','一支双效 透亮肌肤',280.00,280.00,'2018-04-28 17:46:37',4,0),
(60,'【母亲节】雅诗兰黛眼霜 小棕瓶密集修护眼精华 紧致修护淡化细纹','密集修护 创新支撑科技 淡化熊猫眼',590.00,590.00,'2018-04-28 17:52:17',4,0),
(61,'雅诗兰黛面膜 小棕瓶密集修护肌透面膜贴4/8片装套装 补水保湿','内外双膜深修护 肌肤晶莹剔透',1190.00,680.00,'2018-04-28 17:56:18',4,0),
(62,'雅诗兰黛红石榴眼霜 鲜活亮采眼部凝露 保湿提亮 淡化熊猫眼','排浊提亮 淡化熊猫眼',350.00,350.00,'2018-04-28 18:00:00',4,0),
(63,'男士洗发水沐浴露套装古龙香味持久留香去屑止痒洗头膏液洗浴套装','部分地区次日到达',158.00,44.90,'2018-04-28 18:05:39',4,0),
(64,'海飞丝鹿晗洗发水丝质柔滑去屑止痒洗头露膏套装男女士500ml*3','随机抢旅行装',198.50,109.00,'2018-04-28 18:09:46',4,0),
(65,'睿嫣润膏舒盈洗发水250ml女士coco香水洗护合一持久留香韩国进口','润泽秀发 洗护2合1香水洗发水',99.00,59.00,'2018-04-28 18:15:26',4,0),
(66,'乔治卡罗尔男士洗发水沐浴露套装控油去屑持久留香味洗头膏露洗浴','买1送2 收藏送香水 加购送洗面奶',159.00,59.90,'2018-04-28 18:19:02',4,0),
(67,'清扬洗发水露男士活力运动薄荷控油去屑滋养官方正品套装500g*3','专为男士设计 从根源解决头屑烦恼',209.40,109.00,'2018-04-28 18:23:19',4,0),
(68,'Danielwellington丹尼尔惠灵顿DW金属表带女生简约时尚石英腕表','全新系列 金属表带 2年保修 瑞典品牌',1190.00,1190.00,'2018-04-28 18:34:32',5,0),
(69,'DanielWellington丹尼尔惠灵顿DW黑手表织纹带男表黑表盘','瑞典品牌 顺丰包邮 2年保修 进口腕表',1350.00,1350.00,'2018-04-28 18:40:53',5,0),
(70,'【直营】ARMANI/阿玛尼手表女气质名媛时尚优雅镶钻石英表AR1925','欧美时尚 气质经典 优雅潮流',2059.00,1799.00,'2018-04-28 18:46:03',5,0),
(71,'【直营】香港直邮天梭力洛克系列全自动机械男表正品','香港直邮 正品保证',2609.00,2559.00,'2018-04-28 18:51:12',5,0),
(72,'分期购 瑞士天梭Tissot 男表石英表T033.410.11.053.01钢带手表男','全球联保 天猫八年老店 正品保证 顺丰',1750.00,1118.00,'2018-04-28 18:56:16',5,0),
(73,'新款罗西尼手表男潮自动机械男表 正品防水皮带时尚男士表616725','澎湃海蓝 品质非凡 进口机芯 精准可靠',3800.00,1399.00,'2018-04-28 19:01:40',5,0),
(74,'仲尼Johnny同款 天王表男表自动机械表皮带日历休闲腕表GS5956','12期分期免息 评价晒图有礼',1599.00,1599.00,'2018-04-28 19:06:15',5,0),
(75,'海鸥手表男士皮带腕表时尚多功能镂空防水男表自动机械表219.328','七天无理由 飞轮表',3400.00,1480.00,'2018-04-28 19:09:48',5,0),
(76,'G-SHOCK旗舰店官网DW-5600BB卡西欧运动防水男士手表方块gshock','复古运动表',790.00,790.00,'2018-04-28 19:16:19',5,0),
(77,'casio卡西欧G-SHOCK旗舰店官网GA-700运动男士电子手表男表gshock','防震防磁 200米防水 LED照明',990.00,990.00,'2018-04-28 19:21:11',5,0),
(78,'卡西欧CASIO手表男士正品防水精钢带MTP-1374D-7A三眼休闲石英表','商务休闲 下单即送表带一条',598.00,298.00,'2018-04-28 19:25:43',5,0),
(79,'CASIO卡西欧G-SHOCK旗舰店官网DW-5035运动男士手表男表Gshock','5035运动男士 手表男表',1190.00,1190.00,'2018-04-28 19:29:47',5,0),
(80,'SUUNTO颂拓松拓斯巴达极速光电心率运动骑行跑步智能腕表户外手表','光电心率 触摸彩屏',4290.00,4089.00,'2018-04-28 19:36:29',5,0),
(81,'军拓铁腕5心率监测GPS户外功能运动导航智能手表铁人三项马拉松表','权威授时 15天误差小于1秒 双星定位',2999.00,2999.00,'2018-04-28 19:40:47',5,0),
(82,'PANDORA潘多拉 华丽蝴蝶结手链925银手链597242CZ气质时尚简约女','官方直售 正品保证 新品上市',598.00,598.00,'2018-04-28 19:45:55',5,0),
(83,'施华洛世奇DUO恶魔之眼项链时尚气质女短款锁骨链首饰','官方直售 全国专柜联保',990.00,990.00,'2018-04-28 19:53:43',5,0),
(84,'BOLON暴龙2018新款复古蛤蟆镜男金属框太阳镜革新墨镜眼镜BL7021','2018新款 BL7021',800.00,769.00,'2018-04-28 20:01:35',6,0),
(86,'42423委屈','额温枪二二',11.00,11.00,'2024-06-17 12:02:33',2,1);

/*Table structure for table `productimage` */

DROP TABLE IF EXISTS `productimage`;

CREATE TABLE `productimage` (
  `productimage_id` int(11) NOT NULL AUTO_INCREMENT,
  `productimage_type` tinyint(3) unsigned NOT NULL COMMENT '类型(0:概述图片 1:详情图片)',
  `productimage_src` varchar(255) NOT NULL COMMENT '图片地址',
  `productimage_product_id` int(11) NOT NULL COMMENT '产品id',
  PRIMARY KEY (`productimage_id`) USING BTREE,
  KEY `productimage_product_id` (`productimage_product_id`) USING BTREE,
  CONSTRAINT `productimage_ibfk_1` FOREIGN KEY (`productimage_product_id`) REFERENCES `product` (`product_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1037 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品图片表';

/*Data for the table `productimage` */

insert  into `productimage`(`productimage_id`,`productimage_type`,`productimage_src`,`productimage_product_id`) values 
(1,0,'e95d4a9d-ebe9-4f12-975b-2e94e98aa2ef.jpg',1),
(2,0,'643b7e4a-cbaf-444d-afca-ebd4654a775e.jpg',1),
(3,0,'55665514-1521-4092-b6c0-af4425a4b82c.jpg',1),
(4,0,'70fc3539-4b80-49b0-a4d9-85835ad4c044.jpg',1),
(5,0,'8d448648-97d0-4993-9ffc-39cce4bde798.jpg',1),
(6,1,'0ab6b478-c0f6-434d-86f6-3ce0c7c5f6d3.jpg',1),
(7,1,'5d01b06e-08bd-41b3-99e9-f8b4a46eb489.jpg',1),
(8,1,'dbd51c92-3497-4d33-9cd6-ffd7e6cc8a9f.jpg',1),
(9,1,'ce6af876-95b4-4f15-a314-3949411ed3fc.jpg',1),
(10,1,'39ee0ad3-8f20-45aa-a2d8-6c4e168e52b0.jpg',1),
(11,1,'90fd93cb-9d19-4f21-8a40-aace56c57161.jpg',1),
(12,1,'d313974c-f06b-4e9e-aa17-cc443f5a27e8.jpg',1),
(13,1,'42f1b1d7-9193-42c6-8265-803bb0ad8676.jpg',1),
(14,0,'3d84ae79-2fb1-440a-992a-ac1da5197ade.jpg',2),
(15,0,'93e7e2bd-bc63-4d8c-8b8e-05bd0911e6c7.jpg',2),
(16,0,'494081e5-fdca-4af6-bf4d-c6854c0ee02d.jpg',2),
(17,0,'a7669250-a81e-4d35-a299-e594e3ba2ec9.jpg',2),
(18,0,'b40f7925-1565-4265-84c2-1af749904f2e.jpg',2),
(19,1,'10b330cd-1b67-4406-a671-b7c0a27e40e1.png',2),
(20,1,'e237c277-0fed-490e-b6be-bcf7bd43444c.png',2),
(21,1,'e7b51c77-c51b-4de5-bc39-3ab1d186ec9c.jpg',2),
(22,1,'ce262095-b1d6-4998-a563-5408f01c80a1.png',2),
(23,1,'43441bbd-2ef7-4cca-9547-a8fe5c0d9130.png',2),
(24,1,'ec75fb80-c0c1-4bf8-ad9c-5f79a6b6590a.png',2),
(25,0,'3b62eab7-7b4c-46ed-84d3-6802a060c894.jpg',3),
(26,0,'78d16083-7394-43ef-96c8-6c3388b4d4dd.jpg',3),
(27,0,'c992fedf-e463-4d04-b5ca-cf12b68bd9e5.jpg',3),
(28,0,'b5b509d4-dd84-4a52-b571-d93b548db6b1.jpg',3),
(29,0,'d0b2f6fc-6f9b-407d-afe9-bf2a4e7db1b8.jpg',3),
(30,1,'3565cc1d-b112-4e82-8650-d05494ad34d2.jpg',3),
(31,1,'e929b67b-aab2-4205-93d0-8063a995deb6.jpg',3),
(32,1,'6d6981eb-8dc0-4499-8cf9-fbd1aba38c81.jpg',3),
(33,1,'c068b2bf-84a4-44e9-8c64-673f548294e2.jpg',3),
(34,1,'b3245733-5f6b-4b30-81e6-dd3a6ec4b668.jpg',3),
(35,1,'a3025b91-2dea-4b15-8ede-9469df581b54.jpg',3),
(36,1,'3da4621a-50f8-4eb1-a34b-8dd6bb146a3e.jpg',3),
(37,0,'8bf384c1-64fc-40fc-bd8b-7b3f9d8a4682.jpg',4),
(38,0,'39b8a8c7-d640-4c72-9646-cfd09761c2ad.jpg',4),
(39,0,'0da6a251-014b-4ef7-ae6d-5924fa2aca84.jpg',4),
(40,0,'b88076ed-1f80-492e-84d4-3177abde0311.jpg',4),
(41,0,'b49721a3-6dbd-423d-8c98-79871886f6c6.jpg',4),
(42,1,'8e027a2e-4fe7-4d73-9f73-1b687964953e.jpg',4),
(43,1,'e7570e9c-a3fc-47a2-adf1-8e2f8764004d.jpg',4),
(44,1,'2c7c00ea-30bc-43eb-b794-021cb4f651da.jpg',4),
(45,1,'22e58f15-cf34-4e95-8a1e-e9bffb94ebd4.jpg',4),
(46,1,'8bc3a05a-8807-4129-85ff-d54e4cecee4e.jpg',4),
(47,1,'e38fefeb-3dbd-4d61-b310-2ebb75cf42c0.jpg',4),
(48,1,'bb8a2a37-3cad-4365-8f3d-5b9dec502b13.jpg',4),
(49,1,'1f353780-63b4-4a05-9332-680fa7735389.jpg',4),
(50,0,'f053c7f0-40e0-40fd-a05f-70ce99a4c3e6.jpg',5),
(51,0,'d6c22950-ec58-4b77-ac04-b3d5d1b9f7a1.jpg',5),
(52,0,'2914710c-6f89-49ce-a0ae-abfce14ae2a5.jpg',5),
(53,0,'b0fb6d5e-45cd-4f9e-9439-28cefa26febf.jpg',5),
(54,0,'8593c890-8d26-43ba-a740-b4a981b1bc5f.jpg',5),
(55,1,'0ebf7dca-e63c-40a6-8b1b-da60d7e9890d.jpg',5),
(56,1,'3232ee3f-6ac5-42b0-aacc-496160b50ded.jpg',5),
(57,1,'76afcd66-1d05-432b-b0d0-f4eded92abfd.jpg',6),
(58,0,'df9ba1ed-3034-42b3-bbf1-2e2df842cebd.jpg',7),
(59,0,'33be3c70-5b8b-4c35-a68f-257256a9595f.jpg',7),
(60,0,'6479b04b-832e-4e4f-a89d-af92911ecc2d.jpg',7),
(61,0,'9028c936-b290-43be-9676-decb3cc611c7.jpg',7),
(62,0,'6034ace3-88ab-4ede-986d-673c1ea05b13.jpg',7),
(63,1,'bd90d988-ca06-496c-9958-9534b2af0b26.jpg',7),
(64,1,'2c94716f-ee9f-4c49-863e-7c5860e47696.jpg',7),
(65,1,'5b65c994-ed78-4dc2-a155-ca0f2b246569.jpg',7),
(66,1,'46e309d4-bdbb-4399-af24-ddd023740f32.jpg',7),
(67,1,'891440fa-4eec-4851-843a-e87decff7ff8.jpg',7),
(68,1,'624abc69-cdba-46a0-9ee9-3676b9b2c412.jpg',7),
(69,1,'beb0a9de-4bfa-4822-a7cb-f758122b354f.jpg',7),
(70,1,'3503d3c7-5144-4eef-8633-f4b9d612ce3b.jpg',7),
(71,1,'e501bdba-4300-49f3-87bd-7e12d559a561.jpg',8),
(72,1,'6bbdd0b4-d369-4a7d-85a7-5b1698116018.jpg',8),
(73,1,'bf2247b3-934c-4095-8293-d52553fbd00e.jpg',8),
(74,1,'595b90ee-7086-4b8f-9483-beb6593f2b2a.jpg',8),
(75,1,'4863af51-71e0-4797-8050-88cc8f2f95e7.jpg',8),
(76,1,'9f0d8cd6-cd61-4d15-8b9d-9113cd111169.jpg',8),
(77,1,'985cebdb-280e-4a83-86c6-f598228c7eda.jpg',8),
(78,1,'8fafe6a6-8c9e-49ff-9d88-856b38547ff1.jpg',8),
(79,0,'97d150c7-5cd1-4f34-a1d0-bca7b286f88b.jpg',9),
(80,0,'5f22b6b0-619d-4325-bbe7-9579e3a3d367.jpg',9),
(81,0,'5fffa28b-0e48-4864-9c92-e94205ab4b69.jpg',9),
(82,0,'b940a875-6c0f-4088-bd02-8d26ddb307fe.jpg',9),
(83,0,'3fd69cb1-39fb-4d7f-98b9-e924538c744a.jpg',9),
(84,1,'4d8b85c8-fb6b-4c33-997d-c09346d7eb93.jpg',9),
(85,1,'3ee6603e-2543-427d-b1bb-3a28491716fd.jpg',9),
(86,1,'cf9628a4-87a4-4a68-9993-4f09d556c1c6.jpg',9),
(87,1,'45904d26-8189-4a3a-8686-9dc6c06ec930.jpg',9),
(88,1,'6a6fa4dc-8197-400f-8242-8d94280af06c.jpg',9),
(89,1,'16189d6e-15aa-4143-ad7f-8748113a5c6b.jpg',9),
(90,1,'3df3bfa7-e4a4-4e82-b404-a541e51e7529.jpg',10),
(91,1,'968e6c63-fd30-49fb-b612-3ec98224c031.jpg',10),
(92,1,'b26788c4-724b-4e2b-815b-cbfb0a2367b2.jpg',10),
(93,1,'e4a85107-a639-400d-9b81-cfad63825ce6.png',10),
(94,1,'3851377d-8765-4ce6-a112-736b92eac393.jpg',6),
(95,1,'4497b507-7b16-49d3-8f7d-4672a20af2be.jpg',11),
(96,1,'accf159c-2be6-4585-a7c7-fc3e18468927.jpg',11),
(97,1,'3e4c8e00-3a16-497c-b848-17452beeedd8.jpg',11),
(98,1,'f6b25b30-c28f-4dc3-ad2e-c1bc1945f95c.jpg',11),
(99,1,'ec1982af-6d8a-45b9-bf97-1b5946d055a3.jpg',11),
(100,1,'5e8d6afa-6a3b-4679-813f-9e0762e024c4.jpg',11),
(101,1,'49c35ef0-4462-4c96-a4a2-ceff00439e8c.jpg',11),
(102,1,'e1ccda7e-9e7c-4f0b-9f91-0f2bbb7110c9.jpg',11),
(103,0,'c8158a63-077a-4e5e-b131-827da01b500e.jpg',12),
(104,0,'9848d751-7059-4c29-99e0-018409f394d3.jpg',12),
(105,0,'45b2ec48-c6a8-4a94-9d94-0e47bf44bedd.jpg',12),
(106,0,'b6627341-1349-4fa8-90dc-b71230e0a733.jpg',12),
(107,0,'1a76bd3d-32de-4da2-8d88-6f781c579a69.jpg',12),
(108,1,'269d7db3-b1c1-4dc5-8a8c-eebe28359068.jpg',12),
(109,1,'9eac2435-fae5-4f3b-81dc-6422e40ebb87.jpg',12),
(110,1,'18d1ccdf-f1ec-4be5-86b6-22d971027ea9.jpg',12),
(111,1,'5feb5928-c519-4a89-85a6-b05953236726.jpg',12),
(112,0,'8941cca2-d043-4df8-903c-4268f806e935.jpg',13),
(113,0,'6b83e7d9-39f8-4751-8e2a-72559ac43d18.jpg',13),
(114,0,'a68958da-acac-434a-8704-494c5cd2d9aa.jpg',13),
(115,0,'9d7bf113-0f5b-4a30-b4fb-f372f248f885.jpg',13),
(116,0,'2c6b8578-09a6-4d99-a397-d520a5bd8e47.jpg',13),
(117,1,'30111e1b-57b6-42c4-860e-9db829ace29f.jpg',13),
(118,1,'e5887512-6d53-47ce-8f12-c155e96198fa.jpg',13),
(119,1,'89a97b9e-9f17-41a7-97a6-fab83ffd5c99.jpg',13),
(120,1,'39323b3f-54a0-4bc8-a0c9-b5654c23a134.jpg',13),
(121,1,'3cab0080-1ac3-4c2e-9dc4-7a4ae30b7cd0.jpg',13),
(122,1,'44baba1b-7792-47d5-bc1d-dfa2fa55e2d2.jpg',13),
(123,1,'76039ccd-5904-479e-a87f-838017555670.jpg',13),
(124,1,'2d51eacb-4ef8-461d-9d99-34934ff5479d.jpg',13),
(125,1,'2af230d0-0046-4c37-b970-9547656af45c.jpg',14),
(126,1,'7818b9ca-dd98-44e3-acd0-82310b49d8dc.jpg',14),
(127,1,'54edecda-5445-44ae-a657-c9d8e96fbc9a.jpg',14),
(128,1,'b59540b4-af43-49fe-9957-07acdf53e4d8.jpg',14),
(129,1,'8c673de2-12e5-449d-ab4a-4904632f18be.jpg',14),
(130,1,'b98d5c96-6861-41d0-98e7-d1951c9c3b3c.jpg',14),
(131,1,'4fe27980-b732-4cc2-abb3-2ba58db0ca39.jpg',14),
(132,1,'c8d56019-37af-4d57-a0f5-b08a778b3f0d.jpg',14),
(133,0,'b3c66d7c-15d3-489b-a64e-2b703d510cb5.jpg',15),
(134,0,'da8f1b00-f775-4a1d-944a-6998250a8863.jpg',15),
(135,0,'76343fc3-b1f6-46c5-84ef-a86c610836cb.jpg',15),
(136,0,'9d833e80-906b-42df-8dae-eeaf0b0cf2d2.jpg',15),
(137,0,'f57f07e8-6f96-445c-8dfa-429ea8f25093.jpg',15),
(138,1,'e804fba9-7e1d-4430-8da8-75b4d57a9853.jpg',15),
(139,1,'a1fd437e-d592-45d1-b45d-0c3f9617d6d9.jpg',15),
(140,1,'03d0b61d-95ac-4532-95cf-038cff416418.jpg',15),
(141,1,'f15944fc-6385-4351-9611-8edf8d7ead32.jpg',15),
(142,1,'96854edf-9e04-4e95-bb3e-9d7c7e517302.jpg',15),
(143,1,'8b6b3d8b-4466-489e-bfae-e75a02032809.jpg',15),
(144,1,'26a3945e-4066-4137-84c9-1a86421661ef.png',15),
(145,1,'533ba9ce-943b-45ba-a877-9d5fba89a25a.jpg',15),
(146,1,'b2c9cd05-433a-42b6-8f78-ca46e9a10623.jpg',16),
(147,1,'0367bbf0-7915-48d4-b613-e31ae529a503.jpg',16),
(148,1,'68501074-05ef-4e24-b4a8-bbc7f811df15.jpg',16),
(149,1,'8f7d4dfb-c185-42e5-a99b-5a32b2bbefd4.jpg',16),
(150,1,'1ec41e42-6460-4396-9d11-eee8474eff07.jpg',16),
(151,1,'15eac777-eb64-4e1a-a324-72471c440139.jpg',16),
(152,1,'49052196-5409-4e68-b075-2264c96f6943.jpg',16),
(153,1,'8be3ce86-b711-4edc-8f0c-ae3dc68d7441.jpg',16),
(154,0,'9434fb97-93e1-420a-b26a-c3810879db8e.jpg',17),
(155,0,'e07a2a19-7f6c-4fe1-880e-476ad9bff02d.jpg',17),
(156,0,'bf7a04db-d358-4a47-9638-5bd8e5f5ba23.jpg',17),
(157,0,'81d5a76c-30a1-4184-8669-72f327a96402.jpg',17),
(158,0,'748053be-f886-41f7-9044-1112596426ef.jpg',17),
(159,1,'7f145b37-8893-44d9-8d56-c59993ef09a3.jpg',17),
(160,1,'bf736b8a-4111-43ad-8e18-2c9ec2f703e1.jpg',17),
(161,1,'c7eb4667-60f7-41b5-8083-37f850564f5b.jpg',17),
(162,1,'2aa006a9-7892-4b9e-bcbf-ce2196c467c3.jpg',17),
(163,1,'e6ec5c9b-3be2-491c-9cce-9c36e295909c.jpg',17),
(164,1,'19cac32e-2d14-4e7b-8575-4a99e11762e3.jpg',17),
(165,1,'0b0aa9fc-b4ad-46c0-aa60-1fea028e87b9.jpg',17),
(166,1,'4c8e8e24-2a4c-4d6c-bd72-dcc5666ffebb.jpg',17),
(167,0,'f1e55cd1-a044-4c25-a2a5-cbb479cc6857.jpg',18),
(168,0,'d40d7062-12cd-455f-9e4d-e7cdd5bab4b2.jpg',18),
(169,0,'26b813b4-6d08-43db-9b53-d668f7c1992f.jpg',18),
(170,0,'bf038e33-48b3-4896-ba44-30e2e31bde64.jpg',18),
(171,0,'f19b72b6-bebe-4877-835a-b6c42e94e8e4.jpg',18),
(172,1,'8839c5e9-3184-40f5-93e6-ccc9da298394.jpg',18),
(173,1,'9ee0f186-4f3b-4a25-b3c3-b057eb68f1ad.jpg',18),
(174,1,'b74747e7-697c-42ac-8083-f81a8955a55a.jpg',18),
(175,0,'bd8f5ad0-0aba-466a-935c-cce36c307a6c.jpg',19),
(176,0,'dade4311-cf33-4b93-86e1-f34e73bc3c28.jpg',19),
(177,0,'c6651a0f-072e-46d5-9db4-39d750e30e00.jpg',19),
(178,0,'01c48cd8-057d-427c-a185-a115f3274aa5.jpg',19),
(179,0,'9228f588-9e19-419a-9d13-8c7ce414b876.jpg',19),
(180,1,'c934d835-c6e7-47d7-a504-5df207d8f085.jpg',19),
(181,1,'9329d357-9c93-4cbb-8617-cfb2b7f794db.jpg',19),
(182,1,'31234bb1-9fd1-4b96-8691-9053fdabf564.jpg',19),
(183,1,'e83e50fe-b7ba-4c3f-9998-deef3fe456df.jpg',19),
(184,1,'6038bfd4-3ad1-4b41-a210-276f5d1db847.jpg',19),
(185,1,'113c03c3-13a8-46ea-aafc-100b25f939a1.jpg',19),
(186,1,'89d58d65-cc85-4b96-b01a-cc6660334a26.jpg',19),
(187,1,'fbcfce49-2e41-4897-9aa6-2504555a9808.jpg',20),
(188,1,'71c90979-6061-4324-825b-831cb8901f84.jpg',20),
(189,1,'e6593c26-6c88-42c7-b276-a5236e2e77d9.jpg',20),
(190,1,'ce128239-d2a5-41e4-abee-b48a6b4fc738.jpg',20),
(191,1,'00605d75-259d-42e3-afd7-1f2a8fb47276.jpg',20),
(192,1,'2e3ae18a-882e-499e-a074-0bd5801ebf1d.jpg',20),
(193,1,'7acccbbb-fdb2-4fba-b2fe-827109cbae70.jpg',20),
(194,1,'ec28935e-5a87-40cf-92cd-b6bc19bf871b.jpg',20),
(195,0,'10064111-027b-4b72-8ec8-5fde13ef7615.jpg',21),
(196,0,'882ed7b3-cbd7-4a80-af3b-67978a521022.jpg',21),
(197,0,'b54c7f5e-533a-43f3-b4ce-a43c8018216d.jpg',21),
(198,0,'d43e4da1-5912-4adc-9e28-01060e0bc198.jpg',21),
(199,0,'f9fc48a6-0883-4974-804c-285ec9b0fe73.jpg',21),
(200,1,'e09c420b-21a6-4f8d-ba50-690271324ea1.jpg',21),
(201,1,'344b930f-ad2d-48d0-bb4f-72418126e49c.jpg',21),
(202,1,'fe750842-e389-49b0-90f0-487439e85cad.jpg',21),
(203,1,'ce020bd4-dd80-4a2a-8232-dff296a10f78.jpg',21),
(204,1,'5f580854-0dfb-4657-a4f9-e8f90edc8be7.jpg',21),
(205,1,'1d9f88ed-05b2-4495-97eb-38cfec37cc44.jpg',21),
(206,1,'074793c5-7927-42f7-ad61-2b6464abb7b4.jpg',21),
(207,1,'4af244cd-c572-4d8f-a4cb-e5aa7de17f2c.jpg',21),
(208,0,'2c26824a-4510-42fa-b26b-85ce1ae08011.jpg',22),
(209,0,'4004c207-b8fc-4c6c-9427-a06d590237ab.jpg',22),
(210,0,'890ab409-76eb-4368-9b9d-851749dbd258.jpg',22),
(211,0,'c8f0cc0f-7e42-4c27-8814-0fd264ded741.jpg',22),
(212,0,'5741fb83-1f62-47b4-aab6-4f995f99e72c.jpg',22),
(213,1,'ae41c284-5a4a-4675-a869-a9f1901b9985.jpg',22),
(214,1,'3f223836-778b-4731-beb3-8e12c5db96e3.jpg',22),
(215,1,'b0cc4f57-6145-4353-9787-aec19da44d22.jpg',22),
(216,1,'a9743120-21f8-4fb8-80c5-e65a5b825e45.jpg',22),
(217,1,'7378d058-4dce-4985-8471-61e2faa32520.jpg',22),
(218,1,'0d8a1d27-534a-4bf8-bc9c-fdb65c72c041.jpg',22),
(219,1,'78880bfe-fc9a-422d-a0de-f415a132b04f.jpg',22),
(220,1,'ba6fcdae-2754-449e-a99d-a79be43197eb.jpg',22),
(221,0,'69e6aa1e-3ea3-45b2-8664-2db136a8b069.jpg',23),
(222,0,'8b236bb7-19b4-49f4-94b7-af9b091c3caf.jpg',23),
(223,0,'0c5d9b7c-8de7-4778-a961-1c960b8a587f.jpg',23),
(224,0,'9c95c196-7e01-4220-a84e-15b0e0b009f6.jpg',23),
(225,0,'27e3dd15-ec6a-4630-b898-9697b817e418.jpg',23),
(226,1,'bb926a55-93de-4407-873f-a201cb508726.jpg',23),
(227,1,'131e01d6-1df8-4f1b-9799-b84c55be52ea.jpg',23),
(228,1,'fdfd453b-62bb-47b9-aca6-480b10e0a167.jpg',23),
(229,1,'4c8f0f92-72a1-4ecf-bbb0-9232305e8f2d.jpg',23),
(230,1,'98bf2ee6-508e-4b9b-994f-2d40bd73b3a8.jpg',23),
(231,1,'b8963c9d-dfdc-4ac6-a134-796b131c75c5.jpg',23),
(232,1,'b11c3ed3-44a2-4640-a87c-065690cd3fba.jpg',23),
(233,1,'d7c0907a-78d5-4df3-8c19-0d44a5c4c144.jpg',23),
(234,0,'8a3db48c-3eac-408e-89a0-ec2902af0d0c.jpg',24),
(235,0,'9c77f022-ca3d-4814-848a-60a65b4175b8.jpg',24),
(236,0,'f74cfcac-8bc2-4537-b17c-4cc469f5aca7.jpg',24),
(237,0,'86b732f1-8062-4fab-88f0-38a816d5503c.jpg',24),
(238,0,'b13801b1-1297-4232-8c12-9d32941fc63a.jpg',24),
(239,1,'f5677d87-fd54-42f1-be66-69f54da37f88.jpg',24),
(240,1,'2c8a33b2-06f0-4c0d-b700-ce1ae993ef38.jpg',24),
(241,1,'b1fb53b0-b048-44ff-b669-f979345859fb.jpg',24),
(242,1,'64b93389-cb1c-4f94-855e-a845c91539fa.jpg',24),
(243,1,'1017bac9-3dd3-422b-83ec-cb6fe04318e8.jpg',24),
(244,1,'8c8217bf-39e7-4264-aa29-c35dec18f45a.jpg',24),
(245,1,'9e0b5818-be31-41b1-b4d1-3a8ec8f69ba5.jpg',24),
(246,1,'abb36421-2945-4b32-a300-65e7884c58cd.jpg',24),
(247,0,'e696e047-701f-40c9-a7f7-d03229779fe6.jpg',25),
(248,0,'f065c40e-3569-41fd-8a34-0dda2ef558e4.jpg',25),
(249,0,'d540017b-da6e-4bd3-a8c2-6d25cb67a394.jpg',25),
(250,0,'d90b62e5-66be-4196-a599-de9282ecd3cc.jpg',25),
(251,0,'87c944d3-cff9-4de9-bb08-af0769253250.jpg',25),
(252,1,'db8fb22a-dcd5-46aa-9922-edc45d5bf45e.jpg',25),
(253,1,'0d10a06f-4f3d-46e1-a261-8aba5f4bd1c2.jpg',25),
(254,1,'a32ef34d-b32a-4b6f-ab90-0aec452f255e.jpg',25),
(255,1,'be9f63b4-7869-45a0-8f09-e56de2e65d8c.jpg',25),
(256,1,'292b21a7-ca1a-4091-87d0-ad1b54a38035.jpg',25),
(257,1,'9e061654-aaef-4fd0-8b05-71a2a97e0df1.jpg',25),
(258,1,'bc01d31e-2f09-43d7-a04e-3b0d0b230031.jpg',25),
(259,1,'85a9701a-4169-4adf-9570-0bfd25a52673.jpg',25),
(260,1,'51fb3613-a5ad-45ad-bca3-79a8e83b50c1.jpg',26),
(261,1,'050066c0-724a-46b5-b324-4e122ef6dca0.jpg',26),
(262,1,'9a4afb34-4fc0-4d7b-9231-bfad188f5ad8.jpg',26),
(263,1,'5a6c7d2d-76c4-4644-b1d9-d963cf798f70.jpg',26),
(264,1,'f4df6190-c5ae-409b-9c3b-339698acde6d.jpg',26),
(265,1,'2f18ce45-16d3-4bc6-a6e5-fe41f1caef2d.jpg',26),
(266,1,'1bc575e0-1b6a-473b-b5fc-f5aba5033fc9.jpg',26),
(267,1,'ad22eb33-7e92-4c18-881e-e6a0023412f1.jpg',26),
(268,0,'138e4099-9b34-4e4e-aeb4-cf32f2086b08.jpg',27),
(269,0,'16c6e9aa-1e04-488b-8cad-edd3ad593a2d.jpg',27),
(270,0,'e6d755a7-c76b-4872-865d-f9b7f4e3cf96.jpg',27),
(271,0,'ccd8868e-7803-4416-8c02-ddf64ec82266.jpg',27),
(272,0,'431d9afa-2aa9-4b85-9054-2d2632d46880.jpg',27),
(273,1,'bdbbc63f-1648-45fa-aa85-9ed4abb72d16.jpg',27),
(274,1,'8b768419-2753-4bf9-8e17-e66b5dbbbfcd.jpg',27),
(275,1,'84f29ead-6572-4e27-97be-fc63f956041d.jpg',27),
(276,1,'bcdd587f-a624-49cf-a8fb-4142b6fb6245.jpg',27),
(277,1,'6ee63620-31c5-40ea-90b1-cfcc59624e5c.jpg',27),
(278,1,'2f566bfd-9a9c-41f6-9559-28ec596db3e4.jpg',27),
(279,1,'58f51133-1c2c-4f8c-86cd-3d34b20f5caa.jpg',27),
(280,1,'3a0d845e-0054-414a-a5b5-4802cf184ac6.png',27),
(281,1,'474d6a0f-cc4d-4005-95d0-e8a5c20a03a8.jpg',28),
(282,1,'d6b4f5a5-4e34-44ad-a880-271b96ccdeac.jpg',28),
(283,1,'9479e5ea-989c-41b1-9e7f-cbd7aa2f6354.jpg',28),
(284,1,'c5ac4c8c-92d3-483b-928e-895d5773e27d.jpg',28),
(285,1,'d119be04-ff6c-482b-98cd-f9c23a42a455.jpg',28),
(286,1,'1f5d43da-9e7d-413c-bc60-eb148a1ae05c.jpg',28),
(287,1,'8ddef19b-ce0d-4798-86b4-a7e7d2b2baad.jpg',28),
(288,1,'e118b214-6636-49d5-af65-40a899cc5b31.jpg',28),
(289,0,'df384d2b-664c-47a4-b298-8bedcb61a5d6.jpg',29),
(290,0,'27d4c4da-0ceb-4053-a4f2-060585391822.jpg',29),
(291,0,'0da6cd3f-9e18-4f03-89cb-e7786df58ec1.jpg',29),
(292,0,'5a663ab8-50c3-4b51-a11c-247445e94dd0.jpg',29),
(293,0,'7d4e8b39-db6d-45df-ad9a-38805ff75c4b.jpg',29),
(294,1,'fcab4b77-a730-4769-9157-7bf0f2618d36.jpg',29),
(295,1,'6c500865-3ea7-42b6-adfa-757600581868.jpg',29),
(296,1,'b1b5417b-9a52-4c51-8734-7f21ad53f3c4.jpg',29),
(297,1,'58b847d7-c68e-4511-be79-ac7d01d62373.jpg',29),
(298,1,'575c6169-add7-411a-b0f2-ca1472959c52.jpg',29),
(299,1,'df2a5a5d-215b-41e2-aa07-7e26b69a3355.jpg',29),
(300,1,'97f4b959-468f-4989-99e0-41de4068d073.jpg',29),
(301,1,'c044f80f-cc24-40c3-a52b-c0b7eda3bab8.jpg',29),
(302,0,'e43936ed-5149-4451-a7ca-1053d12d821e.jpg',30),
(303,0,'1c048283-7e5e-4ba5-a1d1-76ba67769bc6.jpg',30),
(304,0,'23e190b4-4aca-495e-8559-8a2bc8383f23.jpg',30),
(305,0,'4b71a160-e904-4017-baea-e43b58ad2433.jpg',30),
(306,0,'39a28d9c-4f60-47e2-9c53-5766a0560f67.jpg',30),
(307,1,'cd61668f-6fc5-43b7-8d5b-2258a0c5049e.jpg',30),
(308,1,'2433adb8-a113-4e1f-b72f-e6af1b97f210.jpg',30),
(309,1,'579466aa-a85f-4f08-a8c2-8d992c479d00.jpg',30),
(310,1,'ad84a8a9-875e-4d20-9323-d32903624e6f.jpg',30),
(311,1,'2f465e69-03cf-4729-abf6-a15083c50040.jpg',30),
(312,1,'f09d3d5e-99b8-4c9b-b128-e1e8a51108ed.jpg',30),
(313,1,'26d14600-f1d5-4f77-bf68-a1e570e5dab3.jpg',30),
(314,1,'65a0a5fb-385e-495a-aed5-3199ed2e6a52.jpg',30),
(315,0,'6938846f-ae3c-4265-9d8c-f2da4738dc43.jpg',31),
(316,0,'62586f51-e8bf-4878-bdf9-53660c4dc961.jpg',31),
(317,0,'a992ac0b-a047-46db-968c-79be91c152c3.jpg',31),
(318,0,'0be4c1c5-3aac-4e7d-86c8-366083c4af68.jpg',31),
(319,0,'4aa34e07-bd40-4ca9-b596-50a3f6b9bcac.jpg',31),
(320,1,'3f0cdb3b-f6b9-4a5c-b9ff-604637b4521a.jpg',31),
(321,1,'01bbcf96-e7d0-4100-97be-ce96d43f5c9b.jpg',31),
(322,1,'9324267c-3290-4d5d-8b42-cd9d9868933a.jpg',31),
(323,1,'68c42115-fbfe-4008-8324-07ba2a88d1ae.jpg',31),
(324,1,'e99c0737-0ed9-498a-bbe9-7869d26bbbef.jpg',31),
(325,1,'dd7218e7-864b-4307-b1e5-e2b030219cf5.jpg',31),
(326,1,'a5c4c285-699b-4977-90ec-9590432b130f.jpg',31),
(327,1,'5571405e-7334-4b20-8c60-e5e97c7dbd1f.jpg',31),
(328,1,'a074f250-0108-4360-8b3b-f7ad828896d0.jpg',32),
(329,1,'b3c6d502-43ca-43ba-b382-943762692905.jpg',32),
(330,1,'793d7cd3-2a65-4f4c-aedc-3a2e1de07a2a.jpg',32),
(331,1,'b6b0f6bc-cb09-4d70-88fb-4db12287e237.jpg',32),
(332,1,'741d5230-508a-4949-9f8a-963cffcce8e5.jpg',32),
(333,1,'61654c26-732a-4aeb-8946-43fe26b0d5e0.jpg',32),
(334,1,'4481e88c-873f-4a9c-ab36-1491b320ae49.jpg',32),
(335,1,'3e7fecb4-f3bc-4a8d-893b-065e14677720.jpg',32),
(336,1,'c416e2a3-35c3-4177-8798-ded0a9eb97d9.jpg',33),
(337,1,'085b0acd-d00b-44ec-a9f5-362daf86e36a.jpg',33),
(338,1,'08783f0b-0f76-4dc9-a515-e41254b71d90.jpg',33),
(339,1,'e5791ad0-8477-4711-81c8-4b4b2c2069c6.jpg',33),
(340,1,'5ade9d48-dc2e-40ea-bacb-0f891dd230af.jpg',33),
(341,1,'0eb5569d-cb91-4e38-b55f-8ee9c3da7a44.jpg',33),
(342,1,'7186636a-a64d-4301-b222-1a8c2914edd0.jpg',33),
(343,1,'95cf12e2-ea42-47d7-8da9-a2c31b3bbb04.jpg',33),
(344,0,'be1ac709-2b2f-445b-9271-a0ef5c011fb9.jpg',34),
(345,0,'add6d5d2-8bfd-41f5-b147-cceec1098e97.jpg',34),
(346,0,'7e395708-6a80-4229-8e53-f8dbbe25f9e4.jpg',34),
(347,0,'5fbdfa3b-51d0-47c9-a436-bf3ea0f6eff0.jpg',34),
(348,0,'4a50f540-389f-4252-a188-7d16860cf367.jpg',34),
(349,1,'c651b801-7b58-4280-b078-e3c6c7401281.jpg',34),
(350,1,'7f9ca9ac-a1b2-4729-aff3-c036ed55899e.jpg',34),
(351,1,'bb07de17-78a6-43d7-ad1a-27332ffb17eb.jpg',34),
(352,1,'a65ad7e8-049a-4eee-a128-4692170fe6bf.jpg',34),
(353,1,'0fa542c3-a53b-4255-9292-805a7f9e13b8.jpg',34),
(354,1,'4bb69e1c-b009-4bdd-b255-6bd1750562e9.jpg',35),
(355,1,'0026f1e9-a6d5-43d8-96f9-f27cf848553f.jpg',35),
(356,1,'40e1ce54-81fa-40b0-b48b-7448d173f5e4.jpg',35),
(357,1,'b04ea8ab-58d3-4501-a6ba-73f744bf3ca4.jpg',35),
(358,1,'6ef9a95b-82ad-409f-a31e-79568549a7cd.jpg',35),
(359,1,'3b65fc87-cdbe-4d35-a62b-3c474bb88c5e.jpg',35),
(360,1,'cc0e9be2-de46-46b3-bdf2-db485c79a879.jpg',35),
(361,0,'46d2ce8a-b4ab-4f70-bc95-3f1e9e0c7623.jpg',36),
(362,0,'f01c7d08-b6ed-4c50-8250-98d941125718.jpg',36),
(363,0,'319c0031-2967-410b-9ec7-d9da9d0fc822.jpg',36),
(364,0,'1261b8a4-58b7-4a37-926a-1968d531d2ca.jpg',36),
(365,0,'509da1ea-5788-4875-a88e-b7d80b00a8ef.jpg',36),
(366,1,'27af3a42-6e03-436d-9e59-963597456f74.jpg',36),
(367,1,'f3f192da-e334-420a-9b8e-76b5c91b9cc5.jpg',36),
(368,1,'5cd9702a-a78b-49b1-a4d6-63e8df55a2d2.jpg',36),
(369,1,'02e22807-a090-4f4f-98f0-f53aa4c0f3c3.jpg',36),
(370,1,'8bb2e2be-08a5-429f-b109-ffbea8aad019.jpg',36),
(371,1,'5aba83e7-a741-452d-a760-a6c1e10add4c.jpg',37),
(372,1,'273d8e36-9730-4f1c-9454-48bd25c58449.jpg',37),
(373,1,'a5dd9902-529b-4ebf-8809-e10a718519f8.jpg',37),
(374,1,'86e75c08-7eb2-434e-b59e-28340e69fd9b.jpg',37),
(375,1,'a21fa850-2115-4f50-82c8-afe92ffe409e.jpg',37),
(376,1,'c0609867-4e61-4c6c-8ed9-a93fdc413c99.jpg',37),
(377,1,'65659747-8c08-4b7b-a3f8-06b483beb3d2.jpg',37),
(378,1,'42fdad7e-9d5e-4940-a2a8-4a96a188a9f4.jpg',37),
(379,0,'da1f9f23-346b-4c21-917c-1746932574be.jpg',38),
(380,0,'753b8893-18aa-4409-9b17-48d32f9eac18.jpg',38),
(381,0,'1f0f41ac-81f7-4d16-bcfa-7391c1c27b5b.jpg',38),
(382,0,'5290f809-c206-4dc9-a062-d133be34b2cb.jpg',38),
(383,0,'143903fb-2f5d-467b-a151-e7bf658f2591.jpg',38),
(384,1,'9cda0726-8807-476d-9dfe-9a1ec6149f09.jpg',38),
(385,1,'051f8b27-6c65-4aeb-ba56-09b86f7d2d85.jpg',38),
(386,1,'b05d1825-a83d-4c4b-b777-3c3a9d96cc32.jpg',38),
(387,1,'fa634ff9-b27d-48fc-8489-2c7fcf9cd8c0.jpg',38),
(388,1,'be22a00b-5f8e-4ff5-9dcb-d0a59b465951.jpg',38),
(389,1,'8687d76c-e28c-40e6-8da9-f3fbaa91619b.jpg',38),
(390,1,'252951c9-c46d-4f5f-ae41-16d2dde817f1.jpg',38),
(391,1,'0cc43fc1-4a8a-4f95-bdd8-bd275b806afb.jpg',38),
(392,1,'10778032-bdd6-4696-aac1-eb2ce516d252.jpg',39),
(393,1,'b85bfd39-26ff-47de-96ce-df158c6b00fd.jpg',39),
(394,1,'f0ff9d6d-4d6f-419c-bcc8-7b01a90e10b0.jpg',39),
(395,1,'26b52d9e-59f9-44b4-9607-12e32a30ef81.jpg',39),
(396,1,'5f125745-a68a-4f15-8339-fd3344c6a5ce.jpg',39),
(397,1,'ad38d6f6-1306-4b8d-ae4e-3f735cb74b19.jpg',39),
(398,1,'f1be6623-4013-4a43-b6f7-05c4c78b8134.jpg',39),
(399,1,'da189a0b-fa6b-4f0a-94c6-3ce282bffd24.jpg',39),
(400,0,'a377f184-7c02-42a7-8e2f-a8350362aa7c.jpg',40),
(401,0,'a6bd6f69-7e35-466d-8d6a-e3dadbd0c4d3.jpg',40),
(402,0,'ea4414b2-72c6-4020-8846-07b18c7c88c1.jpg',40),
(403,0,'2e3b85f6-90f9-4832-8168-9a6fef98c907.jpg',40),
(404,0,'ecc44fd6-686c-49fe-983d-214962f84be8.jpg',40),
(405,1,'937025ff-9b7f-4a98-bfee-c8cfa30df97a.jpg',40),
(406,1,'4fafabfb-be4d-47b0-966a-3dc812c75802.jpg',40),
(407,1,'a2979483-017d-4db7-9d2e-014c7657eb6d.jpg',40),
(408,1,'87b4ed29-da8c-4e64-a142-bccd97255425.jpg',40),
(409,1,'cfd16525-29fe-426f-b6a1-fee7475a45db.jpg',40),
(410,0,'ff709d64-4548-4d53-8f68-ad3a376f43c9.jpg',41),
(411,0,'2b0522bc-9762-4161-85a5-0134fe85cb5d.jpg',41),
(412,0,'a6be496a-b2a7-48d1-a213-0130d2f13dd5.jpg',41),
(413,0,'7b4bde55-624b-4586-9957-9ca950659fd6.jpg',41),
(414,0,'5ce42f0b-b459-4f34-b607-3a99d4edb6e6.jpg',41),
(415,1,'1a77d360-c05f-43bd-a6bb-336a19647806.jpg',41),
(416,1,'d6f14994-5aab-4e2d-82dc-df012502baa5.jpg',41),
(417,1,'d0a2f634-1292-45a6-b8fd-06908c2bac12.jpg',41),
(418,1,'268ef503-0baf-4b67-88fc-70ebda0b4dcd.jpg',41),
(419,1,'14db1bbd-d87b-43ad-8107-3c1ee7f49f84.jpg',41),
(508,0,'f076fbe9-2a69-493f-89c9-a2d3303b6879.jpg',6),
(509,0,'0f47c7f4-6815-41cd-b6f3-37cf08542eb0.jpg',6),
(510,0,'10e407f2-0357-4116-926e-fdff44a1b900.jpg',6),
(511,0,'b7745647-3193-4f86-9843-cfd27c1f1a74.jpg',6),
(512,0,'219c867f-1f5f-4db6-91c4-a323b7b4ce8f.jpg',6),
(513,0,'194b7592-dbbc-457f-8d57-07df2c2fa81f.jpg',8),
(514,0,'3f437aa8-68bb-4c86-9558-57d0ab316f3c.jpg',8),
(515,0,'dae7fef0-6394-459e-99bc-6f5235cc3088.jpg',8),
(516,0,'fd8479d3-8999-4447-b20d-79af2f215abe.jpg',8),
(517,0,'9e63240a-f4f5-4354-a9a8-78e25cdea05a.jpg',8),
(518,0,'5c6b0346-b1d5-411c-b4b7-90004f1cbb19.jpg',10),
(519,0,'867e79dd-7f1b-4474-9182-2bf3d4fbf723.jpg',10),
(520,0,'0cb0a7ca-468f-4325-801d-24c737aa468b.jpg',10),
(521,0,'818305f5-499c-4a0a-8f80-c5edc2d86794.jpg',10),
(522,0,'5d3d4b1e-03fd-4286-8f66-2ccc4b972947.jpg',10),
(523,0,'23ff5f7a-bec9-47a2-b476-a0c9d3ba1dc1.jpg',11),
(524,0,'875bfe17-00f8-4876-853d-79639541b0ce.jpg',11),
(525,0,'b4873120-0337-4075-aea1-2b2d25fe2332.jpg',11),
(526,0,'9986250a-bf6e-4657-a7ed-2089d258b187.jpg',11),
(527,0,'75f4298a-df7c-4aeb-98d5-cbcf2e83deab.jpg',11),
(528,0,'a2be53ea-31a0-4535-b09b-76e22645f928.jpg',14),
(529,0,'3b424f0c-76b2-4730-a217-b1bc24df37d2.jpg',14),
(530,0,'c54b5bf9-ddb8-4435-97b0-f2c68921cb06.jpg',14),
(531,0,'f7861741-aaab-4196-83eb-4218b9d08f6e.jpg',14),
(532,0,'406c4ffb-8fb1-4285-8e28-d909f0de0339.jpg',14),
(533,0,'3ae797fb-2c05-4e3d-8f11-00212e02753f.jpg',16),
(534,0,'a22d2dbd-a25f-4213-86ee-9ea0c8c99ada.jpg',16),
(535,0,'43edc9b4-eedc-47bd-a271-e34041c88ab0.jpg',16),
(536,0,'8cc9aa5d-f15a-4bc9-954d-b7d2c20d1380.jpg',16),
(537,0,'73d0f626-5d22-4191-849b-84f195e0673c.jpg',16),
(538,0,'d4e0cc31-c6bc-4aaf-b40a-ce306b444c86.jpg',20),
(539,0,'a8a96261-a668-4625-a634-3716f77e93de.jpg',20),
(540,0,'cd67892a-75b2-427d-b21c-86847c5f11a6.jpg',20),
(541,0,'97d898d5-7005-44f1-a484-ab9339066a26.jpg',20),
(542,0,'5f1bad4a-3c56-48ae-b3c4-950d6d330896.jpg',20),
(543,0,'44ae6bb9-9cfe-4522-859f-ae666414888b.jpg',26),
(544,0,'fb5a1df1-32c6-43f9-aeec-14a77e609bde.jpg',26),
(545,0,'4d10d92e-414c-443e-be7a-67260a740447.jpg',26),
(546,0,'4303f6f4-dfa5-4199-bc66-3a3c7f6fa35f.jpg',26),
(547,0,'ad07c5e7-c9a7-48a9-bca5-ffca588bd449.jpg',26),
(548,0,'84550614-7786-4ce0-bbce-c424fde34e63.jpg',28),
(549,0,'9851ad16-e498-4b0e-bec3-8d08f4eddb8e.jpg',28),
(550,0,'7f77bc82-6972-4a9f-9bbb-8db1eb357e1d.jpg',28),
(551,0,'7dc1b9da-a60e-4286-9b38-09ad4d413940.jpg',28),
(552,0,'ad94469d-fef6-458c-87e2-1ea77ba65ec7.jpg',28),
(553,0,'8de0c092-d398-45bf-b931-cf99819d703e.jpg',32),
(554,0,'a58f4a3e-4805-44dc-8de2-ba41d9445fd6.jpg',32),
(555,0,'6473ee8c-c7bc-493d-8c99-4db146a0dbd6.jpg',32),
(556,0,'566d9cdf-d4ad-47a8-956d-3d316a64b1e0.jpg',32),
(557,0,'c3434483-34d9-4d6b-bfc4-391754060766.jpg',32),
(558,0,'e0832011-052a-422b-a044-01812ae8bc83.jpg',33),
(559,0,'cd744e07-f180-4392-b694-086d03bfbb39.jpg',33),
(560,0,'238283e8-13ac-4bf6-88c3-9af94fc2696b.jpg',33),
(561,0,'278f9e5e-1607-467b-8538-0b515d761db2.jpg',33),
(562,0,'12386868-11ec-4a35-827e-b75a21d41cb7.jpg',33),
(563,0,'38d6be09-2b59-4329-8527-2c6630d23289.jpg',35),
(564,0,'3b9fcc7e-f1c9-44f1-b452-0ae8ce0bc9c3.jpg',35),
(565,0,'ace7ce30-7374-48ad-99b8-ee44c0bad78c.jpg',35),
(566,0,'fbf6f90a-c434-44ed-8344-6df381edf19f.jpg',35),
(567,0,'729311b3-8008-43d0-9132-d9eb89867225.jpg',35),
(568,0,'10768830-b441-4b99-895c-c168e35acc39.jpg',37),
(569,0,'6c53f6a2-3ce3-49d4-b79c-4efc09816652.jpg',37),
(570,0,'9cce5033-f295-4703-accc-e5353f3a034b.jpg',37),
(571,0,'c80c37e8-0c87-4b61-960f-01b7140baf17.jpg',37),
(572,0,'06c38419-570d-485e-85b8-5bc18e8ba28c.jpg',37),
(573,0,'83fd4cef-eefe-40cd-b4d8-8551a1e5b29b.jpg',39),
(574,0,'e75c390c-170c-4b99-aef0-a07824ef1e93.jpg',39),
(575,0,'e02e035d-d08d-49b7-bc0d-c56ae85b6aaf.jpg',39),
(576,0,'c5a7f344-9fce-4595-9189-f2fcdfa22d06.jpg',39),
(577,0,'e9bb89cb-57fb-439c-9eab-af0779bf3b3c.jpg',39),
(578,0,'41f8e353-2be8-4045-9016-40888c0ba368.jpg',48),
(579,0,'156b83f1-7d17-4558-90dd-00ac82dc1e4c.jpg',48),
(580,0,'66747295-c12f-4c81-b1b7-613c39ecd9bd.jpg',48),
(581,0,'bb0e386a-6fa4-4427-9559-e9196841a3da.jpg',48),
(582,0,'639545f3-ac0b-44e0-95a8-179e9e53bd2f.jpg',48),
(583,1,'d8a06c00-6f09-4a0a-81be-603f443659ac.jpg',48),
(584,1,'b716d7bc-287e-437f-b1cb-0869e4a68186.jpg',48),
(585,1,'8354018f-3ac3-4d50-9360-d41949511b73.jpg',48),
(586,1,'0a6f2104-8636-4ca6-8fcd-ca3b0be9c429.jpg',48),
(587,1,'ff6e400d-d662-41cc-b306-5db901495386.jpg',48),
(588,1,'0dd746e5-f0ee-413b-96e5-50673c30903a.jpg',48),
(589,1,'578ef1e7-7e3b-40fb-a8b6-88dec7ff3ef9.jpg',48),
(590,1,'2fe57394-81cd-48ac-b423-d4f0cdb4dc76.jpg',48),
(591,0,'e385d602-668a-4e6d-afa3-b6cffd63e7e9.jpg',49),
(592,0,'00b3b3e7-df22-4d1f-b383-0603ccf717fc.jpg',49),
(593,0,'20199739-3c8b-42a4-b930-c326321c7070.jpg',49),
(594,0,'e074a12e-855c-4905-8ff8-44add66e6d19.jpg',49),
(595,1,'91649831-b127-4573-acf2-52fab1128311.jpg',49),
(596,1,'d9dfbc8f-8e6c-496c-ac3f-1003fa9b1568.jpg',49),
(597,1,'56c5b047-b712-4850-a183-ce7f3106724d.jpg',49),
(598,1,'3b0dc623-f8d5-4fb9-96a7-ebe253fdb2d3.jpg',49),
(599,1,'abe464d4-db12-454a-b7aa-40a722b782bc.jpg',49),
(600,1,'d207a5d8-b567-4953-898e-32d95f414c0b.jpg',49),
(601,1,'038a5b3b-e523-4be5-b4cf-8e4712958dc4.jpg',49),
(602,1,'a8c8c915-5335-4f63-9931-d179edd34ad4.jpg',49),
(603,0,'70e9fc67-c60e-428d-8b40-41f622dff26b.jpg',50),
(604,0,'d1a4f51d-250a-44fb-a94c-f199bdc017d6.jpg',50),
(605,0,'7891e8a9-ef46-45a5-8993-cfff8a66c4a0.jpg',50),
(606,0,'7aa3e502-cfd3-4622-a059-c4da03e7b7c7.jpg',50),
(607,1,'3fc811fd-2635-4bf4-8afa-183b0014ff3d.jpg',50),
(608,1,'a7cc15d6-5279-434c-92e4-6933bd741363.jpg',50),
(609,1,'5ab03562-ff85-48ec-9a8a-529ad6f5cd0b.jpg',50),
(610,1,'a4d2152f-bef9-43bd-aa7a-ab79850dec8e.jpg',50),
(611,1,'4e49fca2-570b-4394-b9ba-78f67511e693.jpg',50),
(612,1,'aa8750f0-66c7-4576-9398-d2af6e3c247e.jpg',50),
(613,1,'cd0aae4a-8ea4-49d5-bb20-13a73857e329.jpg',50),
(614,1,'7e97e0b4-9867-4d36-9bb6-76b6c6ede8bb.jpg',50),
(615,0,'63490732-0045-4238-b464-f14ad13831dc.jpg',51),
(616,0,'97a73049-e607-4521-95d7-feb7d59a1f89.jpg',51),
(617,0,'7cdeb696-7a3d-4af1-9436-d8dd3c4d35c3.jpg',51),
(618,0,'fe096b44-d518-463d-b256-1fe7700b0365.jpg',51),
(619,1,'f08badbc-6f10-483c-8b70-9ba00399191a.jpg',51),
(620,1,'c2b5992c-78a4-48c4-a988-08b212e60557.jpg',51),
(621,1,'13aed4fd-d6fa-43dc-997b-dde3ad7c99d0.jpg',51),
(622,1,'5331f483-8ebc-4f84-8f69-2c2b20989e9d.jpg',51),
(623,1,'0a13212d-e059-4b2c-9bdb-53ea6a9c1713.jpg',51),
(624,1,'44b74190-e09c-42da-bcbc-312a3c7b801a.jpg',51),
(625,1,'13bf6cb4-1f4c-4dc2-82ef-12635d30fb19.jpg',51),
(626,1,'575700a0-9b6a-4272-9bc1-0dea00eb0aae.jpg',51),
(627,0,'40cc1d01-2b34-4e2c-b4f5-9ca171b83e07.jpg',52),
(628,0,'43852208-a66a-47e9-b485-70e49073a803.jpg',52),
(629,0,'0cf7fa49-ed02-4d82-9840-f6b983db0252.jpg',52),
(630,0,'816dffbc-0139-4abf-a965-135d07a8a5b5.jpg',52),
(631,1,'d95baf3f-20b4-4815-88eb-eb53d1b0b0f8.jpg',52),
(632,1,'6cb1ba80-767a-4453-a374-210bc2eaced6.jpg',52),
(633,1,'b39cc25b-4413-48e6-bdd3-b54c747644e3.jpg',52),
(634,1,'51f0524e-32cd-4490-ac50-c8d4abadc924.jpg',52),
(635,1,'8b92495b-7a56-492e-a8f9-c103a1216e0a.jpg',52),
(636,1,'3bf6b8a7-e752-42dd-87e5-10891220a2ae.jpg',52),
(637,1,'6549c236-ec8f-45aa-a72b-ea05856dcea5.jpg',52),
(638,1,'7d08f03a-8486-4a46-9d62-fc70228433cb.jpg',52),
(639,0,'588feb6f-7be3-47ec-9ec8-dd3c663e3a12.jpg',53),
(640,0,'b2ff67b9-58b7-4787-a155-84f92949113c.jpg',53),
(641,0,'dde5556a-edf4-46ee-885b-e14f2308a36f.jpg',53),
(642,0,'167f71de-078d-428e-a1b7-98a7a6fff75c.jpg',53),
(643,0,'1aa3db71-6cee-4d5c-8779-da6f717df090.jpg',53),
(644,1,'bf2ecd8a-48d7-4ae8-a445-e2d142d895e8.jpg',53),
(645,1,'247a241f-8cf3-4278-9aab-2af60d34183f.jpg',53),
(646,1,'eba583a3-9a1e-46ec-9e2c-7f639c44eac1.jpg',53),
(647,1,'f070de8c-8993-4e89-b139-f9224e30bb64.jpg',53),
(648,1,'eaf18dd4-86e5-4a14-8131-f36a54beae37.jpg',53),
(649,1,'49ae5c64-1c7e-42fe-ae8e-8bbb4e5b5a65.jpg',53),
(650,1,'6e6599e0-7a76-48f6-8e1e-c1e784cc23a8.jpg',53),
(651,1,'d0b960e7-d76e-4834-b632-820eac05a11d.jpg',53),
(652,0,'075d74bb-67e1-45d5-a672-c2b226842c56.jpg',54),
(653,0,'3e4cfaa6-4956-4b28-b8e8-50217560eb33.jpg',54),
(654,0,'9baed852-ae19-4a98-a745-171f1ba4ae7b.jpg',54),
(655,0,'b9102294-8d5f-4cc9-b73b-2dbd76dfe5cd.jpg',54),
(656,1,'65e3a9c0-c10a-4b44-baa8-d9699f8a9215.jpg',54),
(657,1,'ecb29b39-49e2-4e22-9b3f-712bd620c746.jpg',54),
(658,1,'cd2a0d6f-6f75-4fb0-beb4-9b5eece577b7.jpg',54),
(659,1,'5ed4286f-7964-4cf6-a7b7-dd1a065ee01f.jpg',54),
(660,0,'46e6a5b5-57eb-463a-bd64-1feb94ecd626.jpg',55),
(661,0,'3230a348-f83c-41ab-afaf-636a34546ccb.jpg',55),
(662,0,'0c3301ba-6af4-473f-a37b-a394935df0ad.jpg',55),
(663,0,'b2566c88-0bd8-400d-b3b3-2a807c70dd32.jpg',55),
(664,0,'2b64313b-3942-44c3-9857-bcc1972e68cd.jpg',55),
(665,1,'f933786e-a5df-476f-839f-aed0deb3846a.jpg',55),
(666,1,'23809975-710d-44bc-b164-40af627881e4.jpg',55),
(667,1,'1493e0be-ccba-4b08-83cc-52e3852e5955.jpg',55),
(668,1,'ac318682-0688-48d5-a1b5-fb0ea26ef3d5.jpg',55),
(669,1,'6a951730-2be8-48cf-9df8-2c2a327f4f25.jpg',55),
(670,1,'78a1d5b1-071b-46bb-84e9-23237566d083.jpg',55),
(671,0,'9e893770-057a-43ef-9cd7-dfe5a652f413.jpg',56),
(672,0,'ce1b4da5-f802-4c83-a5f1-106b77a880cf.jpg',56),
(673,0,'f42f7a3e-ff10-47d3-867c-b23684af6e25.jpg',56),
(674,0,'f1c0932b-9f4c-4bcc-af1c-824b1854c465.jpg',56),
(675,1,'26291bc3-fe54-45f5-b8ec-a9a46f76085d.png',56),
(676,1,'f27307f5-b8d3-4d7d-8be0-3046aa668c63.png',56),
(677,1,'087e08a1-4e92-4018-85a8-3c2104f3fddd.png',56),
(678,1,'1b1c4312-d2c7-44cc-a8f3-feca55a5e3b8.png',56),
(679,1,'0b054aae-d229-467d-af67-d906295bd862.png',56),
(680,1,'d57e20f9-2474-4231-a755-766dda702aad.png',56),
(681,1,'45298afe-9cde-4377-a142-2dcac0a49d9e.png',56),
(682,1,'f035a6d3-495c-4622-9458-d469f223aa3e.png',56),
(683,0,'e9e9492b-9b9c-4067-9a43-b6ef41b13dab.jpg',57),
(684,0,'ce4585f4-80ab-4443-8589-92e4a2fbfe8b.jpg',57),
(685,0,'9725ce4c-92f9-410f-85f8-f8952b0b60f3.jpg',57),
(686,0,'06286fc9-aa03-4827-9406-9d9c104c9cea.jpg',57),
(687,0,'c87afc68-378a-4022-9002-4639ecd719db.jpg',57),
(688,1,'af31e743-fd74-41d5-b55f-555ebe3ad0ea.jpg',57),
(689,1,'99234dfe-9de5-4ff5-892a-c16eeb9c17b3.jpg',57),
(690,1,'5551c0f9-1510-44fd-983d-46c61b8227d1.jpg',57),
(691,0,'62082f83-6217-48c9-a223-18d57f7d6cb3.jpg',58),
(692,0,'1dbf6e58-75d1-43a1-8fc9-3d2564ab0ac2.jpg',58),
(693,0,'4ac9d866-d5a1-4c8c-b0c4-d5c8da8de935.jpg',58),
(694,0,'fb988551-5060-43b7-959d-a38ae64ba944.jpg',58),
(695,0,'cbe30ac0-5f57-4ef0-93fe-987f97e965e5.jpg',58),
(696,1,'ef73bb27-369f-40d6-85be-9c863829bef8.jpg',58),
(697,1,'284ed233-8253-4a9f-b04b-111a54530cb7.jpg',58),
(698,1,'9f781cb0-545b-4338-aea9-481971b564b8.jpg',58),
(699,1,'0f86e616-8dc6-4bd8-abba-4e3bbad5c6f6.jpg',58),
(700,1,'af0466c0-161f-42ac-8747-c1ec4fbade67.jpg',58),
(701,1,'dd05bf12-0de9-4ee5-92a3-341f66b7f434.jpg',58),
(702,1,'6d0c850d-5d6b-44b0-91ee-404a6afb3069.jpg',58),
(703,1,'e8e068a4-92b9-49b7-9777-22ec6fc5c56e.jpg',58),
(704,0,'4f884c97-fcd8-49c1-af51-8b27009be28f.jpg',59),
(705,0,'b71aece9-f816-4636-96b8-2b09f9a08f83.jpg',59),
(706,0,'afd2ff7d-8e33-43a2-a660-3e474667fa20.jpg',59),
(707,0,'47e1e298-a95f-404f-b623-e343f81396d0.jpg',59),
(708,0,'c2d5ee0e-5f47-414d-a8c7-a78ddc578265.jpg',59),
(709,1,'2fbaab9d-5cb0-4cb8-a26d-15f5be5da6de.jpg',59),
(710,1,'72b4d9ea-e8c8-4f46-a3d6-03dbd7a964d8.jpg',59),
(711,1,'ff991b6b-c71e-4887-a4d5-1caa52590920.jpg',59),
(712,1,'0ac9ef0e-5c80-4a2f-a657-a6b0856b2300.jpg',59),
(713,0,'d792426f-b6b9-42c9-9999-1972fb4bfb20.jpg',60),
(714,0,'f9f5c72e-051b-494a-a19e-e4fcd409f450.jpg',60),
(715,0,'74d20ff4-e6d5-4a50-a375-842f7e9a1016.jpg',60),
(716,0,'e8b19191-83c7-4d47-92c9-5dc3af688331.jpg',60),
(717,0,'d928ec2d-bffc-4a71-aa66-13c16d9ac75e.jpg',60),
(718,1,'05d46c77-9727-4193-9a44-1fdc13f03630.jpg',60),
(719,1,'bdc98c26-8f63-4a47-ad7a-ca8b7f00fdbf.jpg',60),
(720,1,'bfcc43ca-eca4-4108-ae05-f461736b80e2.jpg',60),
(721,1,'030120ee-1e05-4a12-8494-e0d928cbdabb.jpg',60),
(722,1,'6e2e6497-40ea-49f5-96f9-f8f8999d3ef3.jpg',60),
(723,1,'908081d3-60b7-4b1d-93b3-626259c80436.jpg',60),
(724,0,'50fde06e-f38d-47e0-8dbb-e2b7c822b842.jpg',61),
(725,0,'ad720e52-8e02-4b40-9f29-dbf8ec72cc13.jpg',61),
(726,0,'3324e208-78ee-46fe-9142-1b9215c6b05e.jpg',61),
(727,0,'211f15ba-0f19-4219-8c9d-d57935902cac.jpg',61),
(728,0,'a8a65f02-817a-4843-bf5e-b79db9b77e3a.jpg',61),
(729,1,'078e4330-f50f-4daa-8ebb-3a523c67a8ed.jpg',61),
(730,1,'56cf3d33-0c7a-4aa2-83c3-4241c93a6b5f.jpg',61),
(731,1,'75131251-f599-45b8-8e4b-f0916e2ea021.jpg',61),
(732,1,'9295e297-2d4f-4836-b114-cee730ad853d.jpg',61),
(733,1,'7ae2157b-d78e-4719-88fe-28900b5a3338.jpg',61),
(734,0,'7888fd12-fa9e-4f11-8e66-3121877ef766.jpg',62),
(735,0,'1bcb9162-2ca2-4966-9241-0bc35402723d.jpg',62),
(736,0,'9bfa0f22-8ac1-41c4-add2-145b7629b6d1.jpg',62),
(737,0,'31a1b8b4-2c7e-4a90-a2a8-e067cc72bd59.jpg',62),
(738,0,'aef81282-a358-4bec-a031-08795cc29c3f.jpg',62),
(739,1,'7b2ad95f-5d84-4eee-9a3b-7f8a686c1bbf.jpg',62),
(740,1,'4e3f55be-302b-47eb-b551-b601277d2f43.jpg',62),
(741,1,'278e1a7c-dc26-4b1a-b100-a6eb9f699da8.jpg',62),
(742,0,'c905e661-6f50-485d-9dfd-99001e821008.jpg',63),
(743,0,'0f223da4-4681-46ca-9aba-060adc32759a.jpg',63),
(744,0,'cd933063-aaf3-48df-8aea-af80d3364733.jpg',63),
(745,0,'ac5dcedc-a8b5-4359-a99e-f6b580546c51.jpg',63),
(746,0,'8d618c10-61c9-4b4a-9536-16979433aec0.jpg',63),
(747,1,'0914deb9-350f-4419-b985-4bb9b5555ef6.jpg',63),
(748,1,'feae19a5-30e7-4ef2-849c-232243608174.jpg',63),
(749,1,'2495772a-d13c-4a17-8b96-dc8a2cfefdcf.jpg',63),
(750,1,'789c77e1-6ec5-4239-9df0-ea9488e6c958.jpg',63),
(751,1,'604b0734-1d3c-4d88-ba40-fc05e3edf2f2.jpg',63),
(752,1,'98d54f11-3d31-4cf4-8030-b1288b928350.jpg',63),
(753,1,'5c2f44d7-54cf-42ec-b7b5-87611459a19d.jpg',63),
(754,1,'a38b857f-5dc6-4099-ab36-0d46118c4a16.jpg',63),
(755,0,'355630e0-e686-4b68-9fc1-e503bcf8dfd1.jpg',64),
(756,0,'5d81794c-38aa-45e8-9133-7bd37bb8ab98.jpg',64),
(757,0,'ce5c9d70-a605-43ce-aa78-8e9dcbc7138e.jpg',64),
(758,0,'129eeb89-eeba-4d40-97db-1513558f7353.jpg',64),
(759,0,'347c749c-15ba-4a36-9891-f15812b71f92.jpg',64),
(760,1,'82b8254e-e456-44b3-a2ff-737611098f0d.jpg',64),
(761,1,'edd36c88-c422-4763-9e99-732dfb71ab6d.jpg',64),
(762,1,'5f25fd23-b7c5-4845-8cab-996dcf5d357f.jpg',64),
(763,1,'7e31e7ad-dd50-4927-8f00-17141069c028.jpg',64),
(764,1,'911df381-d62e-46b0-80b2-f681dc52ba0c.jpg',64),
(765,1,'c300d19e-eb85-4401-b4e9-03b6021952e0.jpg',64),
(766,1,'243d0706-17a5-45c4-869b-97da1e44a34d.jpg',64),
(767,1,'5c1614e8-cbda-4625-a6a8-5c7e6a304aca.jpg',64),
(768,0,'56f85a22-c86c-4d33-8139-b3d691b72a49.jpg',65),
(769,0,'b89737a6-2607-493b-8c14-68c28fc63bcd.jpg',65),
(770,0,'87e89a62-242b-4d8b-83f2-78345bbd9a72.jpg',65),
(771,0,'268f8e79-ba72-4b70-b740-8896cbb2ce42.jpg',65),
(772,0,'925bf80a-8043-4a40-a8fc-dda19bd7fe9b.jpg',65),
(773,1,'aff90b4d-fb13-4980-a754-188affdc25c0.jpg',65),
(774,1,'a0e4ab03-ffb8-4d81-9835-670523163ef3.jpg',65),
(775,1,'f7808753-a39d-4d03-bd7d-89ae08866647.jpg',65),
(776,1,'5187fca1-fa3f-4935-8b1f-137315215764.jpg',65),
(777,1,'16fb3545-db18-40e8-8305-4de4e53549c6.jpg',65),
(778,1,'6f35d289-9cc5-49df-b13b-e43b48b33791.jpg',65),
(779,0,'a9d2f298-c7b9-46e7-ba4d-499c058b1ddc.jpg',66),
(780,0,'1204dc3b-8ff6-45c4-abb3-1efcd813493c.jpg',66),
(781,0,'0f9b5a09-6257-4550-97d9-4940416e0c5e.jpg',66),
(782,0,'7aae8c69-edef-4af6-b97f-742016b9e725.jpg',66),
(783,0,'c3b8d87e-6ce1-4289-a5cf-23d76f650930.jpg',66),
(784,0,'85393363-e3a5-4b11-8164-947a62448e38.jpg',67),
(785,0,'65f441e2-48c1-4027-8e1e-46e8ca13a530.jpg',67),
(786,0,'42a5af82-d7ad-4588-8f08-76e484d60f6b.jpg',67),
(787,0,'45aac8fb-3e94-49b2-bb6d-fb3284c2e40c.jpg',67),
(788,0,'6d3dbf0e-05b5-4589-9018-7230befc55e4.jpg',67),
(789,1,'e031b33e-4806-4a23-952b-14a9d122c11f.jpg',67),
(790,1,'124394ff-c8c5-4bd0-b165-b2448fc06640.jpg',67),
(791,1,'05ad5ae2-87ca-44ac-915c-22f75c7b35a2.jpg',67),
(792,0,'d007bb8d-a671-4bb8-9cb3-f2dbc5b14359.jpg',68),
(793,0,'80387fc3-586a-426c-90f3-f9115e36af1c.jpg',68),
(794,0,'f1c9bb4f-65a5-40c0-ac6a-4320267c5815.jpg',68),
(795,0,'94e4388d-bf7f-499f-8ffa-d738322f6ab2.jpg',68),
(796,0,'43875e6c-7591-4490-a631-60333e8726fd.jpg',68),
(797,1,'60c95441-66cd-4f3f-8dd4-4ea26d8c566f.jpg',68),
(798,1,'6d40d432-f169-4417-9c2e-76a155633bc8.jpg',68),
(799,1,'612fc664-c8a3-42c0-8913-c3b9fa358f6e.jpg',68),
(800,1,'9b7160af-a2b5-404f-abc9-dd7c0c06cce1.jpg',68),
(801,1,'3275e7fa-e3a3-43a1-9854-66d0faf0ede9.jpg',68),
(802,0,'55e5e23e-4c8f-4a6e-9429-a203f543fdb3.jpg',69),
(803,0,'b2b2e666-7226-4352-9693-2134a6e93d9d.jpg',69),
(804,0,'c58622ca-806c-4da1-848a-c7df4b8e0ce4.jpg',69),
(805,0,'737b72fd-0eb2-4ad0-86d8-8a0ef143d2fb.jpg',69),
(806,0,'d7fa190e-eec1-48e6-a44a-73a33f237586.jpg',69),
(807,1,'e136d861-a221-4c15-a245-e6fc61f081aa.jpg',69),
(808,1,'00bbf7a3-c778-4871-b91f-b6f2d67db406.jpg',69),
(809,1,'a1d23b43-5fea-4bf7-8220-4591991108b6.jpg',69),
(810,1,'a16a3549-d6cb-40d3-aa76-8de494f379d3.jpg',69),
(811,1,'23830d89-5b7b-42b3-b18f-ac72023bb63a.jpg',69),
(812,0,'8442d0c7-9cf0-4edd-b760-aeb81763a321.jpg',70),
(813,0,'95f014d5-c10c-4617-b1f6-c5db58e5e3fb.jpg',70),
(814,0,'1bda5c4c-2b7b-4542-af3d-31ab47dfc0e8.jpg',70),
(815,0,'71909ea0-d7d3-45a5-908a-4baedf79e879.jpg',70),
(816,0,'6ebe8dd1-2a86-4147-a425-415b0297b590.jpg',70),
(817,0,'5a79062c-f421-4bdd-871a-59ca6e07c810.jpg',71),
(818,0,'2cae07cb-bb9b-4e75-a4a0-1e6bab2d0ec8.jpg',71),
(819,0,'80cf9a8a-b9cd-487c-94a9-1319247e6b91.jpg',71),
(820,0,'bcc5c6a4-4476-49ee-8dda-05cdb3040feb.jpg',71),
(821,0,'310f39fa-6534-4c5c-9fce-bcae9ebb1dbc.jpg',71),
(822,1,'4d4e8fbe-112c-4464-92b2-b1f0716f3a59.jpg',71),
(823,1,'575262d3-5da8-499d-8dd0-07f8a5946770.jpg',71),
(824,1,'6bc11132-a82d-497b-b414-8e67da770454.jpg',71),
(825,1,'6b61ceac-9bc2-49c7-9b78-47c49ff2d639.jpg',71),
(826,1,'e025898b-ce45-4ca3-8fc7-cee108f6eef9.jpg',71),
(827,1,'1431614b-f504-4d77-9e13-57a12474c3f3.jpg',71),
(828,1,'3c44185c-5f31-4eb8-8f77-f7bfb8a9bd38.jpg',71),
(829,1,'221dda3f-cb27-4439-838f-e294d7448365.jpg',71),
(830,0,'e37f3c63-af8a-4649-9b9a-13c40cf3bb55.jpg',72),
(831,0,'1795cfee-17f7-4819-ac57-9a8391bf97d6.jpg',72),
(832,0,'a7c92a21-07fd-4115-aeda-d92d15a72511.jpg',72),
(833,0,'d976df57-6ddb-478a-aa3b-b966aeed907d.jpg',72),
(834,0,'faa55484-d76a-4785-91df-d262e46b3d8f.jpg',72),
(835,1,'8d18d15b-dfd5-4ec1-85ce-94a1cd4a7cc8.jpg',72),
(836,1,'99a92e20-4298-4e6f-99b0-c5c3d50f9e95.jpg',72),
(837,1,'126db663-e313-48fa-bc02-2fe89e63696d.jpg',72),
(838,1,'e2c7c923-e6d1-457f-ba9b-f2b0f8ac59fc.jpg',72),
(839,1,'be436665-75d9-4296-a827-fd3032cff539.jpg',72),
(840,1,'e354dab1-a3c6-4b04-ae62-a6311927b0ff.jpg',72),
(841,0,'24166bee-b950-4f17-a520-b04baf3d5866.jpg',73),
(842,0,'2d90a056-a136-480f-bf53-1ad8676cdc16.jpg',73),
(843,0,'34b48541-8d1e-4f78-8b6a-9fecb9f3e6bd.jpg',73),
(844,0,'5f9f93c0-fea5-4b6a-b33a-4947a9ebd539.jpg',73),
(845,0,'f179c81b-2944-47bb-8360-9499fb1784ca.jpg',73),
(846,1,'ea1aecba-8e8c-4f5c-a8d2-088ecbb5d5e7.jpg',73),
(847,1,'2a08d5c0-ff05-4599-8fca-52356952b046.jpg',73),
(848,0,'79dc556a-e72c-4faa-b315-7a740c3c3297.jpg',74),
(849,0,'2eab90c5-fb07-4ceb-a14c-03461853c4dc.jpg',74),
(850,0,'13cb37c1-6152-4b5b-b7f7-b7b84ec6d5b7.jpg',74),
(851,0,'9f038513-2ecc-4405-a791-31c2b285dcf8.jpg',74),
(852,0,'c3e508e0-f9b9-4fa2-b7bf-5c745c533178.jpg',74),
(853,1,'7b03324e-abc4-4ea3-bbe7-7c3d143b9479.jpg',74),
(854,1,'56915464-ed59-42fd-a591-e62a56262887.jpg',74),
(855,1,'fd704e75-94a7-4d0b-89ea-69c2affab5bd.jpg',74),
(856,1,'92331a6c-d2c0-4bb6-b814-80e4f9286207.jpg',74),
(857,1,'1c63dbac-eaed-4b67-97e2-2012fe6f2b25.jpg',74),
(858,1,'4f9f7d4a-4132-4b43-bc62-c9c0ef8444d8.jpg',74),
(859,1,'00fc3d59-07cc-4ff1-97d1-848287536b65.jpg',74),
(860,1,'02d597f5-b9f0-4fbf-9897-175a2492bb5b.jpg',74),
(861,0,'7fa8a22d-c479-4818-8287-5ef0b49318cd.jpg',75),
(862,0,'9204995e-1506-4c1f-bf36-a312c31e920f.jpg',75),
(863,0,'859b70e4-2796-45cd-9fc8-a2c69d6bc38f.jpg',75),
(864,0,'e344a142-085d-4869-b765-a69f11688268.jpg',75),
(865,0,'417ffceb-131d-402d-a5dc-d71fa64d3da1.jpg',75),
(866,1,'01646e17-edc9-4c9d-9eac-3c9ea4dd4e60.jpg',75),
(867,0,'f8ec441a-b14c-4020-8fa4-c0b830840b94.jpg',76),
(868,0,'6f6bdef1-0507-4e15-a703-94f53a8b9680.jpg',76),
(869,0,'1ef79ca6-3db3-4383-8810-a88bd3222669.jpg',76),
(870,0,'e77bf380-5680-4fa2-9bb6-590abf206dc6.jpg',76),
(871,0,'f6cc38aa-db05-476c-95ff-ac3e6d72d24b.jpg',76),
(872,1,'1bc588b4-03e4-42f1-9aa6-866e8c0505c5.jpg',76),
(873,1,'e1c386a5-9a9a-4361-9127-54d173ac370c.jpg',76),
(874,1,'37e07fdb-8a58-4028-ab85-dbb69a3d864c.jpg',76),
(875,1,'b83a4011-1e03-484b-a59d-149b68d0b39c.jpg',76),
(876,1,'ef8a490d-8c0f-4016-b16d-30de0ad498fd.jpg',76),
(877,1,'969a38a4-7951-429d-a958-4f6b36976f4f.jpg',76),
(878,1,'7a483de2-859d-4a9b-bca1-60e97b2a3a95.jpg',76),
(879,1,'3fc4f975-4311-4bce-bef1-1c11c600be49.jpg',76),
(880,0,'ffcde472-33a5-4368-a5eb-5eb6a5640019.jpg',77),
(881,0,'6afdaf32-1123-4ee0-9b56-25b8e0272309.jpg',77),
(882,0,'a6d5e722-6965-4a04-acd7-a9b111794fc0.jpg',77),
(883,0,'dcb3b2e1-d658-467f-9042-1a7cd1584afb.jpg',77),
(884,0,'e065b726-006c-4c40-8a74-c70128ddd285.jpg',77),
(885,1,'cf3f01da-c343-4f7a-80c3-e174f883cc86.jpg',77),
(886,1,'112b4554-9fcd-49aa-8f34-db7900e7604a.jpg',77),
(887,1,'7ab929bf-fa18-443e-8a0a-597ce306bb3c.jpg',77),
(888,1,'c306ad7d-488c-44a4-b6d9-c313bef1190f.jpg',77),
(889,1,'cd50f575-abf1-48ea-ba0c-9cd2f49b4db6.jpg',77),
(890,1,'f53c45db-055c-4dc5-a7de-bf350eb7c797.jpg',77),
(891,1,'fd468704-697a-444e-9df4-3de1b3589c2b.jpg',77),
(892,1,'55953b1a-148c-4725-87be-dbb9a1561d39.jpg',77),
(893,0,'cc9e4efb-4fc6-40f5-9fa1-5a9258ff7163.jpg',78),
(894,0,'d6c6f75f-e9ad-411f-9884-9475f9e506f7.jpg',78),
(895,0,'d8d32fe4-8a18-4b65-860b-3b5ec1bd3ccd.jpg',78),
(896,0,'9b769f7d-06b2-420e-804e-c7cc04086db6.jpg',78),
(897,0,'66d40c08-e383-4052-818f-9ee041b98193.jpg',78),
(898,1,'a9fee366-f252-44e0-9082-49627228301c.jpg',78),
(899,1,'be58e763-6528-4570-93a7-e447bd0bf934.jpg',78),
(900,1,'6a5051b4-829d-4480-bbd0-0a5c792ac956.jpg',78),
(901,1,'d6982734-24ad-4dbd-b33b-5997bd79c82d.jpg',78),
(902,1,'e812e966-d3b2-4801-9b16-f16d454ca30c.jpg',78),
(903,1,'5c9ba5c1-4888-4dd7-ba43-dc1efb402945.jpg',78),
(904,1,'f841aea9-b808-4a34-b5a1-e97214503dee.jpg',78),
(905,1,'b63443ff-162b-4cfb-8b37-df192beada5e.jpg',78),
(906,0,'5c85034f-ec06-4357-b0bd-c11a41b91255.jpg',79),
(907,0,'39577f26-766c-419a-904b-2baf77c37aee.jpg',79),
(908,0,'3942cafd-2fab-4158-90b9-909a4eda0f82.jpg',79),
(909,0,'06aa1efc-d128-4814-9bda-d6aacfe77256.jpg',79),
(910,0,'755d19c2-336d-49b9-bce2-7111f052f1d3.jpg',79),
(911,1,'36bd9f0b-6f6b-4fa4-8978-f5bcaaf198ae.jpg',79),
(912,1,'89046552-2890-48b1-9def-e47ea4ba2f07.jpg',79),
(913,1,'ed2f202c-a4d0-4a5c-9dc5-db52f0e667a2.jpg',79),
(914,1,'9a1f21b0-46ce-4e36-aa90-5a2b46c6d8bd.jpg',79),
(915,1,'f79bc904-9916-4ce3-a58e-445dde767752.jpg',79),
(916,1,'dd438f32-e858-42cc-ba8f-7ee665b96b17.jpg',79),
(917,1,'b4f5d248-845e-4759-a240-6b7e5c05393d.jpg',79),
(918,0,'27da2600-879e-4d7a-b3ad-aa51de455332.jpg',80),
(919,0,'ae480577-de3a-4c16-a24b-dc46728a46e1.jpg',80),
(920,0,'785f8795-32bd-4b7b-995d-e286b7bf4885.jpg',80),
(921,0,'7484355e-6204-4ea5-98cf-d6b2afe9f2b5.jpg',80),
(922,0,'4dc21507-95ab-4330-b171-f3572aa6b16a.jpg',80),
(923,1,'242701f1-78e2-49c4-bc89-fce67ab34dcd.jpg',80),
(924,0,'863598e8-0ab3-465e-815f-92066dfe6ec6.jpg',81),
(925,0,'05f8a68b-a014-4a12-8f31-16d04755d776.jpg',81),
(926,0,'637b32c8-a35f-4e45-9ef3-8396f43dc731.jpg',81),
(927,0,'f74a2086-9bf8-4399-b6a7-7679d42c686a.jpg',81),
(928,0,'9c99124d-9ea7-427c-a5fc-4b9465146fb2.jpg',81),
(929,1,'b6abfa6c-935f-4601-a190-585c5384c628.jpg',81),
(930,1,'27c2d147-9e9e-407c-b6cd-2d5e460521e6.jpg',81),
(931,0,'dce287fb-6b9c-4abf-8001-d07e4bb41311.jpg',82),
(932,0,'4c0d9496-5817-414c-b202-a746c15aa450.jpg',82),
(933,0,'0327afca-b260-4454-99d1-220f5da0c303.jpg',82),
(934,0,'e18634cc-3873-41f7-8f39-465d471818be.jpg',82),
(935,1,'5cf9c39c-a5f8-47e0-82b8-70712e8335d6.jpg',82),
(936,0,'0bf4a4d3-18e6-45d7-a0bb-c88452cfdcae.jpg',83),
(937,0,'d97666bb-3e5d-4f72-a763-4a88e8f970ce.jpg',83),
(938,0,'bda6c430-8465-47d4-ad15-68d40e016d55.jpg',83),
(939,0,'c68cc660-e43a-42cb-bce2-373d9d6b2d20.jpg',83),
(940,0,'58b6f7a9-33c2-465e-9ea5-9c70c061287c.jpg',83),
(941,1,'a343f7ec-c69c-4811-a17f-5931532682c8.jpg',83),
(942,1,'8e3c7772-c8c7-4282-8172-bdcb43adf179.jpg',83),
(943,1,'d25cb2a8-34a1-44ee-8c7b-8a8e200c3fb9.jpg',83),
(944,1,'9e8f3191-5bd4-4934-9ec3-e7e0871c991c.jpg',83),
(945,1,'83dd685e-e0b6-4bcf-a910-5565fff7e23d.jpg',83),
(946,1,'00e0976a-68cd-4ac8-b632-c849161d6906.jpg',83),
(947,1,'e5bab5c5-d131-4de5-a36b-73a55ff58a62.jpg',83),
(948,1,'5d2b449a-7a9a-47da-865e-e80523ddd770.jpg',83),
(949,0,'efa5e8b7-6c99-4271-a20f-2437f91bc25a.jpg',84),
(950,0,'5a61beae-13d5-43ee-b539-4af8288fe47d.jpg',84),
(951,0,'d8b52608-1db3-42b7-97c5-d1e1f9449ebe.jpg',84),
(952,0,'eef5cbff-68c4-4df0-95ca-236d9146efdc.jpg',84),
(954,1,'eabb1026-ad0e-4cc4-9858-2b20cbb6ffdd.jpg',84),
(955,1,'6a23521a-5235-4e15-abc4-b3c1e9b7f916.jpg',84),
(956,1,'acdc712f-e814-4c1f-b261-7ab7a0a54d55.jpg',84),
(957,0,'10d6cfe2-23de-4428-9366-68dc0f5c461a.jpg',45),
(958,0,'0c9b32b5-5280-4456-a9f9-c68eb9acf679.jpg',45),
(959,0,'6565c456-2528-4d0f-beb5-0e7d92556486.jpg',45),
(960,0,'dff56527-004b-4f34-8a30-9e382f385307.jpg',45),
(961,0,'bdceb5c9-4168-41fd-8d98-8f97b52261e0.jpg',45),
(962,1,'a9b4ccb1-8489-445f-9212-020a416a81aa.jpg',45),
(963,1,'d201a9b6-ed72-4709-a81d-4b76d0b72cc1.jpg',45),
(964,1,'2fe0f3ec-113a-4a75-a261-b600302a4fcf.jpg',45),
(965,1,'d8993e10-7b2e-40ac-beb3-5d07a06e547f.jpg',45),
(966,1,'6841b33f-bb65-45eb-aeea-98da3c65f158.jpg',45),
(967,1,'5691aeb5-2ba3-4d99-9942-631f06e8dbd2.jpg',45),
(968,1,'56d37647-21fe-49e2-ba10-2aa98240503a.jpg',45),
(969,1,'dff88973-f683-48e2-99bd-01c3d63dacd3.jpg',45),
(970,0,'832fe6a6-84e5-4d6e-95fd-f91c578a20f3.jpg',46),
(971,0,'abbe98a9-e890-4b90-8506-ccc35af9a1ba.jpg',46),
(972,0,'773d27e2-6aac-45a7-81e6-9a8a524c9d41.jpg',46),
(973,0,'26f24077-fbb0-4d87-91a8-718d77934619.jpg',46),
(974,0,'f96e39c6-ca1d-4319-b153-bf58e100fa48.jpg',46),
(975,1,'6da3900f-a10c-4705-bfbd-e0f654cc34ad.jpg',46),
(976,1,'4d660ee3-9c2d-4888-abef-afa91c741561.jpg',46),
(977,1,'f7a46f74-c61c-4cff-b8c4-007d376f9dd3.jpg',46),
(978,1,'f1824d45-6320-443e-8836-dca1d106d485.jpg',46),
(979,1,'f57a66c6-f02c-4a92-991c-86c4d5da95db.jpg',46),
(980,1,'cfae22b3-8379-497f-a6c5-1aaf33fbbf29.jpg',46),
(981,1,'2084d512-f7a4-4afd-bba5-24278fb1529e.jpg',46),
(982,1,'e8b3b20f-81f5-4754-a77f-58894ff59b12.jpg',46),
(983,0,'ef12249e-9698-4b75-9b75-2b6cadeda14c.jpg',44),
(984,0,'5a087f53-6aba-452b-bfd6-d804f1a44147.jpg',44),
(985,0,'08d9f9e1-cea8-44ff-af7d-077d94297d37.jpg',44),
(986,0,'9134b87b-45f5-46e5-83ce-2c9e58dfa8ef.jpg',44),
(987,0,'247ca6ef-4547-4e80-b0be-efb8635f6b26.jpg',44),
(988,1,'6cc79227-ad56-4b88-bc42-929763ce8ff3.jpg',44),
(989,1,'54df20bc-9c57-4075-b0be-b0f26939a0e7.jpg',44),
(990,1,'3e8a7abc-5fa2-48f6-9554-91709901e536.jpg',44),
(991,1,'36a34b23-e8db-4d42-a565-4791e1e8a040.png',44),
(992,0,'e960c83a-9922-460c-9c2b-4e4dadfed8e0.jpg',47),
(993,0,'da10a899-af39-4903-b16a-231b34982da1.jpg',47),
(994,0,'9fa9b755-7fd5-49cc-9bcd-d3dc4493543d.jpg',47),
(995,0,'85303c13-d52d-4077-b062-523ecd402ba4.jpg',47),
(996,0,'57a51462-ccae-475b-a3dc-9552692d8f69.jpg',47),
(997,1,'e10dfcf7-de3b-4121-9ca3-f44b7b4d1308.jpg',47),
(998,1,'1bb5557f-73da-43b5-9805-7a90dc9c7b0b.jpg',47),
(999,1,'eaee6551-fb01-40c9-8185-f5ec4c4f0a83.jpg',47),
(1000,1,'5467c1a3-7e79-4981-8b08-124aa1cd01f5.jpg',47),
(1001,1,'1bc1fffc-acbc-4652-bda0-d325c0c756c1.jpg',47),
(1002,1,'e00d81a7-5a70-4d32-9943-e4223620aa96.jpg',47),
(1003,1,'44255946-90ad-4301-8ae6-62489aafc5c5.jpg',47),
(1004,1,'251ebb13-fb39-491e-b79e-cc1d008c177f.jpg',47),
(1005,0,'63186053-ff66-4ec2-842c-a527ec525a38.jpg',43),
(1006,0,'5f34c5f3-427e-4cef-ae42-3d6723af3ade.jpg',43),
(1007,0,'aedba2bf-9c70-46aa-bcff-2691884a4444.jpg',43),
(1008,0,'14788811-2260-4e52-8b0e-a4cf403f37cb.jpg',43),
(1009,0,'1d19a71b-79b4-468d-9888-380bcae42a40.jpg',43),
(1010,1,'b9fdd4c7-702a-4f44-9b5a-b297cf186fdc.jpg',43),
(1011,1,'a63bf724-1a71-4118-b787-c765f27de079.jpg',43),
(1012,1,'ba247f36-d657-4306-81f2-fcb0c1291705.jpg',43),
(1013,1,'01f142d9-6d7e-4c1b-8d76-357777a317a0.jpg',43),
(1014,1,'cf5c012f-2e4c-4f48-b699-0fc43b90abba.jpg',43),
(1015,1,'6216fb33-c187-46de-a738-8e30d8ef7035.jpg',43),
(1016,1,'db7b8749-fcf0-44c6-b025-c0fe2e1260fc.jpg',43),
(1017,1,'15bb3165-0538-4bdb-a867-8b8fdeffaea2.jpg',43),
(1018,0,'e20ce6b1-826b-4d70-baee-60fd21e1a79b.jpg',42),
(1019,0,'b83fc26c-7101-4e39-ac5e-5a911b7b32a4.jpg',42),
(1020,0,'154abc33-ef2b-4288-b870-cd845c61fe1f.jpg',42),
(1021,0,'f736ca14-015f-4bde-bea7-738af66bfaa0.jpg',42),
(1022,0,'1567c973-2369-432d-95b9-7ffa20dae729.png',42),
(1023,1,'72c17996-0b07-4fb4-bb18-fc5c964b273e.jpg',42),
(1024,1,'fad1e6f8-83db-4567-b003-875d4f768217.jpg',42),
(1025,1,'230eeee6-f2ec-44a7-91d1-797c9cff9f3a.jpg',42),
(1026,1,'96816c33-3235-4cab-9d88-07f2ff2201d2.jpg',42),
(1027,1,'69849337-9e75-475b-b414-e2184e00747b.jpg',42),
(1028,1,'311da872-d8f7-48de-a2f4-04f649118b8c.jpg',42),
(1029,1,'6ad4f9c4-5dc6-476e-bd91-f66e360d4cbb.jpg',42),
(1030,1,'9e46b5da-8b62-4cb3-aabb-50c566a9e1a5.jpg',42),
(1032,1,'42dda6ba-f26f-4db9-9b8c-88a0fbb4b282.png',84),
(1035,0,'cd46ed43-a110-45f3-9118-e1d1c2ede519.png',86),
(1036,1,'f2475dea-f74d-42fe-a6c1-30adc8eaae83.png',86);

/*Table structure for table `productorder` */

DROP TABLE IF EXISTS `productorder`;

CREATE TABLE `productorder` (
  `productorder_id` int(11) NOT NULL AUTO_INCREMENT,
  `productorder_code` varchar(30) NOT NULL COMMENT '订单号',
  `productorder_address` char(6) NOT NULL COMMENT '产品地址',
  `productorder_detail_address` varchar(255) NOT NULL COMMENT '产品详细地址',
  `productorder_post` char(6) DEFAULT NULL COMMENT '邮政编码',
  `productorder_receiver` varchar(20) NOT NULL COMMENT '收货人',
  `productorder_mobile` char(11) NOT NULL COMMENT '联系方式',
  `productorder_pay_date` datetime NOT NULL COMMENT '支付日期',
  `productorder_delivery_date` datetime DEFAULT NULL COMMENT '发货日期',
  `productorder_confirm_date` datetime DEFAULT NULL COMMENT '确认日期',
  `productorder_status` tinyint(1) NOT NULL COMMENT '订单状态(0:待付款 1:待发货 2:待确认 3:交易成功 4:交易关闭)',
  `productorder_user_id` int(11) NOT NULL COMMENT '用户id',
  PRIMARY KEY (`productorder_id`) USING BTREE,
  UNIQUE KEY `un_productorder_code` (`productorder_code`) USING BTREE,
  KEY `productorder_address` (`productorder_address`) USING BTREE,
  KEY `productorder_ibfk_2` (`productorder_user_id`) USING BTREE,
  CONSTRAINT `productorder_ibfk_1` FOREIGN KEY (`productorder_address`) REFERENCES `address` (`address_areaId`),
  CONSTRAINT `productorder_ibfk_2` FOREIGN KEY (`productorder_user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=230 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品订单表';

/*Data for the table `productorder` */

insert  into `productorder`(`productorder_id`,`productorder_code`,`productorder_address`,`productorder_detail_address`,`productorder_post`,`productorder_receiver`,`productorder_mobile`,`productorder_pay_date`,`productorder_delivery_date`,`productorder_confirm_date`,`productorder_status`,`productorder_user_id`) values 
(1,'2018050123494601','610113','外事学院北校区','710000','江超','15319449084','2018-05-01 23:49:46','2018-05-03 16:19:47','2018-05-13 22:08:08',3,1),
(2,'2018050222445401','610113','外事学院北校区','710000','江超','15319449084','2018-05-03 16:13:14','2018-05-04 18:57:51','2018-05-13 22:48:35',3,1),
(3,'2018050222475401','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:20','2018-05-13 20:49:19','2018-05-13 22:50:11',3,1),
(4,'2018050316235501','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:21','2018-05-13 20:49:19','2018-05-13 22:50:16',3,1),
(5,'2018050316281101','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:22','2018-05-13 20:49:20','2018-05-13 22:50:19',3,1),
(6,'2018050316285701','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:23','2018-05-13 20:49:21','2018-05-13 22:50:25',3,1),
(7,'2018050316471301','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:24','2018-05-13 20:49:24','2018-05-13 22:50:33',3,1),
(8,'2018050316510701','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:26','2018-05-13 20:49:25','2018-05-13 22:50:38',3,1),
(9,'2018050411511501','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:27','2018-05-13 20:49:26','2018-05-13 22:50:41',3,1),
(10,'2018050411543801','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:28','2018-05-13 20:49:27','2018-05-13 22:50:45',3,1),
(11,'2018050411551801','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:53:29','2018-05-13 20:49:28','2018-05-13 22:50:53',3,1),
(12,'2018050412020201','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:39','2018-05-13 20:49:09','2018-05-13 22:49:20',3,1),
(13,'2018050412545901','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:19','2018-05-13 20:49:10','2018-05-13 22:49:24',3,1),
(14,'2018050413270401','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:21','2018-05-13 20:49:10','2018-05-13 22:49:28',3,1),
(15,'2018050419041301','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:22','2018-05-13 20:49:11','2018-05-13 22:49:33',3,1),
(16,'2018050509024801','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:23','2018-05-13 20:49:12','2018-05-13 22:49:38',3,1),
(17,'2018050622090701','610113','外事学院北校区','710000','江超','15319449084','2018-05-06 22:09:07','2018-05-13 20:49:13','2018-05-13 22:49:42',3,1),
(18,'2018051010170501','610113','外事学院北校区','710000','江超','15319449084','2018-05-10 10:17:05','2018-05-13 20:49:13','2018-05-13 22:49:46',3,1),
(19,'2018051010184601','610113','外事学院北校区','710000','江超','15319449084','2018-05-10 10:18:46','2018-05-13 20:49:14','2018-05-13 22:49:50',3,1),
(20,'2018051017470301','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:26','2018-05-13 20:49:15','2018-05-13 22:47:06',3,1),
(21,'2018051116461101','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:28','2018-05-13 20:49:16','2018-05-13 22:49:54',3,1),
(22,'2018051116474501','610113','外事学院北校区','710000','江超','15319449084','2018-05-11 16:47:45','2018-05-13 20:49:16','2018-05-13 22:49:57',3,1),
(23,'2018051116515201','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:24','2018-05-13 20:49:17','2018-05-13 22:50:02',3,1),
(24,'2018051116525001','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 14:52:14','2018-05-13 20:49:18','2018-05-13 22:50:07',3,1),
(25,'2018051316423801','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 16:42:38','2018-05-13 20:49:07','2018-05-13 22:46:52',3,1),
(26,'2018051323012601','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 23:01:28','2018-05-13 23:02:14','2018-05-13 23:02:29',3,1),
(27,'2018051323053501','610113','外事学院北校区','710000','江超','15319449084','2018-05-13 23:05:36','2018-05-13 23:05:42','2018-05-13 23:05:48',3,1),
(28,'2018051517145101','610113','外事学院北校区','710000','江超','15319449084','2018-05-15 17:14:59','2018-05-15 17:15:20','2018-05-15 17:15:50',3,1),
(29,'2018051517170501','610113','外事学院北校区','710000','江超','15319449084','2018-05-15 17:17:08','2018-05-23 15:08:11',NULL,2,1),
(30,'2018051517312401','610113','外事学院北校区','710000','江超','15319449084','2018-05-15 17:31:25','2018-05-15 17:31:29','2018-05-15 17:31:33',3,1),
(31,'2018051517402401','610113','外事学院北校区','710000','江超','15319449084','2018-05-15 17:40:25','2018-05-15 17:40:29','2018-05-15 17:40:32',3,1),
(32,'2018052315042401','120116','XX录','652222','江超','13335398248','2018-05-23 15:04:28','2018-05-23 15:04:48','2018-05-23 15:05:08',3,1),
(33,'2018052315060501','120116','XX录','652222','江超','13335398248','2018-05-23 15:06:09','2018-05-23 15:06:14','2018-05-23 15:06:21',3,1),
(34,'2018052315064001','120116','XX录','652222','江超','13335398248','2018-05-23 15:06:41','2018-05-23 15:07:46',NULL,2,1),
(35,'2018052315171201','120116','XX录','652222','江超','13335398248','2018-05-23 15:17:16','2018-05-23 14:02:02',NULL,2,1),
(36,'2018052315172101','120116','XX录','652222','江超','13335398248','2018-05-23 15:17:22','2018-05-25 00:09:16',NULL,2,1),
(37,'2018052315173001','120116','XX录','652222','江超','13335398248','2018-05-23 15:17:32',NULL,NULL,1,1),
(38,'2018052315181401','120116','XX录','652222','江超','13335398248','2018-05-23 15:18:14',NULL,NULL,0,1),
(39,'2018052315183201','120116','XX录','652222','江超','13335398248','2018-05-23 15:18:32',NULL,NULL,0,1),
(40,'2018052315190301','120116','XX录','652222','江超','13335398248','2018-05-23 15:19:03',NULL,NULL,0,1),
(41,'2018052315190801','120116','XX录','652222','江超','13335398248','2018-05-23 15:19:08',NULL,NULL,4,1),
(42,'2018052315191501','120116','XX录','652222','江超','13335398248','2018-05-23 15:19:15',NULL,NULL,4,1),
(43,'2018052421392801','120116','XX录','652222','江超','13335398248','2018-05-24 21:39:30',NULL,NULL,1,1),
(44,'2018052422272203','120116','XX录','652222','江超','13335398248','2018-05-24 22:27:22',NULL,NULL,1,3),
(45,'2018052423333106','440703','幸福中路','652222','李浩','15815951240','2018-05-24 23:33:32','2018-05-24 23:33:35','2018-05-24 23:33:40',3,6),
(46,'2018052423340706','440703','幸福中路','652222','李浩','15815951240','2018-05-24 23:34:08','2018-05-24 23:34:10','2018-05-24 23:34:13',3,6),
(47,'2018052423484107','110101','北京市','','李浩','17625649845','2018-05-24 23:48:46','2018-05-24 23:48:56','2018-05-24 23:49:07',3,7),
(48,'2018052423494901','440703','幸福中路','652222','李浩','15815951240','2018-05-24 23:49:50','2018-05-24 23:49:53','2018-05-24 23:49:57',3,1),
(49,'2018052423505801','440703','幸福中路','652222','李浩','15815951240','2018-05-24 23:51:05','2018-05-24 23:51:07','2018-05-24 23:51:11',3,1),
(50,'2018052423511307','110101','北京市','','李浩','17625649845','2018-05-24 23:51:14','2018-05-24 23:51:20','2018-05-24 23:51:24',3,7),
(51,'2018052423514907','110101','北京市','','李浩','17625649845','2018-05-24 23:51:50','2018-05-24 23:51:56','2018-05-24 23:52:00',3,7),
(52,'2018052423523006','440703','幸福中路','652222','李浩','15815951240','2018-05-24 23:52:31','2018-05-24 23:52:33','2018-05-24 23:52:37',3,6),
(53,'2018052500022309','510922','四川省','','红豆豆','17625649840','2018-05-25 00:02:27','2018-05-25 00:02:31','2018-05-25 00:02:49',3,9),
(54,'2018052500022908','110101','三里屯36号','332200','柯宇航','15829391102','2018-05-25 00:02:31','2018-05-25 00:02:41','2018-05-25 00:02:53',3,8),
(55,'2018052500035109','510922','四川省','','红豆豆','17625649840','2018-05-25 00:03:53','2018-05-25 00:03:56','2018-05-25 00:04:01',3,9),
(56,'2018052500041406','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:04:15','2018-05-25 00:04:17','2018-05-25 00:04:21',3,6),
(57,'2018052500050506','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:05:06','2018-05-25 00:05:08','2018-05-25 00:05:12',3,6),
(58,'2018052500053008','110105','大望京72号','332200','兰荷','15829391102','2018-05-25 00:05:32','2018-05-25 00:05:54','2018-05-25 00:05:59',3,8),
(59,'2018052500060406','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:06:05','2018-05-25 00:06:08','2018-05-25 00:06:12',3,6),
(60,'2018052500062209','510922','四川省','','红豆豆','17625649840','2018-05-25 00:06:23','2018-05-25 00:06:26','2018-05-25 00:06:32',3,9),
(61,'2018052500065401','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:06:55','2018-05-25 00:06:58','2018-05-25 00:07:02',3,1),
(62,'2018052500070809','510922','四川省','','红豆豆','17625649840','2018-05-25 00:07:09','2018-05-25 00:07:17','2018-05-25 00:07:22',3,9),
(63,'20180525000746010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:07:48','2018-05-25 00:08:03','2018-05-25 00:08:19',3,10),
(64,'2018052500083609','510922','四川省','','红豆豆','17625649840','2018-05-25 00:08:38','2018-05-25 00:08:41','2018-05-25 00:08:45',3,9),
(65,'2018052500083808','360481','向明家园1单元601','332200','柯雨君','17629443058','2018-05-25 00:08:40','2018-05-25 00:08:46','2018-05-25 00:08:58',3,8),
(66,'2018052500094903','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:09:51','2018-05-25 00:09:55','2018-05-25 00:09:58',3,3),
(67,'2018052500105703','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:10:58','2018-05-25 00:11:00','2018-05-25 00:11:03',3,3),
(68,'2018052500114103','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:11:42','2018-05-25 00:11:45','2018-05-25 00:11:49',3,3),
(69,'2018052500115609','510922','四川省','','红豆豆','17625649840','2018-05-25 00:12:00','2018-05-25 00:12:06','2018-05-25 00:12:11',3,9),
(70,'2018052500122303','440703','幸福中路','652222','李浩','15815951240','2018-05-25 00:12:24','2018-05-25 00:12:27','2018-05-25 00:12:31',3,3),
(71,'20180525001240010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:12:41','2018-05-25 00:12:45','2018-05-25 00:12:55',3,10),
(72,'2018052500133508','330102','向明家园1单元601','332200','柯雨君','17629443058','2018-05-25 00:13:39','2018-05-25 00:13:47','2018-05-25 00:13:54',3,8),
(73,'20180525001445010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:14:47','2018-05-25 00:14:51','2018-05-25 00:14:54',3,10),
(74,'20180525001607011','510922','四川省','','红豆豆','17625649840','2018-05-25 00:16:08','2018-05-25 00:16:10','2018-05-25 00:16:14',3,11),
(75,'20180525001814012','230102','天下荣君','115599','王天天','15839440677','2018-05-25 00:18:16','2018-05-25 00:18:21','2018-05-25 00:18:25',3,12),
(76,'20180525001858011','510922','四川省','','红豆豆','17625649840','2018-05-25 00:18:59','2018-05-25 00:19:02','2018-05-25 00:19:07',3,11),
(77,'20180525002142011','510922','四川省','','红豆豆','17625649840','2018-05-25 00:21:44','2018-05-25 00:21:47','2018-05-25 00:21:53',3,11),
(78,'20180525002639010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:26:41','2018-05-25 00:26:48','2018-05-25 00:26:54',3,10),
(79,'20180525002647011','510922','四川省','','红豆豆','17625649840','2018-05-25 00:26:48','2018-05-25 00:26:53','2018-05-25 00:26:59',3,11),
(80,'2018052500272203','610102','核心大街','652222','江超','15815951240','2018-05-25 00:27:23','2018-05-25 00:27:26','2018-05-25 00:27:29',3,3),
(81,'2018052500273508','230102','天下荣君','115599','王火火','15830269100','2018-05-25 00:27:36','2018-05-25 00:27:41','2018-05-25 00:27:46',3,8),
(82,'20180525002954012','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-25 00:29:56','2018-05-25 00:30:01','2018-05-25 00:30:08',3,12),
(83,'20180525003540013','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:35:42','2018-05-25 00:35:50','2018-05-25 00:35:58',3,13),
(84,'2018052500361303','510922','四川省','','将','17625649840','2018-05-25 00:36:14','2018-05-25 00:36:18','2018-05-25 00:36:21',3,3),
(85,'2018052500383507','510922','四川省','','将','17625649840','2018-05-25 00:38:36','2018-05-25 00:38:38','2018-05-25 00:38:51',3,7),
(86,'2018052500395807','510922','四川省','','将','17625649840','2018-05-25 00:39:59','2018-05-25 00:40:02','2018-05-25 00:40:07',3,7),
(87,'20180525004017010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 00:40:19','2018-05-25 00:40:23','2018-05-25 00:40:27',3,10),
(88,'2018052500401907','510922','四川省','','将','17625649840','2018-05-25 00:40:20','2018-05-25 00:40:24','2018-05-25 00:40:31',3,7),
(89,'2018052500404107','510922','四川省','','将','17625649840','2018-05-25 00:40:42',NULL,NULL,1,7),
(90,'2018052500405307','510922','四川省','','将','17625649840','2018-05-25 00:40:54','2018-05-25 00:41:19',NULL,2,7),
(91,'2018052500411307','510922','四川省','','将','17625649840','2018-05-25 00:41:14',NULL,NULL,1,7),
(92,'2018051800415607','510922','四川省','','将','17625649840','2018-05-18 00:41:58',NULL,NULL,1,7),
(93,'2018051800420307','510922','四川省','','将','17625649840','2018-05-18 00:42:04','2018-05-18 00:42:07',NULL,2,7),
(94,'2018051800421607','510922','四川省','','将','17625649840','2018-05-18 00:42:17',NULL,NULL,1,7),
(95,'2018051800423307','510922','四川省','','将','17625649840','2018-05-18 00:42:34','2018-05-18 00:42:38',NULL,2,7),
(96,'2018051800424607','510922','四川省','','将','17625649840','2018-05-18 00:42:46',NULL,NULL,0,7),
(97,'20180518004257010','610113','怕踢校区','332026','兰总','17868643185','2018-05-18 00:42:59','2018-05-18 00:43:05','2018-05-18 00:43:12',3,10),
(98,'2018051800431607','510922','四川省','','将','17625649840','2018-05-18 00:43:17',NULL,NULL,1,7),
(99,'2018051800433207','510922','四川省','','将','17625649840','2018-05-18 00:43:33',NULL,NULL,1,7),
(100,'20180518004422012','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-18 00:44:23','2018-05-18 00:44:27','2018-05-18 00:44:32',3,12),
(101,'2018051800443107','510922','四川省','','将','17625649840','2018-05-18 00:44:35','2018-05-18 00:44:39',NULL,2,7),
(102,'2018051800445807','510922','四川省','','将','17625649840','2018-05-18 00:44:58',NULL,NULL,0,7),
(103,'20180518004500010','610113','怕踢校区','332026','兰总','17868643185','2018-05-18 00:45:01','2018-05-18 00:45:05','2018-05-18 00:45:10',3,10),
(104,'2018051800450301','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:03',NULL,NULL,4,1),
(105,'2018051800450407','510922','四川省','','将','17625649840','2018-05-18 00:45:04',NULL,NULL,0,7),
(106,'2018051800450801','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:08',NULL,NULL,0,1),
(107,'20180518004516012','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-18 00:45:16',NULL,NULL,4,12),
(108,'2018051800451707','510922','四川省','','将','17625649840','2018-05-18 00:45:17',NULL,NULL,0,7),
(109,'2018051800451801','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:18',NULL,NULL,0,1),
(110,'2018051800452301','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:24',NULL,NULL,1,1),
(111,'2018051800452307','510922','四川省','','将','17625649840','2018-05-18 00:45:23',NULL,NULL,0,7),
(112,'2018051800452801','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:29',NULL,NULL,1,1),
(113,'2018051800453207','510922','四川省','','将','17625649840','2018-05-18 00:45:32',NULL,NULL,0,7),
(114,'2018051800453301','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:34',NULL,NULL,1,1),
(115,'2018051800454001','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:40','2018-05-18 00:45:42','2018-05-18 00:45:45',3,1),
(116,'2018051800454207','510922','四川省','','将','17625649840','2018-05-18 00:45:42',NULL,NULL,0,7),
(117,'20180518004546012','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-18 00:45:47','2018-05-18 00:45:57','2018-05-18 00:46:02',3,12),
(118,'2018051800455201','610102','核心大街','652222','江超','15815951240','2018-05-18 00:45:53',NULL,NULL,1,1),
(119,'20180518004707010','610113','怕踢校区','332026','兰总','17868643185','2018-05-18 00:47:10','2018-05-18 00:47:14',NULL,2,10),
(120,'2018051800471007','510922','四川省','','将','17625649840','2018-05-18 00:47:10',NULL,NULL,0,7),
(121,'2018051800473607','510922','四川省','','将','17625649840','2018-05-18 00:47:36',NULL,NULL,0,7),
(122,'20180518004746014','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-18 00:47:48','2018-05-19 00:50:10','2018-05-19 00:50:16',3,14),
(123,'2018051900485503','510922','四川省','','将','17625649840','2018-05-19 00:48:58','2018-05-19 00:49:02','2018-05-19 00:49:06',3,3),
(124,'20180519004855014','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-19 00:48:57','2018-05-19 00:49:02','2018-05-19 00:49:07',3,14),
(125,'2018051900491403','510922','四川省','','将','17625649840','2018-05-19 00:49:16','2018-05-19 00:49:20',NULL,2,3),
(126,'20180519004916010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:49:18','2018-05-19 00:49:20','2018-05-19 00:49:27',3,10),
(127,'20180519004942010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:49:43','2018-05-19 00:49:47','2018-05-19 00:49:50',3,10),
(128,'20180519005120014','150303','维奇基尔6号','115599','王火火','17602951130','2018-05-19 00:51:22','2018-05-19 00:51:25','2018-05-19 00:51:29',3,14),
(129,'20180519005337010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:53:38','2018-05-19 00:53:41','2018-05-19 00:53:45',3,10),
(130,'20180519005407014','150303','维奇基尔6号','115599','其二','17602951130','2018-05-19 00:54:08','2018-05-19 00:54:13','2018-05-19 00:54:18',3,14),
(131,'20180519005414010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:54:16','2018-05-19 00:54:20','2018-05-19 00:54:25',3,10),
(132,'20180519005445010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:54:47','2018-05-19 00:54:50','2018-05-19 00:54:53',3,10),
(133,'20180519005526010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:55:27','2018-05-19 00:55:31','2018-05-19 00:55:36',3,10),
(134,'20180519005656010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 00:56:57','2018-05-19 00:57:00','2018-05-19 00:57:03',3,10),
(135,'20180520010205010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:02:07','2018-05-20 01:02:10','2018-05-20 01:02:14',3,10),
(136,'20180519010322010','610113','怕踢校区','332026','兰总','17868643185','2018-05-19 01:03:23','2018-05-19 01:03:28','2018-05-19 01:03:32',3,10),
(137,'20180521010356010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:03:57','2018-05-21 01:04:02','2018-05-21 01:04:07',3,10),
(138,'20180516010413010','610113','怕踢校区','332026','兰总','17868643185','2018-05-16 01:04:15',NULL,NULL,1,10),
(139,'20180520010425010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:04:26','2018-05-20 01:04:29','2018-05-20 01:04:33',3,10),
(140,'20180520010451010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:04:53','2018-05-20 01:04:56','2018-05-20 01:05:00',3,10),
(141,'20180520010520010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:05:23','2018-05-20 01:05:27','2018-05-20 01:05:31',3,10),
(142,'20180520010546010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:05:47','2018-05-20 01:05:51','2018-05-20 01:05:55',3,10),
(143,'20180520010642010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:06:45','2018-05-20 01:06:48','2018-05-20 01:06:52',3,10),
(144,'20180520010652014','150303','维奇基尔6号','115599','其二','17602951130','2018-05-20 01:06:53','2018-05-20 01:06:58','2018-05-20 01:07:02',3,14),
(145,'20180520010717010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:07:18','2018-05-20 01:07:22','2018-05-20 01:07:26',3,10),
(146,'20180520010752014','150303','维奇基尔6号','115599','威威','17602951130','2018-05-20 01:07:53','2018-05-20 01:07:57','2018-05-20 01:08:00',3,14),
(147,'20180520010802010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:08:03','2018-05-20 01:08:07','2018-05-20 01:08:12',3,10),
(148,'20180520010841010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:08:42','2018-05-20 01:08:47','2018-05-20 01:08:51',3,10),
(149,'20180520010903014','130110','维奇基尔6号','115599','谔谔','17603984406','2018-05-20 01:09:05','2018-05-20 01:09:08','2018-05-20 01:09:12',3,14),
(150,'20180520010951014','130110','维奇基尔6号','115599','谔谔','17603984406','2018-05-20 01:09:51',NULL,NULL,4,14),
(151,'20180521011015010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:10:16','2018-05-21 01:10:18','2018-05-21 01:10:22',3,10),
(152,'2018052001104901','610102','核心大街','652222','江超','15815951240','2018-05-20 01:10:50',NULL,NULL,1,1),
(153,'2018052001105401','610102','核心大街','652222','江超','15815951240','2018-05-20 01:10:55','2018-05-21 01:11:59',NULL,2,1),
(154,'2018052001105901','610102','核心大街','652222','江超','15815951240','2018-05-20 01:11:00','2018-05-20 01:11:02',NULL,2,1),
(155,'2018052001111201','610102','核心大街','652222','江超','15815951240','2018-05-20 01:11:13','2018-05-21 01:11:50',NULL,2,1),
(156,'20180520011122010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:11:22',NULL,NULL,0,10),
(157,'20180521011152010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:11:53','2018-05-21 01:11:56','2018-05-21 01:12:02',3,10),
(158,'20180521011200014','130110','维奇基尔6号','115599','猪猪','17603984406','2018-05-21 01:12:02','2018-05-21 01:12:05','2018-05-21 01:12:09',3,14),
(159,'20180521011251010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:12:52','2018-05-21 01:12:55','2018-05-21 01:12:59',3,10),
(160,'2018052101141708','360481','燕山凹563号','332200','宝贝','17602930097','2018-05-21 01:14:18','2018-05-21 01:14:22','2018-05-21 01:14:26',3,8),
(161,'20180521011501010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:15:01',NULL,NULL,0,10),
(162,'20180521011504010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:15:05','2018-05-21 01:15:08','2018-05-21 01:15:12',3,10),
(163,'2018052101153808','360481','燕山凹563号','332200','宝贝','17602930097','2018-05-21 01:15:40','2018-05-21 01:15:43','2018-05-21 01:15:47',3,8),
(164,'20180521011735010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:17:36','2018-05-21 01:17:40','2018-05-21 01:17:44',3,10),
(165,'2018052101180408','360481','燕山凹563号','332200','宝贝','17602930097','2018-05-21 01:18:05','2018-05-21 01:18:09','2018-05-21 01:18:13',3,8),
(166,'20180522011958010','610113','怕踢校区','332026','兰总','17868643185','2018-05-22 01:19:59','2018-05-22 01:20:03','2018-05-22 01:20:09',3,10),
(167,'20180522012137010','610113','怕踢校区','332026','兰总','17868643185','2018-05-22 01:21:38','2018-05-22 01:21:42','2018-05-22 01:21:46',3,10),
(168,'20180522012242010','610113','怕踢校区','332026','兰总','17868643185','2018-05-22 01:22:43','2018-05-22 01:22:46','2018-05-22 01:22:51',3,10),
(169,'20180522012356010','610113','怕踢校区','332026','兰总','17868643185','2018-05-22 01:23:57','2018-05-22 01:24:01','2018-05-22 01:24:05',3,10),
(170,'20180522012518015','360481','燕山凹563号','332200','王宇豪','17602930097','2018-05-22 01:25:19','2018-05-22 01:25:23','2018-05-22 01:25:29',3,15),
(171,'20180522012640015','360481','燕山凹563号','332200','王宇豪','17602930097','2018-05-22 01:26:41','2018-05-22 01:26:45','2018-05-22 01:26:49',3,15),
(172,'20180522012742015','360481','燕山凹563号','332200','王宇豪','17602930097','2018-05-22 01:27:43','2018-05-22 01:27:46','2018-05-22 01:27:50',3,15),
(173,'2018052301290608','360481','燕山凹563号','332200','王宇豪','17602930097','2018-05-23 01:29:07','2018-05-23 01:29:10','2018-05-23 01:29:14',3,8),
(174,'2018052301304108','340102','香茗家园6单元608','662201','李天瞿','15933055640','2018-05-23 01:30:43','2018-05-23 01:30:46','2018-05-23 01:30:52',3,8),
(175,'20180523013128010','610113','怕踢校区','332026','兰总','17868643185','2018-05-23 01:31:31','2018-05-23 01:31:45','2018-05-23 01:31:57',3,10),
(176,'20180523013242016','340102','香茗家园6单元608','662201','李天瞿','15933055640','2018-05-23 01:32:43','2018-05-23 01:32:46','2018-05-23 01:32:51',3,16),
(177,'20180523013324016','360102','香茗家园6单元608','332299','李有钱','15933055640','2018-05-23 01:33:25','2018-05-23 01:33:28','2018-05-23 01:33:31',3,16),
(178,'20180523013358016','360121','香茗家园6单元608','332299','李有钱','15933055640','2018-05-23 01:33:59','2018-05-23 01:34:02','2018-05-23 01:34:05',3,16),
(179,'20180523013506016','340102','瑞敏家园8单元907','223366','吴国友','15908622147','2018-05-23 01:35:07','2018-05-23 01:35:10','2018-05-23 01:35:14',3,16),
(180,'20180520013833016','340102','瑞敏家园8单元907','223366','吴国友','15908622147','2018-05-20 01:38:33',NULL,NULL,0,16),
(181,'20180520013925016','340102','瑞敏家园8单元907','223366','江旭','15908622147','2018-05-20 01:39:25',NULL,NULL,4,16),
(182,'20180520013933010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:39:34','2018-05-20 01:39:38',NULL,2,10),
(183,'20180520013954010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:39:57',NULL,NULL,1,10),
(184,'20180520014010016','340102','瑞敏家园8单元907','223366','江旭','15908622147','2018-05-20 01:40:10',NULL,NULL,0,16),
(185,'20180520014017010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:40:17',NULL,NULL,0,10),
(186,'20180520014111010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:41:15','2018-05-20 01:41:20',NULL,2,10),
(187,'20180520014217010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:42:19','2018-05-20 01:42:24',NULL,2,10),
(188,'20180520014307010','610113','怕踢校区','332026','兰总','17868643185','2018-05-20 01:43:07',NULL,NULL,0,10),
(189,'20180525014536010','610113','怕踢校区','332026','兰总','17868643185','2018-05-25 01:45:38','2018-05-25 01:45:42',NULL,2,10),
(190,'2018052101455801','610102','核心大街','652222','江超','15815951240','2018-05-21 01:45:58',NULL,NULL,0,1),
(191,'2018052101460201','610102','核心大街','652222','江超','15815951240','2018-05-21 01:46:02',NULL,NULL,0,1),
(192,'2018052101460601','610102','核心大街','652222','江超','15815951240','2018-05-21 01:46:07',NULL,NULL,1,1),
(193,'2018052101462701','610102','核心大街','652222','江超','15815951240','2018-05-21 01:46:27',NULL,NULL,0,1),
(194,'2018052101463201','610102','核心大街','652222','江超','15815951240','2018-05-21 01:46:32',NULL,NULL,0,1),
(195,'2018052101463701','610102','核心大街','652222','江超','15815951240','2018-05-21 01:46:37',NULL,NULL,4,1),
(196,'2018052101464301','610102','核心大街','652222','江超','15815951240','2018-05-26 15:48:42',NULL,NULL,1,1),
(197,'20180521014646010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:46:48','2018-05-21 01:46:51',NULL,2,10),
(198,'2018052101474003','610102','核心大街','652222','江超','15815951240','2018-05-21 01:47:40',NULL,NULL,0,3),
(199,'2018052101474503','610102','核心大街','652222','江超','15815951240','2018-05-21 01:47:45',NULL,NULL,0,3),
(200,'20180521014802010','610113','怕踢校区','332026','兰总','17868643185','2018-05-21 01:48:05',NULL,NULL,1,10),
(201,'2018052101484503','610102','核心大街','652222','江超','15815951240','2018-05-21 01:48:45',NULL,NULL,0,3),
(202,'2018052101485003','610102','核心大街','652222','江超','15815951240','2018-05-21 01:48:50',NULL,NULL,0,3),
(203,'2018052101485503','610102','核心大街','652222','江超','15815951240','2018-05-21 01:48:55',NULL,NULL,0,3),
(204,'2018052101490103','610102','核心大街','652222','江超','15815951240','2018-05-21 01:49:01',NULL,NULL,0,3),
(205,'2018052101490903','610102','核心大街','652222','江超','15815951240','2018-05-21 01:49:09',NULL,NULL,4,3),
(206,'20180525141433016','220102','我i企鹅去','225611','阿斯顿','17601593302','2018-05-25 14:14:34','2018-05-25 14:14:38','2018-05-25 14:14:41',3,16),
(207,'2018052514241401','610102','核心大街','652222','江超','15815951240','2018-05-25 14:24:15','2018-05-25 15:11:31','2018-05-25 15:11:35',3,1),
(208,'20180525174553018','610722','街道','225611','阿斯顿','17601593302','2018-06-03 17:45:57','2018-06-03 17:49:49','2018-06-03 17:50:46',3,18),
(209,'20180525175237018','610722','街道','225611','阿斯顿','13335398248','2018-06-03 17:52:42','2018-06-03 17:52:52','2018-06-03 17:53:07',3,18),
(210,'20180526154727019','110101','XX街道','610000','坚持','15319449084','2018-06-04 15:47:40',NULL,NULL,1,19),
(211,'2018052615500401','110101','XX街道','610000','坚持','15319449084','2018-06-06 15:50:10','2018-06-13 10:47:51',NULL,2,1),
(212,'20180526155720020','110101','XX街道','610000','坚持','15319449084','2018-06-07 15:57:32','2018-06-07 16:07:25','2018-06-07 16:07:53',3,20),
(213,'20180526160942020','110101','XX街道','610000','坚持','15319449084','2018-06-08 16:09:48','2018-06-08 16:10:07','2018-06-08 16:10:16',3,20),
(214,'20180526164213021','110101','XX街道','610000','坚持','15319449084','2018-06-08 16:42:23','2018-06-08 16:48:38','2018-06-08 16:49:00',3,21),
(215,'20180526165132021','110101','XX街道','610000','坚持','15319449084','2018-06-09 16:51:37','2018-06-09 16:52:10','2018-06-09 16:52:19',3,21),
(216,'2018061310482401','110101','XX街道','610000','坚持','15319449084','2018-06-13 10:48:25','2018-06-13 10:48:28','2018-06-13 10:48:31',3,1),
(217,'2018052310402501','110101','XX街道','610000','坚持','15319449084','2018-05-23 10:40:27','2018-05-23 10:41:08','2018-05-23 10:41:21',3,1),
(218,'2018052311293201','610112','智慧路','610000','江超','15319449084','2018-05-23 11:29:46',NULL,NULL,1,1),
(219,'2018052314033101','610112','智慧路','610000','江超','15319449084','2018-05-23 14:03:43','2018-05-23 14:04:00','2018-05-23 14:04:11',3,1),
(220,'2018052314045801','610112','智慧路','610000','江超','15319449084','2018-05-23 14:05:01','2018-05-23 14:05:08','2018-05-23 14:05:15',3,1),
(221,'2018052314112801','610112','智慧路','610000','江超','15319449084','2018-05-23 14:11:29','2018-05-23 14:11:32','2018-05-23 14:11:38',3,1),
(222,'2023052517172603','110101','111','','111','18800000000','2023-05-25 17:17:29','2023-05-25 17:27:28','2023-06-14 09:25:45',3,3),
(223,'2024052821334901','110101','qwsa','432000','q','13787444570','2024-05-28 21:33:53',NULL,NULL,1,1),
(224,'2024052915372901','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-05-29 15:37:32',NULL,NULL,1,1),
(225,'2024061510090101','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-06-15 10:09:03','2024-06-15 11:06:45',NULL,2,1),
(226,'2024061510445301','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-06-15 10:44:57','2024-06-15 11:06:30',NULL,2,1),
(227,'2024061510572601','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-06-15 10:57:27','2024-06-15 11:05:44',NULL,2,1),
(228,'2024061710391401','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-06-17 10:39:16',NULL,NULL,1,1),
(229,'2024061711544401','110101','qwsa额温枪二无','432000','q恶趣味','13787444570','2024-06-17 11:54:56',NULL,NULL,1,1);

/*Table structure for table `productorderitem` */

DROP TABLE IF EXISTS `productorderitem`;

CREATE TABLE `productorderitem` (
  `productorderitem_id` int(11) NOT NULL AUTO_INCREMENT,
  `productorderitem_number` smallint(5) unsigned NOT NULL COMMENT '数量',
  `productorderitem_price` decimal(10,2) NOT NULL COMMENT '单价',
  `productorderitem_product_id` int(11) NOT NULL COMMENT '关联产品id',
  `productorderitem_order_id` int(11) DEFAULT NULL COMMENT '关联订单id',
  `productorderitem_user_id` int(11) NOT NULL COMMENT '关联用户id',
  `productorderitem_userMessage` varchar(255) DEFAULT NULL COMMENT '用户备注',
  PRIMARY KEY (`productorderitem_id`) USING BTREE,
  KEY `productorderitem_product_id` (`productorderitem_product_id`) USING BTREE,
  KEY `productorderitem_order_id` (`productorderitem_order_id`) USING BTREE,
  KEY `productorderitem_user_id` (`productorderitem_user_id`) USING BTREE,
  CONSTRAINT `productorderitem_ibfk_1` FOREIGN KEY (`productorderitem_product_id`) REFERENCES `product` (`product_id`),
  CONSTRAINT `productorderitem_ibfk_2` FOREIGN KEY (`productorderitem_order_id`) REFERENCES `productorder` (`productorder_id`) ON DELETE CASCADE,
  CONSTRAINT `productorderitem_ibfk_3` FOREIGN KEY (`productorderitem_user_id`) REFERENCES `user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=314 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品订单详细表';

/*Data for the table `productorderitem` */

insert  into `productorderitem`(`productorderitem_id`,`productorderitem_number`,`productorderitem_price`,`productorderitem_product_id`,`productorderitem_order_id`,`productorderitem_user_id`,`productorderitem_userMessage`) values 
(2,1,49.00,41,1,1,''),
(3,1,118.00,31,2,1,'回头客'),
(4,1,35.00,38,3,1,''),
(5,1,2599.00,42,4,1,''),
(6,5,12995.00,42,5,1,''),
(7,5,12995.00,42,6,1,''),
(8,1,35.00,38,7,1,''),
(9,1,469.00,30,8,1,''),
(10,1,35.00,38,9,1,''),
(11,1,109.00,67,10,1,''),
(12,1,528.00,25,11,1,''),
(13,2,576.00,43,12,1,''),
(14,1,569.00,44,13,1,''),
(15,1,79.90,35,14,1,''),
(21,1,49.00,41,15,1,''),
(24,1,69.00,5,16,1,''),
(26,1,89.00,26,17,1,''),
(33,1,69.90,40,20,1,NULL),
(34,1,35.00,38,18,1,''),
(35,1,118.00,31,19,1,''),
(36,1,139.00,36,20,1,NULL),
(37,2,518.00,39,20,1,NULL),
(38,1,680.00,61,20,1,NULL),
(39,2,990.00,77,20,1,NULL),
(40,1,139.00,36,20,1,NULL),
(41,1,69.90,40,20,1,NULL),
(42,2,990.00,77,20,1,NULL),
(43,1,259.00,39,20,1,NULL),
(44,1,680.00,61,20,1,NULL),
(45,1,78.90,33,20,1,NULL),
(46,1,59.00,34,20,1,NULL),
(47,1,59.00,34,21,1,''),
(48,1,69.90,40,23,1,''),
(49,1,69.90,40,22,1,''),
(50,1,2599.00,42,23,1,''),
(51,1,59.00,34,24,1,''),
(52,1,69.90,40,25,1,''),
(53,2,5198.00,42,26,1,'111'),
(54,3,2997.00,47,26,1,'222'),
(55,1,79.90,37,27,1,'zzz'),
(56,1,49.00,41,29,1,''),
(57,2,159.80,35,29,1,''),
(58,3,267.00,26,29,1,''),
(59,1,78.00,32,29,1,''),
(60,2,278.00,8,29,1,''),
(61,1,78.90,33,28,1,''),
(62,5,394.50,33,29,1,''),
(63,1,79.00,1,30,1,''),
(64,1,79.00,1,31,1,''),
(65,1,49.00,41,32,1,'disddadd'),
(66,2,98.00,41,33,1,'da'),
(67,1,79.00,1,33,1,'daad'),
(68,2,158.00,9,33,1,''),
(69,1,69.90,40,34,1,''),
(70,1,35.00,38,35,1,''),
(71,1,118.00,31,36,1,''),
(72,1,288.00,43,37,1,''),
(73,1,139.00,36,38,1,''),
(74,5,12995.00,42,39,1,''),
(75,1,59.00,34,40,1,''),
(76,1,79.90,37,41,1,''),
(77,1,89.00,26,42,1,''),
(78,1,528.00,25,155,1,''),
(79,1,78.00,32,155,1,''),
(80,1,49.00,41,43,1,'111133'),
(81,1,79.00,9,44,3,''),
(82,1,59.00,14,45,6,''),
(83,1,49.00,41,46,6,''),
(84,1,79.00,1,NULL,7,NULL),
(85,1,79.00,1,47,7,''),
(86,1,79.00,1,48,1,''),
(87,1,79.00,1,49,1,''),
(88,1,79.00,1,50,7,''),
(89,1,79.00,1,51,7,''),
(90,1,79.00,1,52,6,''),
(91,1,79.00,1,NULL,9,NULL),
(92,1,79.00,1,53,9,''),
(93,1,79.00,1,54,8,''),
(94,1,79.00,1,55,9,''),
(95,1,79.00,1,56,6,''),
(96,1,79.00,1,57,6,'小码'),
(97,1,49.00,41,58,8,''),
(98,5,12995.00,42,59,6,''),
(99,4,10396.00,42,NULL,9,NULL),
(100,3,7797.00,42,60,9,''),
(101,1,2599.00,42,61,1,''),
(102,1,2599.00,42,62,9,''),
(103,1,2599.00,42,63,10,''),
(104,1,2599.00,42,64,9,''),
(105,4,10396.00,42,65,8,''),
(106,1,2599.00,42,66,3,''),
(107,99,4851.00,41,67,3,''),
(108,1,79.00,1,68,3,''),
(109,100,259900.00,42,69,9,''),
(110,199,15721.00,1,70,3,''),
(111,160,415840.00,42,71,10,''),
(112,198,514602.00,42,72,8,''),
(113,20,51980.00,42,73,10,''),
(114,1,59.00,34,NULL,11,NULL),
(115,1,59.00,34,74,11,''),
(116,999,78921.00,1,75,12,''),
(117,1,49.00,41,76,11,''),
(118,1,1599.00,46,77,11,''),
(119,6,2814.00,30,78,10,''),
(120,1,469.00,30,79,11,''),
(121,5,2345.00,30,80,3,'带发票'),
(122,1,469.00,30,81,8,''),
(123,1,1190.00,68,82,12,''),
(124,1,469.00,30,83,13,''),
(125,1,79.00,1,84,3,''),
(126,6,708.00,31,NULL,7,NULL),
(127,5,590.00,31,85,7,''),
(128,1,139.00,21,NULL,7,NULL),
(129,1,139.00,21,86,7,''),
(130,3,417.00,8,87,10,''),
(131,1,139.00,36,88,7,''),
(132,1,169.00,29,89,7,''),
(133,1,35.00,38,90,7,''),
(134,1,1136.00,18,91,7,''),
(135,1,159.00,15,92,7,''),
(136,1,139.00,36,93,7,''),
(137,1,49.00,41,94,7,''),
(138,1,199.00,2,95,7,''),
(139,1,129.50,12,96,7,''),
(140,5,2845.00,44,97,10,''),
(141,1,118.00,31,98,7,''),
(142,1,79.00,9,99,7,''),
(143,1,199.00,2,100,12,''),
(144,1,139.00,21,101,7,''),
(145,1,1136.00,18,102,7,''),
(146,1,899.00,27,103,10,''),
(147,1,75.00,4,104,1,''),
(148,1,79.00,9,105,7,''),
(149,1,35.00,38,106,1,''),
(150,1,79.00,1,NULL,12,NULL),
(151,1,79.00,1,107,12,''),
(152,1,78.00,32,108,7,''),
(153,1,79.90,35,109,1,''),
(154,1,89.00,26,110,1,''),
(155,1,79.90,35,111,7,''),
(156,1,78.00,32,112,1,''),
(157,1,139.00,16,NULL,7,NULL),
(158,1,139.00,16,113,7,''),
(159,1,59.00,14,114,1,''),
(160,1,44.90,63,115,1,''),
(161,1,139.00,10,116,7,''),
(162,1,79.00,1,117,12,''),
(163,1,590.00,60,118,1,''),
(164,3,234.00,32,119,10,''),
(165,1,259.00,39,120,7,''),
(166,1,79.90,35,121,7,''),
(167,1,2599.00,42,122,14,''),
(168,1,79.00,1,123,3,''),
(169,500,1299500.00,42,124,14,''),
(170,1,79.00,9,125,3,''),
(171,1,350.00,62,126,10,''),
(172,1,2999.00,81,127,10,''),
(173,5,395.00,1,128,14,''),
(174,1,1599.00,46,129,10,''),
(175,1,4088.00,45,130,14,''),
(176,3,177.00,13,131,10,''),
(177,1,59.90,66,132,10,''),
(178,1,990.00,77,133,10,''),
(179,1,118.00,31,134,10,''),
(180,9,14391.00,46,135,10,''),
(181,1,49.00,41,136,10,''),
(182,1,69.90,40,137,10,''),
(183,1,528.00,25,138,10,''),
(184,1,139.00,36,139,10,''),
(185,3,239.70,35,140,10,''),
(186,6,654.00,64,141,10,''),
(187,8,7920.00,83,142,10,''),
(188,1,89.00,26,143,10,''),
(189,1,2599.00,42,144,14,''),
(190,6,408.00,17,145,10,''),
(191,1,399.60,28,146,14,''),
(192,2,1798.00,27,147,10,''),
(193,1,298.00,78,148,10,''),
(194,2,98.00,41,149,14,''),
(195,1,49.00,41,150,14,''),
(196,1,999.00,47,151,10,''),
(197,1,259.00,39,152,1,''),
(198,1,528.00,25,153,1,''),
(199,1,59.00,34,154,1,''),
(200,1,138.00,23,155,1,''),
(201,1,1480.00,75,156,10,''),
(202,1,1190.00,79,157,10,''),
(203,2,980.00,48,158,14,''),
(204,1,1480.00,75,159,10,''),
(205,2,3598.00,70,160,8,''),
(206,1,298.00,78,161,10,''),
(207,1,298.00,78,162,10,''),
(208,5,1440.00,43,163,8,''),
(209,1,1599.00,46,164,10,''),
(210,1,668.00,84,165,8,''),
(211,1,259.00,39,166,10,''),
(212,1,139.00,10,167,10,''),
(213,1,79.90,37,168,10,''),
(214,5,995.00,20,169,10,''),
(215,4,10236.00,71,170,15,''),
(216,1,138.00,23,171,15,''),
(217,1,2599.00,42,172,15,''),
(218,1,139.00,16,173,8,''),
(219,60,34140.00,44,174,8,''),
(220,1,288.00,43,175,10,''),
(221,50,49950.00,47,176,16,''),
(222,1,118.00,31,177,16,''),
(223,1,528.00,25,178,16,''),
(224,1,159.00,15,179,16,''),
(225,10,790.00,1,180,16,''),
(226,1,899.00,27,181,16,''),
(227,1,59.00,14,182,10,''),
(228,1,59.90,66,183,10,''),
(229,1,329.00,22,184,16,''),
(230,1,680.00,61,185,10,''),
(231,1,59.00,34,186,10,''),
(232,1,4089.00,80,187,10,''),
(233,1,4089.00,80,188,10,''),
(234,1,999.00,47,189,10,''),
(235,1,69.90,40,190,1,''),
(236,1,69.00,5,191,1,''),
(237,1,399.60,28,192,1,''),
(238,1,469.00,30,193,1,''),
(239,1,2999.00,81,194,1,''),
(240,1,569.00,44,195,1,''),
(241,1,999.00,47,196,1,''),
(242,1,1599.00,46,197,10,''),
(243,1,4089.00,80,198,3,''),
(244,1,329.00,22,199,3,''),
(245,1,1190.00,68,200,10,''),
(246,1,139.00,36,201,3,''),
(247,1,59.00,65,202,3,''),
(248,1,44.90,63,203,3,''),
(249,1,230.00,57,204,3,''),
(250,1,288.00,43,205,3,''),
(251,1,59.00,34,211,1,''),
(252,1,139.00,36,211,1,''),
(253,4,796.00,20,211,1,''),
(254,1,49.00,41,206,16,''),
(255,1,49.00,41,207,1,''),
(261,5,2845.00,44,NULL,17,NULL),
(262,5,12995.00,42,208,18,'带包装盒'),
(263,1,2599.00,42,209,18,''),
(264,1,569.00,44,209,18,''),
(265,1,469.00,30,210,19,'128250000'),
(266,3,987.00,24,211,1,''),
(267,4,3596.00,27,211,1,''),
(268,1,899.00,27,212,20,'大一码'),
(269,2,98.00,41,213,20,''),
(270,1,139.00,36,213,20,''),
(271,1,89.00,26,214,21,'黑色'),
(273,4,140.00,38,215,21,''),
(274,4,10396.00,42,215,21,''),
(275,4,6396.00,46,215,21,''),
(276,1,259.00,39,216,1,''),
(277,1,59.80,11,217,1,''),
(278,1,49.00,41,218,1,''),
(279,1,2599.00,42,219,1,'黑色'),
(281,5,20440.00,45,220,1,''),
(282,1,569.00,44,220,1,''),
(283,1,49.00,41,220,1,''),
(284,1,2599.00,42,221,1,''),
(285,1,469.00,30,222,3,''),
(287,1,118.00,31,223,1,''),
(291,1,35.00,38,228,1,''),
(292,2,938.00,30,224,1,''),
(298,1,59.00,34,225,1,''),
(300,3,147.00,41,227,1,''),
(304,2,398.00,2,226,1,''),
(309,1,469.00,30,228,1,''),
(310,1,569.00,44,NULL,1,NULL),
(311,1,59.00,34,229,1,'xuxu'),
(312,1,139.00,8,229,1,'');

/*Table structure for table `property` */

DROP TABLE IF EXISTS `property`;

CREATE TABLE `property` (
  `property_id` int(11) NOT NULL AUTO_INCREMENT,
  `property_name` varchar(25) NOT NULL COMMENT '属性名称',
  `property_category_id` int(11) NOT NULL COMMENT '关联类别id',
  PRIMARY KEY (`property_id`) USING BTREE,
  KEY `property_category_id` (`property_category_id`) USING BTREE,
  CONSTRAINT `property_ibfk_1` FOREIGN KEY (`property_category_id`) REFERENCES `category` (`category_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=245 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='类别属性表';

/*Data for the table `property` */

insert  into `property`(`property_id`,`property_name`,`property_category_id`) values 
(1,'材质成分',1),
(2,'服装版型',1),
(3,'风格',1),
(4,'衣长',1),
(5,'袖长',1),
(6,'领型',1),
(7,'图案',1),
(8,'品牌',1),
(9,'适用年龄',1),
(11,'颜色分类',1),
(12,'尺码',1),
(13,'材质成分',2),
(14,'服装版型',2),
(15,'风格',2),
(16,'衣长',2),
(17,'袖长',2),
(18,'领型',2),
(19,'图案',2),
(20,'品牌',2),
(21,'适用年龄',2),
(22,'年份季节',2),
(23,'颜色分类',2),
(24,'尺码',2),
(25,'品牌',3),
(26,'上市年份季节',3),
(27,'帮面材质',3),
(28,'鞋底材质',3),
(29,'鞋头款式',3),
(30,'后跟高',3),
(31,'跟底款式',3),
(32,'鞋帮高度',3),
(33,'颜色分类',3),
(34,'尺码',3),
(35,'后帮',3),
(41,'是否商场同款',5),
(42,'品牌',5),
(43,'型号',5),
(44,'机芯类型',5),
(45,'手表种类',5),
(46,'风格',5),
(47,'表带材质',5),
(48,'形状',5),
(49,'显示方式',5),
(50,'上市时间',5),
(51,'颜色分类',5),
(52,'防水深度',5),
(53,'品牌产地',5),
(54,'证书编号',6),
(55,'证书状态',6),
(56,'产品名称',6),
(57,'3C规格型号',6),
(58,'产品名称',6),
(59,'品牌名称',6),
(60,'CPU型号',6),
(61,'机身颜色',6),
(62,'网络模式',6),
(63,'品牌',7),
(64,'安全等级',7),
(65,'材质成分',7),
(66,'货号',7),
(67,'适用性别',7),
(68,'颜色分类',7),
(69,'参考身高',7),
(70,'款式',7),
(71,'风格',7),
(72,'适用季节',7),
(73,'面料',7),
(74,'衣门襟',7),
(75,'袖长',7),
(76,'生产许可编号',8),
(77,'产品标准号',8),
(78,'厂名',8),
(79,'厂址',8),
(80,'厂家联系方式',8),
(81,'配料表',8),
(82,'储藏方法',8),
(83,'保质期',8),
(84,'食品添加剂',8),
(85,'净含量',8),
(86,'包装方式',8),
(87,'包装种类',8),
(88,'品牌',8),
(89,'系列',8),
(90,'有机食品',8),
(91,'食品口味',8),
(92,'是否含糖',8),
(93,'产地',8),
(94,'省份',8),
(95,'城市',8),
(96,'厂名',9),
(97,'厂址',9),
(98,'厂家联系方式',9),
(99,'配料表',9),
(100,'储藏方法',9),
(101,'保质期',9),
(102,'食品添加剂',9),
(103,'个数',9),
(104,'净含量',9),
(105,'包装方式',9),
(106,'原产地',9),
(107,'同城服务',9),
(108,'品牌',9),
(109,'售卖方式',9),
(110,'有机食品',9),
(111,'生鲜储存温度',9),
(112,'热卖时间',9),
(113,'产地',9),
(114,'省份',9),
(115,'城市',9),
(116,'套餐份量',9),
(117,'套餐周期',9),
(118,'配送频次',9),
(119,'特产品类',9),
(120,'价格',9),
(121,'水果种类',9),
(122,'证书编号',10),
(123,'证书状态',10),
(124,'产品名称',10),
(125,'3C规格型号',10),
(127,'保修期',10),
(128,'品牌',10),
(129,'型号',10),
(130,'能效等级',10),
(131,'空调类型',10),
(132,'分辨率',10),
(133,'工作方式',10),
(134,'适用面积',10),
(135,'产品类型',10),
(136,'使用方式',10),
(137,'电机类型',10),
(138,'品牌',11),
(139,'型号',11),
(140,'安装辅材包',11),
(141,'计价单位',11),
(142,'颜色分类',11),
(143,'厚度',11),
(144,'基材',11),
(145,'尺寸',11),
(146,'材质',11),
(147,'品牌',12),
(148,'型号',12),
(149,'汽车品牌',12),
(150,'适用季节',12),
(151,'表层材质',12),
(152,'颜色分类',12),
(153,'主要材质含量',12),
(154,'图案',12),
(155,'座位数',12),
(156,'替换原车套',12),
(157,'发动机种类',12),
(158,'产品名称',12),
(159,'净含量',12),
(160,'机油分类',12),
(161,'粘度级别',12),
(162,'机油级别',12),
(163,'品牌',13),
(164,'产品等级',13),
(165,'制造工艺',13),
(166,'尺寸',13),
(167,'羽绒羽毛种类',13),
(168,'被套织造工艺',13),
(169,'被套面料',13),
(170,'被子种类',13),
(171,'绒子含量',13),
(172,'货号',13),
(173,'适用季节',13),
(174,'颜色分类',13),
(175,'适用场景',13),
(176,'鲜花主花材',13),
(177,'适用对象',13),
(178,'鲜花朵数',13),
(179,'适用节日',13),
(180,'绿植工艺',13),
(181,'鲜花规格',13),
(182,'花束辅材',13),
(183,'品牌',14),
(184,'产品名称',14),
(185,'产品剂型',14),
(186,'使用剂量',14),
(187,'套餐类型',14),
(188,'有效期',14),
(189,'用法',14),
(190,'药品分类',14),
(191,'药品名称',14),
(192,'药品通用名',14),
(193,'批准文号',14),
(194,'生产企业',14),
(195,'规格',14),
(196,'类别',14),
(197,'品牌',15),
(198,'产品名称',15),
(199,'包装体积',15),
(200,'流行元素',15),
(201,'适用人群',15),
(202,'适用场景',15),
(203,'颜色分类',15),
(204,'风格',15),
(205,'型号',15),
(206,'材质',15),
(207,'产地',15),
(208,'净重',15),
(209,'原产地',15),
(210,'品名',15),
(211,'毛重',15),
(212,'厂家名称',15),
(213,'厂家地址',15),
(214,'狗狗品种',15),
(215,'适用阶段',15),
(216,'食品口味',15),
(217,'配方/口味',15),
(218,'产品名称',16),
(219,'是否是套装',16),
(220,'书名',16),
(221,'定价',16),
(222,'出版社名称',16),
(223,'出版时间',16),
(224,'作者',16),
(225,'编者',16),
(226,'开本',16),
(227,'书名',16),
(228,'ISBN编号',16),
(229,'品牌',4),
(230,'产品名称',4),
(231,'保质期',4),
(232,'面膜分类',4),
(233,'规格类型',4),
(234,'适合肤质',4),
(235,'净含量',4),
(236,'品名',4),
(237,'批准文号',4),
(238,'功效',4),
(239,'产地',4),
(240,'上市时间',4),
(241,'型号',4),
(242,'适用对象',4),
(243,'件数',4),
(244,'适用发质',4);

/*Table structure for table `propertyvalue` */

DROP TABLE IF EXISTS `propertyvalue`;

CREATE TABLE `propertyvalue` (
  `propertyvalue_id` int(11) NOT NULL AUTO_INCREMENT,
  `propertyvalue_value` varchar(100) NOT NULL COMMENT '属性值',
  `propertyvalue_property_id` int(11) NOT NULL COMMENT '关联属性id',
  `propertyvalue_product_id` int(11) NOT NULL COMMENT '关联产品id',
  PRIMARY KEY (`propertyvalue_id`) USING BTREE,
  KEY `propertyvalue_property_id` (`propertyvalue_property_id`) USING BTREE,
  KEY `propertyvalue_product_id` (`propertyvalue_product_id`) USING BTREE,
  CONSTRAINT `propertyvalue_ibfk_1` FOREIGN KEY (`propertyvalue_property_id`) REFERENCES `property` (`property_id`) ON DELETE CASCADE,
  CONSTRAINT `propertyvalue_ibfk_2` FOREIGN KEY (`propertyvalue_product_id`) REFERENCES `product` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=723 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='产品属性管理表';

/*Data for the table `propertyvalue` */

insert  into `propertyvalue`(`propertyvalue_id`,`propertyvalue_value`,`propertyvalue_property_id`,`propertyvalue_product_id`) values 
(1,'通勤',3,1),
(2,'宽松',2,1),
(4,'棉95.1% 聚氨酯弹性纤维(氨纶)4.9%',1,1),
(5,'拼色',7,1),
(6,'连帽',6,1),
(7,'长袖',5,1),
(8,'常规',4,1),
(9,'18-24周岁',9,1),
(10,'戚米',8,1),
(11,'白色 红色 黄色',11,1),
(12,'均码',12,1),
(13,'通勤',3,2),
(14,'宽松',2,2),
(16,'棉98.4% 粘胶纤维(粘纤)1.6%',1,2),
(17,'纯色',7,2),
(18,'连帽',6,2),
(19,'长袖',5,2),
(20,'中长款',4,2),
(21,'18-24周岁',9,2),
(22,'乔琪诺',8,2),
(23,'蓝色',11,2),
(24,'XS S M L',12,2),
(25,'纯色',19,3),
(26,'长袖',17,3),
(27,'青春流行',15,3),
(28,'聚酯纤维95% 聚氨酯弹性纤维(氨纶)5%',13,3),
(29,'高帮',32,4),
(30,'浅蓝 深蓝',33,4),
(31,'35 36 37 38 39',34,4),
(32,'人本',25,4),
(33,'2017年春季',26,4),
(34,'橡胶',28,4),
(35,'通勤',3,5),
(36,'宽松',2,5),
(38,'棉80% 聚酯纤维20%',1,5),
(39,'字母',7,5),
(40,'连帽',6,5),
(41,'长袖',5,5),
(42,'短款',4,5),
(43,'18-24周岁',9,5),
(44,'沂森',8,5),
(45,'白色MOU 粉色MOU 姜黄MOU 蓝色MOU 卡其MOU 红色MOU 黑色MOU 灰色MOU 姜',11,5),
(46,'S M L XL',12,5),
(47,'中年',21,6),
(48,'商务绅士',15,6),
(49,'棉65.2% 聚酯纤维33.6% 聚氨酯弹性纤维(氨纶)1.2%',13,6),
(50,'低帮',32,7),
(51,'35 36 37 38 39',34,7),
(52,'人本',25,7),
(53,'2016年夏季',26,7),
(54,'橡胶',28,7),
(55,'长袖',17,8),
(56,'方领',18,8),
(57,'商务休闲',15,8),
(58,'聚酯纤维95% 聚氨酯弹性纤维(氨纶)5%',13,8),
(59,'修身',14,8),
(60,'通勤',3,9),
(61,'直筒',2,9),
(63,'棉94% 聚氨酯弹性纤维(氨纶)6%',1,9),
(64,'人物',7,9),
(65,'连帽',6,9),
(66,'长袖',5,9),
(67,'常规',4,9),
(68,'18-24周岁',9,9),
(69,'Loytio/洛蒂欧',8,9),
(70,'白色 绿色',11,9),
(71,'均码',12,9),
(72,'青年',21,10),
(73,'条纹',19,10),
(74,'长袖',17,10),
(75,'翻领',18,10),
(76,'青春流行',15,10),
(77,'粘胶纤维(粘纤)55% 聚酯纤维45%',13,10),
(78,'修身',14,10),
(79,'高帮',32,11),
(80,'黑夜光仰望中 黑仰望中非夜光 白仰望中 兰花鹿中 白底背影中 黑底背影中 梅花鹿中 白夜光仰望中',33,11),
(81,'30 31 32 33 34 35 36 37 38 39 40 41 42 43',34,11),
(82,'牛牛手绘',25,11),
(83,'2015年秋季',26,11),
(84,'牛筋',28,11),
(85,'街头',3,12),
(86,'宽松',2,12),
(88,'棉100%',1,12),
(89,'字母',7,12),
(90,'圆领',6,12),
(91,'短袖',5,12),
(92,'常规款',4,12),
(93,'25-29周岁',9,12),
(94,'ELF SACK/妖精的口袋',8,12),
(95,'芝士白（SLXL预售4.27发)',11,12),
(96,'S M L XL',12,12),
(97,'高帮',32,13),
(98,'N810深蓝纯色 N810浅蓝纯色 N810黑色纯色 N810深蓝夜光星星 N810浅蓝夜光星星',33,13),
(99,'35 36 37 38 39 40',34,13),
(100,'VIVIENKLEIN',25,13),
(101,'2016年春季',26,13),
(102,'橡胶',28,13),
(103,'高帮',32,14),
(104,'黑色-高帮情侣款 白色-高帮情侣款 黑色-高帮女孩 白色-高帮女孩 黑色-高帮男孩 白色-高帮男孩',33,14),
(105,'35 36 37 38 39 40 41 42 43',34,14),
(106,'YOVCA',25,14),
(107,'2015年秋季',26,14),
(108,'橡胶',28,14),
(110,'棉91.3% 聚酯纤维7.3% 聚氨酯弹性纤维(氨纶)1.4%',1,15),
(111,'25-29周岁',9,15),
(112,'ESE·Y/逸阳',8,15),
(113,'牛仔蓝-八分 浅蓝色-八分 牛仔蓝-九分',11,15),
(114,'25/5 26/7 27/9 28/11 29/13 30/15 31/17 32//19 33/',12,15),
(115,'青年',21,16),
(116,'方领',18,16),
(117,'青春流行',15,16),
(118,'聚酯纤维68.7% 棉31.3%',13,16),
(119,'修身',14,16),
(120,'低帮',32,17),
(121,'518黑白 518黑蓝橘 518黑红 518黑玫红 518红色 2616黑色 2616白色 261',33,17),
(122,'35 36 37 38 39 40 41 42 43 44',34,17),
(123,'韵肯',25,17),
(124,'2016年夏季',26,17),
(125,'橡胶',28,17),
(126,'通勤',3,18),
(128,'聚酯纤维100%',1,18),
(129,'碎花',7,18),
(130,'V领',6,18),
(131,'短袖',5,18),
(132,'中裙',4,18),
(133,'35-39周岁',9,18),
(134,'Koradior/珂莱蒂尔',8,18),
(135,'花色',11,18),
(136,'S M L XL XXL',12,18),
(137,'低跟(1-3cm)',30,19),
(138,'低帮',32,19),
(139,'平底',31,19),
(140,'27深兰（H14W7521） 07绿色（H14W7521） 14粉红（H14W7521） 04白色',33,19),
(141,'34 35 36 37 38 39',34,19),
(142,'Hotwind/热风',25,19),
(143,'2017年春季',26,19),
(144,'超细纤维',27,19),
(145,'橡胶',28,19),
(146,'其他',29,19),
(147,'其他',3,20),
(148,'标准',2,20),
(149,'聚酯纤维58.5% 棉41.5%',1,20),
(150,'纯色',7,20),
(151,'尖领（常规）',6,20),
(152,'短袖',5,20),
(153,'青年',9,20),
(155,'棉63.5% 聚酯纤维31.6% 聚氨酯弹性纤维(氨纶)4.9%',1,21),
(156,'25-35周岁',9,21),
(157,'Free Breath/自由呼吸',8,21),
(158,'桔红【预售L 4/25陆续发货】 浅粉【预售M/L/XL/2XL 4/25陆续发货】 柠黄【预售M',11,21),
(159,'L M XL XXL XXXL',12,21),
(160,'中跟(3-5cm)',30,22),
(161,'细跟',31,22),
(162,'Black黑色 Navy深蓝色 White白色',33,22),
(163,'34 35 36 37 38 39 40 41',34,22),
(164,'CHARLES&KEITH',25,22),
(165,'2018年春季',26,22),
(166,'PU',27,22),
(167,'复合底',28,22),
(168,'尖头',29,22),
(170,'棉95% 聚氨酯弹性纤维(氨纶)5%',1,23),
(171,'25-35周岁',9,23),
(172,'梦辕',8,23),
(173,'彩蓝+白裤 荧光绿+白裤 玫红+黑裤 白色+白裤 玫红+白裤 彩蓝+黑裤 荧光绿+黑裤 白色+黑裤',11,23),
(174,'L【建议102-112斤】 M【建议92-102斤】 S 【建议65-92斤】 XL【建议112-',12,23),
(175,'低跟(1-3cm)',30,24),
(176,'方跟',31,24),
(177,'Black黑色 Pink粉红色 White白色',33,24),
(178,'35 36 37 38 39 40 41',34,24),
(179,'CHARLES&KEITH',25,24),
(180,'2018年春季',26,24),
(181,'PU',27,24),
(182,'复合底',28,24),
(183,'尖头',29,24),
(185,'聚酯纤维95% 聚氨酯弹性纤维(氨纶)5%',1,25),
(186,'25-35周岁',9,25),
(187,'PHICIA/菲西娅',8,25),
(188,'杏色',11,25),
(189,'M L XL XXL',12,25),
(190,'青年',21,26),
(191,'圆点',19,26),
(192,'短袖',17,26),
(193,'扣领尖领',18,26),
(194,'商务绅士',15,26),
(195,'聚酯纤维60% 棉40%',13,26),
(196,'修身',14,26),
(197,'004大气灰/黑/银灰色/白 黑/闪电蓝/超红/浅碳黑 001黑/白 104白/黑/脉冲珊瑚蓝',33,27),
(198,'38.5 39 40 40.5 41 42 42.5 43 44 44.5 45 46 47',34,27),
(199,'Nike/耐克',25,27),
(200,'2018年夏季',26,27),
(201,'织物/橡塑材料',27,27),
(202,'时尚都市',3,28),
(203,'标准',2,28),
(204,'棉100%',1,28),
(205,'纯色',7,28),
(206,'立领',6,28),
(207,'中长款',4,28),
(208,'青年',9,28),
(209,'通勤',3,29),
(210,'直筒',2,29),
(212,'棉100%',1,29),
(213,'圆点',7,29),
(214,'POLO领',6,29),
(215,'长袖',5,29),
(216,'常规款',4,29),
(217,'25-29周岁',9,29),
(218,'INMAN/茵曼',8,29),
(219,'浅普蓝（现货，领券减10） 象牙白（领券减10） 粉桃红（现货，领券减10）',11,29),
(220,'S M L XL',12,29),
(221,'800浅红/蓝黑蓝/银灰色/白 100白/狼灰/银灰色/金属银 黑/黑/煤黑/白',33,30),
(222,'35.5 36 36.5 37.5 38 38.5 39 40 40.5 41 42',34,30),
(223,'Nike/耐克',25,30),
(224,'2018年夏季',26,30),
(225,'通勤',3,31),
(226,'宽松',2,31),
(228,'其他100%',1,31),
(229,'其他',7,31),
(230,'V领',6,31),
(231,'长袖',5,31),
(232,'中长款',4,31),
(233,'瑷珊',8,31),
(234,'浅咖色 黑色 红色',11,31),
(235,'S M L XL',12,31),
(236,'青年',21,32),
(237,'字母数字',19,32),
(238,'圆领',18,32),
(239,'青春流行',15,32),
(240,'棉100%',13,32),
(241,'标准',14,32),
(242,'青春流行',3,33),
(243,'标准',2,33),
(244,'棉100%',1,33),
(245,'圆领',6,33),
(246,'短袖',5,33),
(247,'通勤',3,34),
(248,'修身',2,34),
(250,'棉92.2% 聚氨酯弹性纤维(氨纶)7.8%',1,34),
(251,'纯色',7,34),
(252,'圆领',6,34),
(253,'短袖',5,34),
(254,'常规款',4,34),
(255,'25-29周岁',9,34),
(256,'Mefound/棉立方',8,34),
(257,'茉莉白 玫瑰灰 天青',11,34),
(258,'S M L XL',12,34),
(259,'甜美',3,36),
(260,'宽松',2,36),
(262,'聚酯纤维100%',1,36),
(263,'花色',7,36),
(264,'荷叶领',6,36),
(265,'长袖',5,36),
(266,'常规款',4,36),
(267,'18-24周岁',9,36),
(268,'润乙一',8,36),
(269,'绿色 黄色',11,36),
(270,'XS M S',12,36),
(271,'青春流行',3,37),
(272,'标准',2,37),
(273,'棉96.9% 聚氨酯弹性纤维(氨纶)3.1%',1,37),
(274,'字母数字',7,37),
(275,'圆领',6,37),
(276,'短袖',5,37),
(277,'青年',9,37),
(278,'通勤',3,38),
(279,'宽松',2,38),
(281,'棉95% 聚氨酯弹性纤维(氨纶)5%',1,38),
(282,'卡通动漫 动物图案 植物花卉 几何图案 字母 数字',7,38),
(283,'圆领',6,38),
(284,'短袖',5,38),
(285,'常规款',4,38),
(286,'18-24周岁',9,38),
(287,'彤莉兰',8,38),
(288,'圆领470 黑 V领470 黑 T073 白 T073 黑 T073 黄 T073 粉 T073',11,38),
(289,'M L XL XXL',12,38),
(290,'通勤',3,40),
(291,'宽松',2,40),
(293,'棉59.2% 聚酯纤维36.7% 聚氨酯弹性纤维(氨纶)4.1%',1,40),
(294,'几何图案 字母',7,40),
(295,'圆领',6,40),
(296,'短袖',5,40),
(297,'常规款',4,40),
(298,'18-24周岁',9,40),
(299,'柠衣阁',8,40),
(300,'【2件69.9】9052黄色+6477砖红色 【2件69.9】9052黄色+6480芒果黄 【2件',11,40),
(301,'M L XL XXL',12,40),
(302,'甜美',3,41),
(303,'直筒',2,41),
(305,'棉100%',1,41),
(306,'卡通动漫 动物图案 字母',7,41),
(307,'圆领',6,41),
(308,'短袖',5,41),
(309,'常规款',4,41),
(310,'18-24周岁',9,41),
(311,'相公娘子',8,41),
(312,'灰色反1社1交1 粉色反社交1 sup白色1 灰色sup 黑色sup1 蓝色sup1 粉色sup1',11,41),
(313,'S M L XL XXL XXXL',12,41),
(314,'OPPO',59,42),
(315,'OPPO R11S',58,42),
(316,'OPPO R11s（电源适配器：AK779、AK779GB、AK779JH、AK779JH1、VC5',57,42),
(317,'TD-LTE数字移动电话机',56,42),
(318,'有效',55,42),
(319,'双卡多模',62,42),
(320,'2017011606006547',54,42),
(321,'SDM660',60,42),
(322,'香槟色 星幕新年版 黑色',61,42),
(323,'5 PATARABET SUD ，33330 SAINT EMILION，FRANCE',79,43),
(324,'SAS CELLAR PRIVILEGE',78,43),
(325,'常温、避光、卧放',82,43),
(326,'5475 天',83,43),
(327,'86-10-8485 3024',80,43),
(328,'葡萄汁、二氧化硫',81,43),
(329,'包装',86,43),
(330,'Chateau Monlot/梦陇酒庄',88,43),
(331,'梦陇经典系列',89,43),
(332,'1500ml',85,43),
(333,'小米',59,44),
(334,'Xiaomi/小米 红米5A',58,44),
(335,'MCE3B（电源适配器：MDY-08-ET 输出：5VDC 1.0A）',57,44),
(336,'TD-LTE数字移动电话机',56,44),
(337,'有效',55,44),
(338,'双卡多模',62,44),
(339,'2017011606004154',54,44),
(340,'骁龙425',60,44),
(341,'香槟金 铂银灰 浅蓝色 樱花粉',61,44),
(342,'150m^2 及以上',134,46),
(343,'S6 白色、S6 黑色，工作参数：直流14.8V 30W；充电参数：直流24V 1A ；',125,46),
(344,'12个月',127,46),
(345,'360',128,46),
(346,'2017010708036299',122,46),
(347,'有效',123,46),
(348,'360扫地机器人',124,46),
(349,'S6',129,46),
(350,'Huawei/华为',59,45),
(351,'Huawei/华为 P20',58,45),
(352,'EML-AL00（开关电源适配器：HW-050450C00 输出：5VDC，2A或4.5VDC，5A',57,45),
(353,'TD-LTE数字移动电话机',56,45),
(354,'有效',55,45),
(355,'双卡双待',62,45),
(356,'2018011606048695',54,45),
(357,'宝石蓝 香槟金 渐变樱粉金 亮黑色',61,45),
(358,'Meizu/魅族',59,47),
(359,'Meizu/魅族 魅蓝 Note6',58,47),
(360,'M721Q (USB电源适配器：UP0830S    输出:5.0VDC 3A/8VDC 3A/12',57,47),
(361,'TD-LTE 数字移动电话机',56,47),
(362,'有效',55,47),
(363,'双卡双待',62,47),
(364,'2017011606986361',54,47),
(365,'骁龙625',60,47),
(366,'香槟金 皓月银 曜石黑 孔雀青',61,47),
(367,'Estee Lauder/雅诗兰黛 肌透修护眼部精华霜',230,48),
(368,'2019-01-01至2019-12-31',231,48),
(369,'Estee Lauder/雅诗兰黛',229,48),
(370,'正常规格',233,48),
(371,'改善眼部暗沉 淡化细纹 滋润 补水',238,48),
(372,'2013年',240,49),
(373,'Clarins/娇韵诗 清透美白防晒亮彩乳SPF40',230,49),
(374,'Clarins/娇韵诗',229,49),
(375,'任何肤质',234,49),
(376,'女士',242,49),
(377,'正常规格',233,49),
(378,'Clarins/娇韵诗 清透美白防晒亮彩乳SPF40',236,49),
(379,'50ml 30ml',235,49),
(380,'防晒',238,49),
(381,'国妆特进字J20150495',237,49),
(382,'Clarins/娇韵诗 纤妍紧致精华乳',230,50),
(383,'2020-01-01至2020-04-02',231,50),
(384,'Clarins/娇韵诗',229,50),
(385,'任何肤质',234,50),
(386,'正常规格',233,50),
(387,'50ml',235,50),
(388,'提拉紧致 紧致肌肤',238,50),
(389,'国妆特进字J20150497',237,50),
(390,'Clarins/娇韵诗 青春赋活精华水',230,51),
(391,'Clarins/娇韵诗',229,51),
(392,'正常规格',233,51),
(393,'200ml',235,51),
(394,'补水 保湿',238,51),
(395,'Clarins/娇韵诗 焕颜紧致眼霜',230,52),
(396,'2019-08-02至2019-09-12',231,52),
(397,'Clarins/娇韵诗',229,52),
(398,'任何肤质',234,52),
(399,'正常规格',233,52),
(400,'15ml',235,52),
(401,'眼部舒缓 紧致',238,52),
(402,'80017653',241,53),
(403,'Clarins/娇韵诗',229,53),
(405,'Clarins/娇韵诗 恒润奇肌保湿凝露',230,54),
(406,'Clarins/娇韵诗',229,54),
(407,'正常规格',233,54),
(408,'凝露',236,54),
(409,'50ml',235,54),
(410,'补水 保湿 控油',238,54),
(411,'日本',239,55),
(412,'IPSA/茵芙莎 纯美无瑕修饰遮瑕膏SPF25/PA+++',230,55),
(413,'防晒类',232,55),
(414,'IPSA/茵芙莎',229,55),
(415,'任何肤质',234,55),
(416,'脸部',242,55),
(417,'正常规格',233,55),
(418,'IPSA/茵芙莎',236,55),
(419,'4.5g',235,55),
(420,'修饰肤色 遮瑕 保湿',238,55),
(421,'国妆特进字J20170439',237,55),
(422,'IPSA/茵芙莎 水润倍护防晒日乳EX',230,56),
(423,'IPSA/茵芙莎',229,56),
(424,'正常规格',233,56),
(425,'IPSA/茵芙莎',236,56),
(426,'30ml',235,56),
(427,'防晒 隔离',238,56),
(428,'国妆特进字J20161126',237,56),
(429,'IPSA/茵芙莎 舒缓净润洁面乳',230,57),
(430,'价格 ¥480.00  商品详情 累计评价 1369 手机购买  品牌名称：',229,57),
(431,'正常规格',233,57),
(432,'价格 ¥480.00  商品详情 累计评价 1369 手机购买  品牌名称：',236,57),
(433,'125g',235,57),
(434,'舒缓肌肤',238,57),
(435,'国妆备进字J20162948',237,57),
(436,'日本',239,58),
(437,'IPSA茵芙莎水乳套装UL 流金水自律循环美肌液护肤品套装',230,58),
(438,'IPSA/茵芙莎',229,58),
(439,'任何肤质',234,58),
(440,'正常规格',233,58),
(441,'IPSA/茵芙莎',236,58),
(442,'补水 保湿 提拉紧致 提亮肤色 滋润 舒缓肌肤 嫩肤 舒缓镇定 淡斑',238,58),
(443,'Estee Lauder/雅诗兰黛 鲜亮焕采泡沫洁面乳',230,59),
(444,'36个月',231,59),
(445,'Estee Lauder/雅诗兰黛',229,59),
(446,'正常规格',233,59),
(447,'Estee Lauder/雅诗兰黛',236,59),
(448,'125ml',235,59),
(449,'提亮肤色 补水 保湿 舒缓肌肤 清洁毛孔',238,59),
(450,'Estee Lauder/雅诗兰黛 新肌透修护眼部密集精华',230,60),
(451,'Estee Lauder/雅诗兰黛',229,60),
(452,'正常规格',233,60),
(453,'Estee Lauder/雅诗兰黛',236,60),
(454,'15ml',235,60),
(455,'保湿补水 淡化细纹 提拉紧致',238,60),
(456,'Estee Lauder/雅诗兰黛 密集修护肌透面膜',230,61),
(457,'36个月',231,61),
(458,'Estee Lauder/雅诗兰黛',229,61),
(459,'任何肤质',234,61),
(460,'正常规格',233,61),
(461,'Estee Lauder/雅诗兰黛',236,61),
(462,'4片 8片',235,61),
(463,'活肤滋润 舒缓肌肤',238,61),
(464,'Estee Lauder/雅诗兰黛 鲜活亮采眼部凝露',230,62),
(465,'Estee Lauder/雅诗兰黛',229,62),
(466,'正常规格',233,62),
(467,'Estee Lauder/雅诗兰黛',236,62),
(468,'15ml',235,62),
(469,'淡化细纹 滋润 补水',238,62),
(470,'中国大陆',239,63),
(471,'男士赋活洗发水',241,63),
(472,'EUSEMIA/欧善美 男士赋活洗发水',230,63),
(473,'所有发质',244,63),
(474,'3年',231,63),
(475,'EUSEMIA/欧善美',229,63),
(476,'男士',242,63),
(477,'常规单品',233,63),
(478,'EUSEMIA/欧善美',236,63),
(479,'古龙香味400ML+400ML（洗发水+沐浴露） 古龙香味600ML+600ML（洗发水+沐浴露）',235,63),
(480,'深度滋养 柔软顺滑 深层修复 深度清洁 滋润营养 改善毛躁 控油 滋润 补水 去屑',238,63),
(481,'中国大陆',239,64),
(482,'丝质柔滑型去屑洗发水500ml*3',241,64),
(483,'海飞丝 丝质柔滑型去屑洗发水500ml*3',230,64),
(484,'所有发质',244,64),
(485,'3年',231,64),
(486,'海飞丝',229,64),
(487,'通用',242,64),
(488,'海飞丝',236,64),
(489,'500ml',235,64),
(490,'柔软顺滑 去屑止痒 深度清洁 改善毛躁 改善头痒 滋润 补水',238,64),
(491,'韩国',239,65),
(492,'睿嫣润膏 舒盈滋养洗发水',230,65),
(493,'所有发质',244,65),
(494,'睿嫣润膏',229,65),
(495,'正常规格',233,65),
(496,'舒盈滋养洗发水',236,65),
(497,'250mL',235,65),
(498,'深层滋润头皮、清洁 改善毛躁 改善头痒 去头屑 滋润',238,65),
(499,'国妆备进字J20161490',237,65),
(500,'中国大陆',239,66),
(501,'男士洗发水沐浴露',241,66),
(502,'George caroll/乔治卡罗尔 男士洗发水沐浴露',230,66),
(503,'3年',231,66),
(504,'George caroll/乔治卡罗尔',229,66),
(505,'通用',242,66),
(506,'常规单品',233,66),
(507,'George caroll/乔治卡罗尔',236,66),
(508,'深度滋养 柔软顺滑 深层修复 其他 深度清洁 滋润营养 控油 滋润 补水 舒缓肌肤 去屑 清洁发丝',238,66),
(509,'中国大陆',239,67),
(510,'男士去屑洗发露套装',241,67),
(511,'CLEAR/清扬',230,67),
(512,'所有发质',244,67),
(513,'CLEAR/清扬',229,67),
(514,'3件',243,67),
(515,'男士',242,67),
(516,'套装',233,67),
(517,'CLEAR/清扬',236,67),
(518,'其他',235,67),
(519,'柔软顺滑 深度清洁 控油 去屑',238,67),
(520,'指针式',49,68),
(521,'圆形',48,68),
(522,'女',45,68),
(523,'classic petite 28mm',43,68),
(524,'石英机芯',44,68),
(525,'Daniel Wellington',42,68),
(526,'不锈钢',47,68),
(527,'是',41,68),
(528,'时尚潮流',46,68),
(529,'Melrose Black Sterling Black Melrose White Sterli',51,68),
(530,'30米生活防水',52,68),
(531,'欧美',53,68),
(532,'2017年秋冬',50,68),
(533,'指针式',49,69),
(534,'圆形',48,69),
(535,'男',45,69),
(536,'手表镜面材质: 矿物强化玻璃镜面品牌: Daniel Wellington型号:',43,69),
(537,'石英机芯',44,69),
(538,'Daniel Wellington',42,69),
(539,'风格: 时尚潮流表带材质:',47,69),
(540,'风格:',46,69),
(541,'Cornwall Black Rosegold Cornwall Black Silver Cor',51,69),
(542,'30米生活防水',52,69),
(543,'欧美',53,69),
(544,'2016年秋冬',50,69),
(545,'形状: 圆形显示方式:',49,70),
(546,'圆形',48,70),
(547,'女',45,70),
(548,'AR1925',43,70),
(549,'石英机芯',44,70),
(550,'EMPORIO ARMANI/阿玛尼',42,70),
(551,'不锈钢',47,70),
(552,'时尚潮流',46,70),
(553,'间金色钢带AR1926 银色钢带Ar1925',51,70),
(554,'30米生活防水',52,70),
(555,'欧美',53,70),
(556,'2015年冬季',50,70),
(557,'指针式',49,71),
(558,'圆形',48,71),
(559,'男',45,71),
(560,'机械机芯-自动机械机芯',44,71),
(561,'Tissot/天梭',42,71),
(562,'表带材质:',47,71),
(563,'是',41,71),
(564,'休闲',46,71),
(565,'钢带罗马黑盘T006.407.11.053.00 钢带数字黑盘T006.407.11.052.00',51,71),
(566,'30米生活防水',52,71),
(567,'瑞士',53,71),
(568,'2009年',50,71),
(569,'指针式',49,72),
(570,'圆形',48,72),
(571,'Tissot/天梭系列: T033.410.11.053.01机芯类型: 石英机芯手表种类:',45,72),
(572,'石英机芯',44,72),
(573,'Tissot/天梭',42,72),
(574,'精钢',47,72),
(575,'是',41,72),
(576,'休闲',46,72),
(577,'【现货】男皮带黑T033.410.16.053.01 男皮带白T033.410.16.013.01',51,72),
(578,'30米生活防水',52,72),
(579,'瑞士',53,72),
(580,'2010年',50,72),
(581,'指针式',49,73),
(582,'圆形',48,73),
(583,'男',45,73),
(584,'616725',43,73),
(585,'机械机芯-自动机械机芯',44,73),
(586,'Rossini/罗西尼',42,73),
(587,'真皮',47,73),
(588,'是',41,73),
(589,'休闲',46,73),
(590,'皮带蓝盘616725G05D 皮带黑盘616725G04C 皮带棕色616725G01G（此花色于',51,73),
(591,'50m',52,73),
(592,'国内',53,73),
(593,'2016年春夏',50,73),
(594,'指针式',49,74),
(595,'圆形',48,74),
(596,'男',45,74),
(597,'5956',43,74),
(598,'机械机芯-自动机械机芯',44,74),
(599,'Tian Wang/天王',42,74),
(600,'真皮',47,74),
(601,'时尚',46,74),
(602,'灰面黑带 白面棕带 蓝盘蓝带 黑盘棕带',51,74),
(603,'50m',52,74),
(604,'国内',53,74),
(605,'2017年秋冬',50,74),
(606,'指针式',49,75),
(607,'圆形',48,75),
(608,'男',45,75),
(609,'机械机芯-自动机械机芯',44,75),
(610,'Seagull/海鸥表',42,75),
(611,'其他',47,75),
(612,'复古',46,75),
(613,'黑盘金圈白框黑带 白盘黑圈白框黑带 白盘白圈白框黑带 1',51,75),
(614,'50m',52,75),
(615,'国内',53,75),
(616,'男',45,76),
(617,'DW-5600BB石',43,76),
(618,'石英机芯',44,76),
(619,'Casio/卡西欧',42,76),
(620,'其他',47,76),
(621,'运动',46,76),
(622,'DW-5600BB-1PR DW-5600E-1VPF G-5600E-1PR DW-5600HR',51,76),
(623,'200m',52,76),
(624,'日本',53,76),
(625,'男',45,77),
(626,'GA-700-7AP',43,77),
(627,'石英机芯',44,77),
(628,'Casio/卡西欧',42,77),
(629,'其他',47,77),
(630,'是',41,77),
(631,'时尚潮流',46,77),
(632,'GA-700-2APR GA-700-1APR GA-700-1BPR GA-700-4APR G',51,77),
(633,'200m',52,77),
(634,'日本',53,77),
(635,'指针式',49,78),
(636,'圆形',48,78),
(637,'男',45,78),
(638,'MTP-1374D-7A',43,78),
(639,'石英机芯',44,78),
(640,'Casio/卡西欧',42,78),
(641,'金属',47,78),
(642,'时尚潮流',46,78),
(643,'MTP-1374L-1A MTP-1374D-1A MTP-1374D-7A MTP-1374D-',51,78),
(644,'50m',52,78),
(645,'日本',53,78),
(646,'2013年',50,78),
(647,'男',45,79),
(648,'DW-5735',43,79),
(649,'石英机芯',44,79),
(650,'Casio/卡西欧',42,79),
(651,'其他',47,79),
(652,'时尚潮流',46,79),
(653,'DW-5035D-1BPR（预计5月1日左右发货） DW-5735D-1BPR（预计5月10日左右',51,79),
(654,'200m',52,79),
(655,'日本',53,79),
(656,'2018年春季',50,79),
(657,'双显式',49,80),
(658,'圆形',48,80),
(659,'SS022990000',43,80),
(660,'智能机芯',44,80),
(661,'Suunto/颂拓',42,80),
(662,'精钢',47,80),
(663,'是',41,80),
(664,'极速光电心率黑色 极速光电心率蓝色 极速光电心率樱花粉',51,80),
(665,'100m',52,80),
(666,'芬兰',53,80),
(667,'双显式',49,81),
(668,'圆形',48,81),
(669,'军拓铁腕5',43,81),
(670,'智能机芯',44,81),
(671,'JTOUR/军拓',42,81),
(672,'不锈钢',47,81),
(673,'咖金黑 碳素黑 铂金白 咖金白',51,81),
(674,'50m',52,81),
(675,'国内',53,81),
(676,'2018年春季',50,81),
(677,'597242CZ-1',43,82),
(678,'pandora/潘多拉',42,82),
(679,'16.5 18.5 20.5',51,82),
(680,'5172560',43,83),
(681,'Swarovski/施华洛世奇',42,83),
(682,'是',41,83),
(683,'Bolon/暴龙1',42,84),
(684,'BL7021C11 BL7021C60 BL7021D10 BL7021D70 BL7021D92',51,84),
(685,'厦门',53,84),
(686,'青年',21,28),
(687,'纯色',19,28),
(688,'立领',18,28),
(689,'时尚都市',15,28),
(690,'中长款',16,28),
(691,'棉100%',13,28),
(692,'标准',14,28),
(693,'青年',21,37),
(694,'字母数字',19,37),
(695,'短袖',17,37),
(696,'圆领',18,37),
(697,'青春流行',15,37),
(698,'棉96.9% 聚氨酯弹性纤维(氨纶)3.1%',13,37),
(699,'标准',14,37),
(700,'青年',21,20),
(701,'纯色',19,20),
(702,'短袖',17,20),
(703,'尖领（常规）',18,20),
(704,'其他',15,20),
(705,'聚酯纤维58.5% 棉41.5%',13,20),
(706,'标准',14,20),
(707,'短袖',17,33),
(708,'圆领',18,33),
(709,'青春流行',15,33),
(710,'棉100%',13,33),
(711,'标准',14,33),
(712,'恶趣味去',22,86),
(713,'厄齐尔',23,86),
(714,'恶趣味群',13,86),
(715,'额温枪二',14,86),
(716,'厄齐尔',15,86),
(717,'厄齐尔',16,86),
(718,'却额',17,86),
(719,'恶趣味',18,86),
(720,'厄齐尔群',19,86),
(721,'缺钱',20,86),
(722,'王企鹅去',21,86);

/*Table structure for table `review` */

DROP TABLE IF EXISTS `review`;

CREATE TABLE `review` (
  `review_id` int(11) NOT NULL AUTO_INCREMENT,
  `review_content` mediumtext NOT NULL COMMENT '内容',
  `review_createdate` datetime NOT NULL COMMENT '创建日期',
  `review_user_id` int(11) NOT NULL COMMENT '关联用户id',
  `review_product_id` int(11) NOT NULL COMMENT '关联产品id',
  `review_orderItem_id` int(11) NOT NULL COMMENT '关联订单详细id',
  PRIMARY KEY (`review_id`) USING BTREE,
  KEY `review_user_id` (`review_user_id`) USING BTREE,
  KEY `review_product_id` (`review_product_id`) USING BTREE,
  KEY `review_orderItem_id` (`review_orderItem_id`) USING BTREE,
  CONSTRAINT `review_ibfk_1` FOREIGN KEY (`review_user_id`) REFERENCES `user` (`user_id`),
  CONSTRAINT `review_ibfk_2` FOREIGN KEY (`review_product_id`) REFERENCES `product` (`product_id`),
  CONSTRAINT `review_ibfk_3` FOREIGN KEY (`review_orderItem_id`) REFERENCES `productorderitem` (`productorderitem_id`)
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='评论表';

/*Data for the table `review` */

insert  into `review`(`review_id`,`review_content`,`review_createdate`,`review_user_id`,`review_product_id`,`review_orderItem_id`) values 
(1,'测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试','2018-05-14 13:10:50',1,37,55),
(2,'手机很好！！！！！！！！！！拍照很清晰','2018-05-14 00:00:00',1,42,53),
(3,'质量很好','2018-05-15 00:00:00',1,33,61),
(4,'dda','2018-05-15 00:00:00',1,1,64),
(5,'很好','2018-05-23 00:00:00',1,41,65),
(6,'非常好看！质量也不错！','2018-05-24 00:00:00',6,14,82),
(7,'很早之前就加入购物车了！买回来后果然没失望！质量很好，穿着也很舒服！','2018-05-24 00:00:00',6,41,83),
(8,'衣服很好看，发货很及时！','2018-05-24 00:00:00',7,1,85),
(9,'衣服很好看！模特也是哦(笑','2018-05-24 00:00:00',1,1,86),
(10,'衣服很时尚，质量也挺好','2018-05-24 00:00:00',7,1,88),
(11,'给姐姐买的衣服，姐姐很喜欢！质量很不错','2018-05-24 00:00:00',1,1,87),
(12,'发的衣服有点大了，其他还行，好评给卖家一个鼓励！','2018-05-24 00:00:00',6,1,90),
(13,'发货过程有点慢，衣服还行吧！','2018-05-24 00:00:00',7,1,89),
(14,'衣服特别看好，穿上去特潮，男票超级喜欢\r\n好评！','2018-05-25 00:00:00',8,1,93),
(15,'衣服有点大','2018-05-25 00:00:00',9,1,92),
(17,'没有卖家秀吗。。。。','2018-05-25 00:00:00',6,1,95),
(18,'给姑姑买的，姑姑很喜欢！掌柜也很细心！','2018-05-25 00:00:00',6,1,96),
(19,'前置两千万柔光双摄，照亮你的美！！！','2018-05-25 00:00:00',6,42,98),
(20,'手机很好用！给买家点赞','2018-05-25 00:00:00',9,42,100),
(21,'衣服贼好看，和女朋友买的情路装\r\n这下我们也是社会人了  哈哈哈哈哈\r\n抖音真神奇\r\n小猪佩奇身上纹，掌声送给社会人','2018-05-25 00:00:00',8,41,97),
(22,'手机刚买回家，很流畅，比R9好多了','2018-05-25 00:00:00',1,42,101),
(23,'前置柔光两千万照亮你的美！\r\n','2018-05-25 00:00:00',9,42,102),
(24,'手机用了几天有点卡，还是推荐小米','2018-05-25 00:00:00',9,42,104),
(25,'隔壁小米手机便宜又好用','2018-05-25 00:00:00',3,42,106),
(26,'刚到手，感觉很好用比一般手机要好，打算再买一只备用','2018-05-25 00:00:00',10,42,103),
(27,'超级好用  比之前买的各种手机都好用\r\n给爸妈和哥哥各买了一个    但是自己还是用Iphone\r\n不要问我为什么   OPPO对于年轻人来说性价比太差了\r\n但是家人喜欢    家人全是O粉\r\n好评','2018-05-25 00:00:00',8,42,105),
(28,'买家未做评价！','2018-05-25 00:00:00',3,41,107),
(29,'不说了，给七大姑八大姨都买了一件','2018-05-25 00:00:00',3,1,108),
(30,'公司年会，大家人手一个R11','2018-05-25 00:00:00',9,42,109),
(31,'温州最大皮革厂批发','2018-05-25 00:00:00',3,1,110),
(32,'对这款手机爱不释手，打算多买点给员工拿来当奖励','2018-05-25 00:00:00',10,42,111),
(33,'公司发福利人手一台\r\n不要问我为什么 当老板的就是这么任性','2018-05-25 00:00:00',8,42,112),
(34,'手机性能不错，比其他的还要好，再给亲戚买一些人手一只R11','2018-05-25 00:00:00',10,42,113),
(35,'五星好评！模特姐姐很好看','2018-05-25 00:00:00',11,34,115),
(36,'超级稀饭希望商家越做越好  好评好评','2018-05-25 00:00:00',12,1,116),
(37,'和女朋友一起穿，那就非常社会了！\r\n','2018-05-25 00:00:00',11,41,117),
(38,'自从买了这个扫地机器人，妈妈再也不担心我的卫生了','2018-05-25 00:00:00',11,46,118),
(39,'特步，非一般的感觉！','2018-05-25 00:00:00',11,30,120),
(40,'最爱小白鞋','2018-05-25 00:00:00',3,30,121),
(41,'买来训练的 踩屎感不明显  毕竟470的鞋子就那样吧   中评','2018-05-25 00:00:00',8,30,122),
(42,'耐特，非人般的感觉','2018-05-25 00:00:00',10,30,119),
(43,'买来送女友的生日礼物  她超级喜欢  给我冰火了一波  舒服~','2018-05-25 00:00:00',12,68,123),
(44,'鞋子不错，穿的很舒适','2018-05-25 00:00:00',13,30,124),
(45,'好评','2018-05-25 00:00:00',7,31,127),
(46,'衣服感觉不错，手感很好，重要的是穿很舒适\r\n','2018-05-25 00:00:00',10,8,130),
(47,'挺好看的就是价格太贵了','2018-05-18 00:00:00',12,2,143),
(48,'66666666666666666666666666666666666666666666666666666（听说好评返现）','2018-05-18 00:00:00',12,1,162),
(49,'好看','2018-05-19 00:00:00',14,1,173),
(50,'买来送爸爸的  老爸的荣耀8用了半年了  该给他换一个了','2018-05-19 00:00:00',14,45,175),
(51,'说实话OPPO手机是真的垃圾 真的不如小米\r\n同样的价格  小米能超OPPO几千里','2018-05-20 00:00:00',14,42,189),
(52,'衣服质量特别好  简直就是物美价廉','2018-05-20 00:00:00',14,28,191),
(53,'买来做情侣装  是不是因为太火了 回头率超高  还特别便宜','2018-05-20 00:00:00',14,41,194),
(54,'卖了两个  自己一个麻麻一个 超级好用。好评！','2018-05-21 00:00:00',14,48,203),
(55,'买了两个做情侣表感觉还可以','2018-05-21 00:00:00',8,70,205),
(56,'一下子买了五个  送朋友  感觉特别高大上','2018-05-21 00:00:00',8,43,208),
(57,'。','2018-05-21 00:00:00',8,84,210),
(58,'买了四个送客户 感觉这单生意可以拿下','2018-05-22 00:00:00',15,71,215),
(59,'6','2018-05-22 00:00:00',15,23,216),
(60,'老O粉了非常喜欢OPPO','2018-05-22 00:00:00',15,42,217),
(61,'质量超乎我的想象','2018-05-23 00:00:00',8,16,218),
(62,'买来奖励员工  性价比比较高的手机把  一直都很信赖小米','2018-05-23 00:00:00',8,44,219),
(63,'超级喜欢','2018-05-23 00:00:00',16,31,222),
(64,'就是有点小贵','2018-05-23 00:00:00',16,25,223),
(65,'特别喜欢 显腿长','2018-05-23 00:00:00',16,15,224),
(66,'222','2018-05-25 00:00:00',16,41,254),
(67,'手机很好用','2018-05-25 00:00:00',18,42,262),
(68,'111','2018-05-25 00:00:00',18,44,264),
(73,'非常不错','2018-05-23 00:00:00',1,11,277),
(74,'手机很好','2018-05-23 00:00:00',1,42,279),
(75,'111111111','2018-05-23 00:00:00',1,42,284);

/*Table structure for table `user` */

DROP TABLE IF EXISTS `user`;

CREATE TABLE `user` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_name` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '用户名',
  `user_nickname` varchar(50) NOT NULL COMMENT '昵称',
  `user_password` varchar(50) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '密码',
  `user_realname` varchar(20) DEFAULT NULL COMMENT '姓名',
  `user_gender` tinyint(1) NOT NULL COMMENT '性别',
  `user_birthday` date NOT NULL COMMENT '出生日期',
  `user_address` char(6) NOT NULL COMMENT '所在地地址',
  `user_homeplace` char(6) NOT NULL COMMENT '家乡',
  `user_profile_picture_src` varchar(100) DEFAULT NULL COMMENT '用户头像',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标识(1删除 0未删除）',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `un_user_name` (`user_name`) USING BTREE,
  KEY `user_address` (`user_address`) USING BTREE,
  KEY `user_homeplace` (`user_homeplace`) USING BTREE,
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`user_address`) REFERENCES `address` (`address_areaId`),
  CONSTRAINT `user_ibfk_2` FOREIGN KEY (`user_homeplace`) REFERENCES `address` (`address_areaId`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户表';

/*Data for the table `user` */

insert  into `user`(`user_id`,`user_name`,`user_nickname`,`user_password`,`user_realname`,`user_gender`,`user_birthday`,`user_address`,`user_homeplace`,`user_profile_picture_src`,`del_flag`) values 
(1,'admin','MRJIANG','123456','江超',0,'2013-12-01','610112','110100','8ba4edd6-0c92-46be-b9c4-65fa4883426b.jpg',0),
(3,'MRJIANG','MRJIANG','cc123','MRJIANG',0,'2018-05-11','110105','130000','5835a2b3-5f44-4423-812e-1ad5cecb3020.png',0),
(6,'a1209577113','如有巧合丶','cc123.0','',0,'2018-05-07','230102','130000','',0),
(7,'李浩','李','cc123.0','李浩',0,'1997-06-22','110109','130000','fd834bfc-da2c-4dce-907e-1df54415b0fd.jpg',0),
(8,'聚散又依依','聚散又依依','a1392432919','邓云涛',0,'1997-04-05','110101','130000','78659273-05ec-4089-b94c-789f61ecdbf6.jpg',0),
(9,'红豆豆','豆豆','li19970622','红豆豆',1,'2000-05-12','510923','130000','6ffe783e-bbb1-4aa2-8207-b3fda65bcebc.jpg',0),
(10,'兰文斌','停车坐爱枫林晚','ln221397','老男孩',0,'2018-05-10','511922','130000','599e3499-da42-494c-a1ab-85d0548a857a.jpg',0),
(11,'明明','小明','li19970622','红豆豆',0,'2018-05-01','110101','130000','025639f4-537a-4bdc-a9fb-279e9dfac1f8.jpg',0),
(12,'霜叶红于二月花','王九日','a666','',0,'1999-10-01','320583','130000','',0),
(13,'那倒是','加尔奈特巴斯塔','ln221379','',0,'2018-05-04','610113','130000','',0),
(14,'王999','王恒','a999999999','王蓝',0,'1995-01-08','230112','130000','',0),
(15,'我变强了','李有生','a789','',0,'1980-09-04','370102','130000','',0),
(16,'柯6','我的兄弟叫顺溜','hui6','柯旭',1,'2001-01-07','420102','130000','',0),
(17,'bao','可乐','123456a','小猫咪',0,'2002-02-16','610113','130000','cbb05079-5cc9-42a6-a76a-4890418f5121.jpg',0),
(18,'西安','西安','cc123.0','西安',0,'2018-05-03','110101','130000','',0),
(19,'b120','人员','cc123.0','其他',0,'2018-05-01','110101','130000','29baa916-f0e7-441c-bb0a-45dee0cdebb7.jpg',0),
(20,'c120','aaa','cc123.0','',0,'2018-05-03','110101','130000','',0),
(21,'d120','任意切换','cc123.0','',0,'2018-05-09','110101','130000','',0);

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
