package com.xq.tmall.controller.fore;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import com.baomidou.mybatisplus.toolkit.IdWorker;
import com.xq.tmall.controller.BaseController;
import com.xq.tmall.entity.ApiVerCodeResp;
import com.xq.tmall.entity.Result;
import com.xq.tmall.entity.User;
import com.xq.tmall.service.UserService;
import com.xq.tmall.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 前台天猫-登录API
 */
@Api(tags = "前台天猫-登录API")
@RestController
@RequiredArgsConstructor
public class ForeLoginController extends BaseController {
    private final UserService userService;

    // 用户登录-API接口
    @ApiOperation(value = "用户登录", notes = "用户登录，返回JWT Token")
    @PostMapping(value = "api/login", produces = "application/json;charset=utf-8")
    public Result<Map<String, Object>> login(
            @RequestParam String username,
            @RequestParam String password,
            @RequestParam(required = false) String verifyCode) {
        try {
            // 验证用户名和密码
            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }

            // 用户验证登录
            User user = userService.login(username.trim(), password.trim());

            if (user == null) {
                return Result.error("用户名或密码错误");
            }

            // 生成JWT Token
            String token = JwtUtil.generateToken(user.getUser_id(), user.getUser_name());

            // 构建响应数据
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("token", token);
            responseData.put("user", user);

            return Result.success("登录成功", responseData);
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    // 用户退出登录-API接口
    @ApiOperation(value = "用户退出登录", notes = "用户退出登录")
    @PostMapping(value = "api/logout", produces = "application/json;charset=utf-8")
    public Result<String> logout() {
        try {
            // JWT是无状态的，客户端删除Token即可实现退出
            // 这里可以添加Token黑名单机制（如果需要的话）
            return Result.success("退出登录成功");
        } catch (Exception e) {
            return Result.error("退出登录失败：" + e.getMessage());
        }
    }

    // 获取登录验证码-API接口
    @ApiOperation(value = "获取登录验证码", notes = "获取登录验证码")
    @GetMapping(value = "api/login/code", produces = "application/json;charset=utf-8")
    public Result<ApiVerCodeResp> getVerCode() {
        try {
            CircleCaptcha captcha = CaptchaUtil.createCircleCaptcha(140, 38, 4, 20);
            ApiVerCodeResp verCodeResp = new ApiVerCodeResp(
                String.valueOf(IdWorker.getId()),
                captcha.getImageBase64Data(),
                captcha.getCode().toLowerCase()
            );
            return Result.success("获取验证码成功", verCodeResp);
        } catch (Exception e) {
            return Result.error("获取验证码失败：" + e.getMessage());
        }
    }
}
