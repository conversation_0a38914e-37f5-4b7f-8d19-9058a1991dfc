.header {
    width: 1230px;
    margin: 0 auto;
    height: 96px;
}

.header > #mall<PERSON>ogo {
    float: left;
    padding-top: 28px;
    width: 280px;
    height: 64px;
    line-height: 64px;
    position: relative;
}

#mallLogo > a {
    position: relative;
    display: block;
    width: 190px;
    height: 30px;
    overflow: hidden;
}

#mallLogo img {
    width: 190px;
    height: 28px;
    cursor: pointer;
    vertical-align: top;
}

.header > .shopSearchHeader {
    float: right;
    overflow: hidden;
    width: 597px;
    padding-top: 25px;
}

.shopSearchHeader > form {
    border: solid #ff0036;
    border-width: 3px 0 3px 3px;
}

.shopSearchHeader > form > .shopSearchInput {
    position: relative;
    height: 30px;
    font-family: Arial, serif;
}

input::-webkit-input-placeholder { /* WebKit browsers*/
    font-weight: normal;
}

input:-moz-placeholder { /* Mozilla Firefox 4 to 18*/
    font-weight: normal;
}

input::-moz-placeholder { /* Mozilla Firefox 19+*/
    font-weight: normal;
}

input:-ms-input-placeholder { /* Internet Explorer 10+*/
    font-weight: normal;
}

.shopSearchInput > .searchInput {
    font-size: 12px;
    color: #000;
    width: 496px;
    height: 20px;
    line-height: 20px;
    padding: 5px 3px 5px 5px;
    border: none;
    font-weight: 900;
    outline: none;
    float: left;
}

.shopSearchInput > .searchBtn {
    width: 90px;
    height: 30px;
    font-size: 16px;
    cursor: pointer;
    color: #ffffff;
    background-color: #FF0036;
    overflow: hidden;
    border: 0;
    font-family: Arial, serif;
    float: left;
}

.headerLayout {
    width: 1230px;
    margin: 0 auto;
}

.headerLayout > .headerContext {
    position: relative;
    height: 92px;
}

.headerContext > .header-extra {
    margin: 0 auto;
    width: 1200px;
    padding: 20px 0 0;
    overflow: hidden;
    zoom: 1;
}

.header-extra > li {
    width: 240px;
    display: block;
    float: left;
    text-align: center;
    margin: 0;
    padding: 0;
    list-style: none;
    font-family: Arial, serif;
    font-size: 12px;
}

.header-extra > li > .step-name {
    padding: 3px 0;
    font-weight: bold;
    color: #888;
}

.header-extra > li > .step-no_first {
    height: 34px;
    line-height: 34px;
    color: #FFFFFF;
    font-size: 18px;
    background: url(../../images/fore/WebsiteImage/T1Usl8FnRfXXcVlxZa-734-340.png) no-repeat 50% -204px;
}

.header-extra > li > .step-no {
    height: 34px;
    line-height: 34px;
    color: #FFFFFF;
    font-size: 18px;
    background: url(../../images/fore/WebsiteImage/T1Usl8FnRfXXcVlxZa-734-340.png) no-repeat 50% -102px;
}

.header-extra > li > .step-no.step-no-select {
    background: url(../../images/fore/WebsiteImage/T1Usl8FnRfXXcVlxZa-734-340.png) no-repeat 50% -170px;
}

.header-extra > li > .step-no_last {
    height: 34px;
    line-height: 34px;
    color: #FFFFFF;
    font-size: 18px;
    background: url(../../images/fore/WebsiteImage/T1Usl8FnRfXXcVlxZa-734-340.png) no-repeat 50% -136px;
}

.header-extra > li > .step-no_last.step-no-select {
    background: url(../../images/fore/WebsiteImage/T1Usl8FnRfXXcVlxZa-734-340.png) no-repeat 50% -68px;
}

.header-extra > li > .step-time {
    color: #999;
    padding: 8px 0;
    text-align: center;
}

.content {
    width: 1230px;
    margin: 100px auto 60px auto;
    min-height: 400px;
    color: #666;
}

.content > h1 {
    border-bottom: 1px solid #adc8e6;
    font-size: 16px;
    color: #111111;
    padding-bottom: 10px;
    margin-bottom: 10px;
    font-family: Arial, serif;
    font-weight: bold;
}

.content > .order_info {
    padding: 10px 20px;
}

.order_info > h2 {
    font-family: Arial, serif;
    line-height: 25px;
    color: #333;
    font-weight: 700;
    font-size: 14px;
}

.order_info > .table_order_orderItem {
    width: 100%;
}

.table_order_orderItem > thead th {
    text-align: center;
    font-family: Arial, serif;
    font-weight: normal;
    font-size: 12px;
    border-bottom: 3px solid #b2d1ff;
}

.table_order_orderItem > tbody td {
    font-family: Arial, serif;
    font-weight: normal;
    font-size: 12px;
}

.table_order_orderItem > tbody > tr.tr_shop {
    border-bottom: 1px dotted #80b2ff;
}

.tr_shop > td {
    padding-top: 25px;
    height: 22px;
}

.table_order_orderItem > tbody > tr.tr_product_info {
    border-bottom: 1px dotted #ddd;
}

.table_order_orderItem > tbody > tr.order-ft {
    text-align: right;
}

.order-ft > td {
    padding: 8px 10px;
    line-height: 130%;
    text-align: center;
    overflow: hidden;
}

.order-ft > td > div {
    text-align: right;
    float: right;
    margin-top: 20px;
    color: black;
}

.order-ft > td > div > strong {
    font-size: 18px;
    font-weight: 400;
    color: #c00;
}

.tr_product_info > td {
    text-align: center;
    padding: 10px 0;
}

.tr_product_info > td:first-child {
    text-align: left;
}

.tr_product_info > td > img {
    padding-left: 10px;
}

.tr_product_info > td > span.span_product_name > a {
    display: inline-block;
    padding-left: 10px;
}

.misc-info td {
    padding: 5px 10px;
    line-height: 25px;
    color: black;
}

.set-row > td {
    border-bottom: 1px solid #ddd;
}

.misc-info td > .info_label {
    display: block;
    float: left;
    width: 90px;
    text-align: right;
    margin-right: 10px;
}

.order_info > .order-dashboard {
    margin-top: 10px;
    border: 1px solid #e5e5e5;
}

.order-dashboard > .bd {
    padding: 26px 100px;
    border: 1px solid #F58B0F;
}

.bd > ul > li {
    list-style: disc;
    color: #F00000;
    font-weight: 700;
    font-size: 14px;
    line-height: 28px;
    font-family: Arial, serif;
}

.bd > ul > li.message {
    color: black;
    font-weight: normal;
}

.bd > a {
    box-sizing: border-box;
    height: 31px;
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    font-size: 14px;
    text-decoration: none;
    background: url(../../images/fore/WebsiteImage/1eOlqqn1qX.png) 0 -163px no-repeat;
    padding: 0 16px;
    border-radius: 3px;
    line-height: 31px;
    color: white;
    font-family: tohoma, serif;
    font-weight: bold;
    margin-top: 20px;
}