<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "mybatis-3-mapper.dtd">
<mapper namespace="com.xq.tmall.dao.CategoryMapper">
    <insert id="insertOne" parameterType="com.xq.tmall.entity.Category">
        INSERT category (category_name,category_image_src)
            VALUES (
            #{category.category_name},
            #{category.category_image_src})
    </insert>
    <update id="updateOne" parameterType="com.xq.tmall.entity.Category">
        UPDATE category
        <set>
            <if test="category.category_name != null">category_name = #{category.category_name},</if>
            <if test="category.category_image_src != null">category_image_src = #{category.category_image_src},</if>
            <if test="category.del_flag != null">del_flag = #{category.del_flag}</if>
        </set>
        <where>
            category_id = #{category.category_id}
        </where>
    </update>
    <select id="selectCategoryList" resultType="com.xq.tmall.entity.Category">
        SELECT category_id,category_name,category_image_src FROM category
        <where>
            del_flag=0
            <if test="category_name != null"> and category_name LIKE concat('%',#{category_name},'%')</if>
        </where>
        <if test="pageUtil != null">
            LIMIT #{pageUtil.pageStart},#{pageUtil.count}
        </if>
    </select>
    <select id="selectOne" parameterType="java.lang.Integer" resultType="com.xq.tmall.entity.Category">
        SELECT category_id,category_name,category_image_src FROM category
        <where>
            del_flag=0
            and category_id = #{category_id}
        </where>
    </select>
    <select id="selectTotal" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM category
        <where>
            del_flag=0
            <if test="category_name != null">and category_name LIKE concat('%',#{category_name},'%')</if>
        </where>
    </select>
</mapper>