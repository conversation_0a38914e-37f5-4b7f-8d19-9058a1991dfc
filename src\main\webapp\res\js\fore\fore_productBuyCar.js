/**
 * 购物车页面JavaScript - 使用AJAX加载数据
 */
$(function () {
    // 初始化页面
    CartPage.init();

    //搜索框验证
    $('form').submit(function () {
        if ($(this).find("input[name='product_name']").val() === "") {
            alert("请输入关键字！");
            return false;
        }
    });
    $(".tbody_checkbox>.cbx_select").click(function () {
        const obj = $(this).parents("tr.orderItem_info").toggleClass("orderItem_selected");
        sumPrice();
    });
    $("#cbx_select_all,#J_SelectAllCbx2").click(function () {
        const yn = $(this).prop("checked");
        const obj = $("tr.orderItem_info");
        if (!yn) {
            $(".tbody_checkbox>.cbx_select").prop("checked", false);
            sumPrice();
            obj.removeClass("orderItem_selected");
            $("#J_Go").removeClass("selected");
        } else {
            $(".tbody_checkbox>.cbx_select").prop("checked", true);
            sumPrice();
            obj.addClass("orderItem_selected");
            $("#J_Go").addClass("selected");
        }
    });
    $(".item_amount>input[type=text]").bind('input propertychange', function () {
        let number = $(this).val();
        if (isNaN(number) || $.trim(number) === "") {
            $(this).val(1);
            $(this).prev("a").addClass("no_minus");
            return;
        }
        if (parseInt(number) >= 500) {
            $(this).val(500);
            number = 500;
            $(this).next("a").addClass("no_minus");
        } else if (parseInt(number) > 1 && parseInt(number) < 500) {
            $(this).prev("a").removeClass("no_minus");
            $(this).next("a").removeClass("no_minus");
        } else if (parseInt(number) <= 1) {
            $(this).val(1);
            number = 1;
            $(this).prev("a").addClass("no_minus");
        }
        let price = $(this).parents("tr").find(".orderItem_product_price").text();
        price = parseFloat(price.substring(1));
        const price_sum = parseFloat(price * parseInt(number));
        $(this).parents("tr").find(".orderItem_product_realPrice").text("￥" + price_sum.toFixed(1));
        sumPrice();
    });
});

function up(obj) {
    obj = $(obj);
    const number = obj.next("input");
    let value = parseInt(number.val());
    if (value > 1) {
        obj.removeClass("no_minus");
    } else {
        obj.addClass("no_minus");
    }
    if (obj.hasClass("no_minus")) {
        return true;
    } else {
        if (isNaN(number.val()) || $.trim(number.val()) === "" || parseInt(number.val()) <= 1) {
            number.val("1");
            obj.addClass("no_minus");
            return true;
        }
        value--;
        if (value < 500) {
            number.next("a").removeClass("no_minus");
        } else {
            number.next("a").addClass("no_minus");
        }
        let price = obj.parents("tr").find(".orderItem_product_price").text();
        price = parseFloat(price.substring(1));
        const price_sum = parseFloat(price * value);
        number.val(value);
        obj.parents("tr").find(".orderItem_product_realPrice").text("￥" + price_sum.toFixed(1));
        if (value === 1) {
            obj.addClass("no_minus");
        }
        sumPrice();
    }
}

function down(obj) {
    obj = $(obj);
    const number = obj.prev("input");
    let value = parseInt(number.val());
    if (value < 500) {
        obj.removeClass("no_minus");
    } else {
        obj.addClass("no_minus");
    }
    if (obj.hasClass("no_minus")) {
        return true;
    } else {
        if (isNaN(number.val()) || $.trim(number.val()) === "" || parseInt(number.val()) < 1) {
            number.val("1");
            return true;
        }
        obj.prevAll(".J_Minus").removeClass("no_minus");
        value++;
        let price = obj.parents("tr").find(".orderItem_product_price").text();
        price = parseFloat(price.substring(1));
        const price_sum = parseFloat(price * value);
        obj.parents("tr").find(".orderItem_product_realPrice").text("￥" + price_sum.toFixed(1));
        number.val(value);
        sumPrice();
    }
}

function sumPrice() {
    let price_sum = 0.00;
    const obj = $("input.cbx_select:checked").parents("tr.orderItem_info");
    obj.each(function () {
        price_sum += parseFloat($(this).find(".orderItem_product_realPrice").text().substring(1));
    });
    $(".total_value").text(price_sum.toFixed(2));

    if (obj.length > 0) {
        $("#J_Go").addClass("selected");
    } else {
        $("#J_Go").removeClass("selected");
    }
    $("#J_SelectedItemsCount").text(obj.length);
}

function create(obj) {
    obj = $(obj);
    if (!obj.hasClass("selected")) {
        return true;
    }
    const orderItemMap = {};
    const tr = $("input.cbx_select:checked").parents("tr.orderItem_info");
    tr.each(function () {
        const key = $(this).find(".input_orderItem").attr("name");
        orderItemMap[key] = $(this).find(".item_amount").children("input").val();
    });
    $.ajax({
        url: "/tmall/orderItem",
        type: "PUT",
        data: {
            "orderItemMap": JSON.stringify(orderItemMap)
        },
        traditional: true,
        success: function (data) {
            if (data.success) {
                location.href = "/tmall/order/create/byCart?order_item_list=" + data.orderItemIDArray;
                return true;
            } else {
                alert("购物车商品结算异常，请稍候再试！");
                location.href = "/tmall/cart";
            }
        },
        beforeSend: function () {
        },
        error: function () {
            alert("购物车商品结算异常，请稍候再试！");
            location.href = "/tmall/cart";
        }
    });
}

const CartPage = {
    // 当前数据
    currentData: null,

    // 初始化
    init: function() {
        this.loadCartData();
        this.bindEvents();
    },

    // 加载购物车数据
    loadCartData: function() {
        const self = this;

        // 检查是否有JWT Token
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        if (!token) {
            // 没有token，跳转到登录页
            window.location.href = '/tmall/login';
            return;
        }

        $.ajax({
            url: '/tmall/api/cart',
            type: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            dataType: 'json',
            success: function(response) {
                $('#loadingIndicator').hide();

                if (response.code === 200 && response.data) {
                    self.currentData = response.data;
                    if (response.data.orderItemList && response.data.orderItemList.length > 0) {
                        self.renderCartContent(response.data);
                    } else {
                        self.showEmptyCart();
                    }
                } else {
                    if (response.code === 401 || (response.message && response.message.includes('未登录'))) {
                        // 用户未登录或token过期，跳转到登录页
                        localStorage.removeItem('token');
                        sessionStorage.removeItem('token');
                        window.location.href = '/tmall/login';
                    } else {
                        alert(response.message || '获取购物车数据失败');
                        self.showEmptyCart();
                    }
                }
            },
            error: function(xhr, status, error) {
                $('#loadingIndicator').hide();
                console.error('加载购物车数据失败:', error);
                if (xhr.status === 401) {
                    // 用户未登录或token过期
                    localStorage.removeItem('token');
                    sessionStorage.removeItem('token');
                    window.location.href = '/tmall/login';
                } else {
                    alert('网络错误，请稍后重试');
                    self.showEmptyCart();
                }
            }
        });
    },

    // 渲染购物车内容
    renderCartContent: function(data) {
        const html = this.generateCartHTML(data);
        $('#cartContent').html(html).show();
        this.bindCartEvents();
    },

    // 显示空购物车
    showEmptyCart: function() {
        $('#emptyCart').show();
    },

    // 生成购物车HTML
    generateCartHTML: function(data) {
        let html = `
            <div id="J_FilterBar">
                <ul id="J_CartSwitch">
                    <li>
                        <a href="/tmall/cart" class="J_MakePoint">
                            <em>全部商品</em>
                            <span class="number">${data.orderItemTotal || 0}</span>
                        </a>
                    </li>
                </ul>
                <div class="cart-sum">
                    <span class="pay-text">已选商品（不含运费）</span>
                    <strong class="price"><em id="J_SmallTotal"><span class="total-symbol">&nbsp;</span>0.00</em></strong>
                    <a id="J_SmallSubmit" class="submit-btn submit-btn-disabled">结&nbsp;算</a>
                </div>
                <div class="wrap-line">
                    <div class="floater"></div>
                </div>
            </div>
            <table id="J_CartMain">
                <thead>
                <tr>
                    <th class="selectAll_th"><input type="checkbox" class="cbx_select" id="cbx_select_all"><label for="cbx_select_all">全选</label></th>
                    <th width="474px" class="productInfo_th"><span>商品信息</span></th>
                    <th width="120px"><span>单价</span></th>
                    <th width="120px"><span>数量</span></th>
                    <th width="120px"><span>金额</span></th>
                    <th width="84px"><span>操作</span></th>
                    <th hidden>ID</th>
                </tr>
                </thead>
                <tbody>
        `;

        // 渲染购物车商品
        if (data.orderItemList && data.orderItemList.length > 0) {
            data.orderItemList.forEach(function(orderItem) {
                const product = orderItem.productOrderItem_product;
                const unitPrice = (orderItem.productOrderItem_price / orderItem.productOrderItem_number).toFixed(2);
                const imageSrc = product.singleProductImageList && product.singleProductImageList.length > 0
                    ? product.singleProductImageList[0].productImage_src
                    : 'default.jpg';

                html += `
                    <tr class="orderItem_category">
                        <td colspan="6">
                            <span class="shop_logo"></span>
                            <span class="category_shop">店铺：海涛${product.product_category.category_name}旗舰店</span>
                        </td>
                    </tr>
                    <tr class="orderItem_info">
                        <td class="tbody_checkbox">
                            <input type="checkbox" class="cbx_select" id="cbx_orderItem_select_${orderItem.productOrderItem_id}" name="orderItem_id">
                            <label for="cbx_orderItem_select_${orderItem.productOrderItem_id}"></label>
                        </td>
                        <td>
                            <img class="orderItem_product_image" src="/tmall/res/images/item/productSinglePicture/${imageSrc}" style="width: 80px;height: 80px;"/>
                            <span class="orderItem_product_name">
                                <a href="/tmall/product/${product.product_id}">${product.product_name}</a>
                            </span>
                        </td>
                        <td><span class="orderItem_product_price">￥${unitPrice}</span></td>
                        <td>
                            <div class="item_amount">
                                <a href="javascript:void(0)" onclick="up(this)" class="J_Minus ${orderItem.productOrderItem_number <= 1 ? 'no_minus' : ''}">-</a>
                                <input type="text" value="${orderItem.productOrderItem_number}"/>
                                <a href="javascript:void(0)" onclick="down(this)" class="J_Plus">+</a>
                            </div>
                        </td>
                        <td><span class="orderItem_product_realPrice">￥${orderItem.productOrderItem_price.toFixed(2)}</span></td>
                        <td><a href="javascript:void(0)" onclick="removeItem('${orderItem.productOrderItem_id}')" class="remove_order">删除</a></td>
                        <td><input type="hidden" class="input_orderItem" name="${orderItem.productOrderItem_id}"/></td>
                    </tr>
                `;
            });
        }

        html += `
                </tbody>
            </table>
            <div id="J_FloatBar">
                <div id="J_SelectAll2">
                    <div class="cart_checkbox">
                        <input class="J_checkboxShop" id="J_SelectAllCbx2" type="checkbox" value="true"/>
                        <label for="J_SelectAllCbx2" title="勾选购物车内所有商品"></label>
                    </div>
                    <span class="span_selectAll">&nbsp;全选</span>
                </div>
                <div class="operations">
                    <a href="javascript:void(0)" onclick="remove()">删除</a>
                </div>
                <div class="float-bar-right">
                    <div id="J_ShowSelectedItems">
                        <span class="txt">已选商品</span>
                        <em id="J_SelectedItemsCount">0</em>
                        <span class="txt">件</span>
                    </div>
                    <div class="price_sum">
                        <span class="txt">合计（不含运费）:</span>
                        <strong class="price">
                            <em id="J_Total">
                                <span class="total_symbol">&nbsp;  ￥</span>
                                <span class="total_value"> 0.00</span>
                            </em>
                        </strong>
                    </div>
                    <div class="btn_area">
                        <a href="javascript:void(0)" id="J_Go" onclick="create(this)">
                            <span>结&nbsp;算</span>
                        </a>
                    </div>
                </div>
            </div>
        `;

        return html;
    },

    // 绑定购物车事件
    bindCartEvents: function() {
        // 重新绑定原有的事件处理器
        $(".tbody_checkbox>.cbx_select").off('click').on('click', function () {
            const obj = $(this).parents("tr.orderItem_info").toggleClass("orderItem_selected");
            sumPrice();
        });

        $("#cbx_select_all,#J_SelectAllCbx2").off('click').on('click', function () {
            const yn = $(this).prop("checked");
            const obj = $("tr.orderItem_info");
            if (!yn) {
                $(".tbody_checkbox>.cbx_select").prop("checked", false);
                sumPrice();
                obj.removeClass("orderItem_selected");
                $("#J_Go").removeClass("selected");
            } else {
                $(".tbody_checkbox>.cbx_select").prop("checked", true);
                sumPrice();
                obj.addClass("orderItem_selected");
                $("#J_Go").addClass("selected");
            }
        });

        $(".item_amount>input[type=text]").off('input propertychange').on('input propertychange', function () {
            let number = $(this).val();
            if (isNaN(number) || $.trim(number) === "") {
                $(this).val(1);
                $(this).prev("a").addClass("no_minus");
                return;
            }
            if (parseInt(number) >= 500) {
                $(this).val(500);
                number = 500;
                $(this).next("a").addClass("no_minus");
            } else if (parseInt(number) > 1 && parseInt(number) < 500) {
                $(this).prev("a").removeClass("no_minus");
                $(this).next("a").removeClass("no_minus");
            } else if (parseInt(number) <= 1) {
                $(this).val(1);
                number = 1;
                $(this).prev("a").addClass("no_minus");
            }
            let price = $(this).parents("tr").find(".orderItem_product_price").text();
            price = parseFloat(price.substring(1));
            const price_sum = parseFloat(price * parseInt(number));
            $(this).parents("tr").find(".orderItem_product_realPrice").text("￥" + price_sum.toFixed(2));
            sumPrice();
        });
    },

    // 绑定其他事件
    bindEvents: function() {
        // 这里可以添加其他需要绑定的事件
    },

    // 删除单个商品
    removeItem: function(orderItemId) {
        const self = this;

        if (confirm('确定要删除这个商品吗？')) {
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');

            $.ajax({
                url: '/tmall/orderItem/' + orderItemId,
                type: 'DELETE',
                headers: {
                    'Authorization': 'Bearer ' + token
                },
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        // 重新加载购物车数据
                        self.loadCartData();
                    } else {
                        alert('删除商品失败，请稍后重试！');
                    }
                },
                error: function(xhr) {
                    if (xhr.status === 401) {
                        // 用户未登录或token过期
                        localStorage.removeItem('token');
                        sessionStorage.removeItem('token');
                        window.location.href = '/tmall/login';
                    } else {
                        alert('删除商品失败，请稍后重试！');
                    }
                }
            });
        }
    },

    // 批量删除选中商品
    removeSelectedItems: function() {
        const self = this;
        const selectedItems = [];

        $("input.cbx_select:checked").each(function() {
            const orderItemId = $(this).parents("tr.orderItem_info").find(".input_orderItem").attr("name");
            if (orderItemId) {
                selectedItems.push(orderItemId);
            }
        });

        if (selectedItems.length === 0) {
            alert('请选择要删除的商品！');
            return;
        }

        if (confirm(`确定要删除选中的 ${selectedItems.length} 个商品吗？`)) {
            let deleteCount = 0;
            let errorCount = 0;
            const token = localStorage.getItem('token') || sessionStorage.getItem('token');

            selectedItems.forEach(function(orderItemId) {
                $.ajax({
                    url: '/tmall/orderItem/' + orderItemId,
                    type: 'DELETE',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    },
                    dataType: 'json',
                    success: function(data) {
                        deleteCount++;
                        if (deleteCount + errorCount === selectedItems.length) {
                            if (errorCount > 0) {
                                alert(`删除完成，但有 ${errorCount} 个商品删除失败`);
                            }
                            // 重新加载购物车数据
                            self.loadCartData();
                        }
                    },
                    error: function(xhr) {
                        errorCount++;
                        if (xhr.status === 401) {
                            // 用户未登录或token过期
                            localStorage.removeItem('token');
                            sessionStorage.removeItem('token');
                            window.location.href = '/tmall/login';
                            return;
                        }
                        if (deleteCount + errorCount === selectedItems.length) {
                            alert(`删除完成，但有 ${errorCount} 个商品删除失败`);
                            // 重新加载购物车数据
                            self.loadCartData();
                        }
                    }
                });
            });
        }
    }
};

// 全局函数，供HTML中的onclick调用
function removeItem(orderItemId) {
    CartPage.removeItem(orderItemId);
}

function remove() {
    CartPage.removeSelectedItems();
}
