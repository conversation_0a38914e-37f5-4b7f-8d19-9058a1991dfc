package com.xq.tmall.service;

import com.xq.tmall.dto.ProductListDTO;
import com.xq.tmall.entity.Product;
import com.xq.tmall.util.PageUtil;

import java.util.List;

/**
 * 商品列表服务接口
 * 专门处理商品列表页面相关的业务逻辑
 */
public interface ProductListService {
    
    /**
     * 根据分类ID获取商品列表
     * 
     * @param categoryId 分类ID
     * @param pageUtil 分页参数
     * @param orderBy 排序字段
     * @param isDesc 是否降序
     * @return 商品列表DTO
     */
    ProductListDTO getProductsByCategory(Integer categoryId, PageUtil pageUtil, String orderBy, Boolean isDesc);
    
    /**
     * 根据商品名称搜索商品列表
     * 
     * @param productName 商品名称
     * @param pageUtil 分页参数
     * @param orderBy 排序字段
     * @param isDesc 是否降序
     * @return 商品列表DTO
     */
    ProductListDTO searchProductsByName(String productName, PageUtil pageUtil, String orderBy, Boolean isDesc);
    
    /**
     * 获取商品列表（通用方法）
     * 
     * @param categoryId 分类ID（可选）
     * @param productName 商品名称（可选）
     * @param pageUtil 分页参数
     * @param orderBy 排序字段
     * @param isDesc 是否降序
     * @return 商品列表DTO
     */
    ProductListDTO getProductList(Integer categoryId, String productName, PageUtil pageUtil, String orderBy, Boolean isDesc);
    
    /**
     * 设置商品列表的图片信息
     * 
     * @param productList 商品列表
     */
    void setProductImages(List<Product> productList);
    
    /**
     * 设置商品列表的销量和评论数
     * 
     * @param productList 商品列表
     */
    void setProductStats(List<Product> productList);
    
    /**
     * 验证搜索参数
     * 
     * @param categoryId 分类ID
     * @param productName 商品名称
     * @return 是否有效
     */
    boolean isValidSearchParams(Integer categoryId, String productName);
}
