
tmall:
  swagger-open: true                #是否开启swagger (true/false)
  kaptcha-open: true               #是否开启登录时验证码 (true/false)
  spring-session-open: true        #是否开启spring session,如果是多机环境需要开启(true/false)
  session-invalidate-time: 1800     #session失效时间(只在单机环境下生效，多机环境在SpringSessionConfig类中配置) 单位：秒
  session-validation-interval: 900  #多久检测一次失效的session(只在单机环境下生效) 单位：秒
  muti-datasource-open: false  #开启多数据源
  file-upload-path: E:/tmall #上传目录

beetl:
  resource-auto-check: true         #热加载beetl模板，开发时候用

spring:
  profiles:
    active: dev
  mvc:
    view:
      prefix: /WEB-INF/page/
      suffix: .jsp
      static-path-pattern: /res/*
  devtools:
    restart:
      enabled: false
      additional-paths: src/main/java
      additional-exclude: /res/*
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB

  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 6000ms  # 连接超时时长（毫秒）
    expire-time: 1800 # redis过期时间是30分钟30*60
    jedis:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

#MyBatis
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xq.tmall.entity;
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    id-type: 0
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: false
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    capital-mode: true
    #序列接口实现类配置
    #key-generator: com.baomidou.springboot.xxx
    #逻辑删除配置
    logic-delete-value: 0
    logic-not-delete-value: 1
    #自定义填充策略接口实现
    #meta-object-handler: com.xq.tmall.config.mybatis.MyMetaObjectHandler
    #自定义SQL注入器
    #sql-injector: com.baomidou.springboot.xxx
  configuration:
    map-underscore-to-camel-case: false # 开启驼峰命名转换法
    cache-enabled: true
    #Mybatis返回null值不显示
    call-setters-on-nulls: true
#logging
logging:
  level:
    com.xq.tmall: DEBUG
  #file: ./logs/xqdjzwwexin-log.log

#短信平台配置
sms:
  open: true
  sms-type:
  sign-name:
  api-id:
  api-key:
  url:
