package com.xq.tmall.service;

import com.xq.tmall.entity.Product;
import com.xq.tmall.entity.Property;
import com.xq.tmall.dto.ProductDetailsDTO;

import java.util.List;

/**
 * 商品详情服务接口
 * 专门处理商品详情页面相关的业务逻辑
 */
public interface ProductDetailsService {
    
    /**
     * 获取完整的商品详情信息
     * 
     * @param productId 商品ID
     * @return 商品详情DTO
     */
    ProductDetailsDTO getProductDetails(Integer productId);
    
    /**
     * 获取商品的基本信息
     * 
     * @param productId 商品ID
     * @return 商品信息
     */
    Product getProductBasicInfo(Integer productId);
    
    /**
     * 获取商品的属性列表
     * 
     * @param product 商品对象
     * @return 属性列表
     */
    List<Property> getProductProperties(Product product);
    
    /**
     * 获取猜你喜欢的商品列表
     * 
     * @param categoryId 分类ID
     * @param excludeProductId 排除的商品ID
     * @param count 获取数量
     * @return 商品列表
     */
    List<Product> getRecommendedProducts(Integer categoryId, Integer excludeProductId, Integer count);
    
    /**
     * 获取随机推荐商品
     * 
     * @param categoryId 分类ID
     * @param currentGuessNumber 当前随机数
     * @param count 获取数量
     * @return 商品列表和新的随机数
     */
    ProductDetailsDTO.GuessProductResult getRandomRecommendedProducts(Integer categoryId, Integer currentGuessNumber, Integer count);
    
    /**
     * 验证商品是否可用
     * 
     * @param product 商品对象
     * @return 是否可用
     */
    boolean isProductAvailable(Product product);
}
